{"name": "@ekuaibao/entry-applet", "version": "9.137.19", "serverPath": "applet", "description": "whispered-applet", "author": "ekuaibao@team", "license": "No LICENSE", "private": true, "sideEffects": true, "devDependencies": {"@babel/runtime": "7.21.5", "@ekuaibao/fetch": "0.7.1", "@ekuaibao/mobx-remotedev": "0.5.0", "@ekuaibao/webpack-retry-chunk-load-plugin": "1.5.4", "@ekuaibao/whispered": "6.2.7", "@types/big.js": "4.0.5", "@types/classnames": "2.3.1", "@types/crypto-js": "4.1.2", "@types/jest": "24.9.1", "@types/lodash": "4.14.194", "@types/react": "16.14.40", "@types/react-dom": "16.9.18", "@types/react-highlight-words": "0.16.4", "@vue/preload-webpack-plugin": "^2.0.0", "babel-eslint": "9.0.0", "babel-jest": "23.6.0", "cross-env": "5.2.1", "crypto": "^1.0.1", "duplicate-package-checker-webpack-plugin": "^3.0.0", "eslint-config-prettier": "3.6.0", "eslint-plugin-prettier": "2.7.0", "html-replace-webpack-plugin": "^2.6.0", "husky": "^9.1.6", "jest": "24.9.0", "jsdom": "26.0.0", "line-reader": "0.4.0", "npm-run-all": "^4.1.5", "prettier": "1.19.1", "script-ext-html-webpack-plugin": "^2.1.5", "ts-jest": "24.3.0", "tslint": "5.20.1", "tslint-config-prettier": "1.18.0", "upload-huawei-cli": "1.0.0", "webpack-bundle-analyzer": "^4.10.2", "whispered-build": "3.3.12", "xxhashjs": "^0.2.2"}, "resolutions": {"babel-core": "^7.0.0-bridge.0", "fsevents": "", "core-js": "3.41.0"}, "dependencies": {"@antv/data-set": "0.11.8", "@antv/f2": "3.8.13", "@ekuaibao/3rd_resources": "1.0.0-beta.28", "@ekuaibao/async-component": "1.0.9", "@ekuaibao/auth": "1.3.11", "@ekuaibao/charts": "1.0.0-beta.0", "@ekuaibao/ekuaibao_404": "0.0.2", "@ekuaibao/ekuaibao_types": "1.0.71", "@ekuaibao/enhance-layer-manager-mobile": "5.4.4-beta.1", "@ekuaibao/enhance-stacker-manager": "3.1.4", "@ekuaibao/eui-isomorphic": "1.2.9", "@ekuaibao/eui-mobile": "0.3.1", "@ekuaibao/eui-styles": "3.0.0-beta.9", "@ekuaibao/event-track": "2.0.12", "@ekuaibao/helpers": "1.1.10", "@ekuaibao/i18n": "3.1.6", "@ekuaibao/image-player": "5.19.9", "@ekuaibao/lib": "2.15.17", "@ekuaibao/loading": "4.0.1", "@ekuaibao/markup-image": "0.0.4", "@ekuaibao/mcalendar": "2.0.26-beta.2", "@ekuaibao/messagecenter": "5.1.0", "@ekuaibao/mobx-store": "1.1.11", "@ekuaibao/money-math": "4.1.7", "@ekuaibao/painter": "0.0.76-beta.9", "@ekuaibao/platform.is": "1.0.24", "@ekuaibao/popup-mobile": "2.0.0", "@ekuaibao/rpc": "2.0.3", "@ekuaibao/sdk-bridge": "1.17.7", "@ekuaibao/session-info": "1.0.2", "@ekuaibao/signature": "0.0.10", "@ekuaibao/springboard": "2.1.10", "@ekuaibao/store": "1.0.10", "@ekuaibao/template": "5.0.10", "@ekuaibao/theme-variables": "1.3.16", "@ekuaibao/uploader": "3.5.3", "@ekuaibao/vendor-antd-mobile": "2.4.0", "@ekuaibao/vendor-common": "1.4.3-beta.1", "@ekuaibao/vendor-lodash": "4.17.13", "@ekuaibao/vendor-polyfill": "1.0.3", "@ekuaibao/vendor-whispered": "4.6.8-release.0", "@featbit/js-client-sdk": "3.0.12", "@hose/control": "1.1.11", "@hose/eui-icons": "3.0.91", "@hose/eui-mobile": "3.9.6", "@hose/eui-theme": "3.0.7", "@hose/numeral": "0.0.15", "@hose/pro-eui-mobile-components": "0.1.22", "@mikecousins/react-pdf": "5.5.1", "@sentry/browser": "5.30.0", "@sentry/cli": "1.75.0", "@sentry/integrations": "5.30.0", "@sentry/react": "5.30.0", "@sentry/tracing": "5.30.0", "@sentry/webpack-plugin": "1.20.0", "antd-mobile": "2.3.4", "antd-mobile-icons": "0.3.0", "big.js": "5.2.2", "blueimp-canvas-to-blob": "3.29.0", "classnames": "2.3.2", "clipboard": "2.0.11", "colors": "1.4.0", "compressorjs": "1.2.1", "core-js": "3.6.5", "crypto-js": "4.1.1", "ekbc-fastclick": "1.0.5", "ekbc-pro-ui": "3.0.4", "ekbc-query-builder": "2.0.7", "ekbc-react-d3-charts": "2.2.4", "ekbc-reports": "3.3.7", "ekbc-thirdParty-card": "3.0.4", "image-promise": "5.0.1", "immer": "9.0.21", "js-audio-recorder": "1.0.7", "js-cookie": "3.0.1", "js-message": "1.0.7", "jsencrypt": "3.3.2", "lodash": "4.17.21", "mobx": "4.15.7", "mobx-react-lite": "2.2.2", "moment": "2.29.4", "nanoid": "5.0.4", "prop-types": "15.8.1", "pseudo-worker": "1.3.0", "qrcode.react": "0.8.0", "qs": "6.11.1", "rc-form": "1.5.0", "react": "16.13.1", "react-addons-create-fragment": "15.6.2", "react-copy-to-clipboard": "5.1.0", "react-dom": "16.13.1", "react-highlight-words": "0.14.0", "react-lottie": "1.2.3", "react-markdown": "7.1.2", "react-sortable-hoc": "1.11.0", "react-virtualized": "9.22.5", "regenerator-runtime": "0.13.11", "remark-gfm": "2.0.0", "rimraf": "3.0.2", "semver": "7.6.3", "simple-i18n-cli": "1.1.1", "systemjs": "6.14.1", "use-immer": "0.7.0", "victory": "36.6.8", "whatwg-fetch": "2.0.4"}, "scripts": {"start": "npm run dev", "update:_plugins": "node node_modules/whispered-build/shells/createEntryPlugins.js", "build:pre:dev": "rimraf .dist && node --max_old_space_size=10240 scripts/builds-dev", "build:pre:pro": "rimraf .dist && node --max_old_space_size=10240 scripts/builds-pro", "dev": "npm run update:_plugins && npm run dev:server", "dev:server": "webpack-dev-server", "dev_loc_hw": "npm run update:_plugins && HSFK=1 HWLC=1 npm run dev", "build": "npm run clean && npm-run-all --parallel build:origin build:bill", "build:origin": "NODE_ENV=production && npm run update:_plugins && npm run build:source", "build:source": "NODE_ENV=production webpack --config ./webpack.build.js --hash --compress --progress", "clean": "rimraf ./build", "emit:local:config": "cp ./configs/webpack.local.js webpack.local.js", "fix-memory-limit": "cross-env LIMIT=20240 increase-memory-limit", "build:huawei": "rm -rf ./build && webpack --config webpack.build.huawei.js --hash --compress --progress", "clear": "rm -rf ./packge-lock.json  && rm -rf ./node_modules && rm -rf ./dll && npm i & npm run dev", "lint:es": "eslint --ext .jsx,.js src", "lint:ts": "tslint -p tsconfig.json", "test:watch": "jest -o --watch", "test": "jest --coverage", "patch:i18n": "find ./src/i18n/locales -type f -name '*.all.json' | sed 's/.all.json//' | xargs -I % mv %.all.json %.json", "build:i18n": "i18n-cli wrap --dry-run false './src/**/*' && i18n-cli parse --output-dir ./src/i18n/locales --locales en-US,zh-CN './src/**/*' && npm run patch:i18n", "build:analyze": "rimraf build && npm run clean && npm run update:_plugins && NODE_ENV=production  NODE_OPTIONS='--max_old_space_size=10240' webpack --mode production --hash --compress --progress --config configs/webpack.build.analyze.js", "dev:bill": "webpack-dev-server --config configs/bill/webpack.config.dev.js", "build:bill": "NODE_ENV=production  NODE_OPTIONS='--max_old_space_size=10240' webpack --mode production --hash --compress --progress --config configs/bill/webpack.config.prod.js", "build:bill:local": "npm run clean && NODE_ENV=development  NODE_OPTIONS='--max_old_space_size=10240' webpack --mode production --hash --compress --progress --config configs/bill/webpack.config.prod.js", "build:bill:analyze": "npm run clean && ANALYZE=1 NODE_ENV=production  NODE_OPTIONS='--max_old_space_size=10240' webpack --mode production --hash --compress --progress --config configs/bill/webpack.config.prod.js", "analyze:iconfont": "node configs/bill/scripts/analyze-iconfont.js"}, "repository": {"type": "git", "url": "********************:whispered/applet.git"}, "whispered": {"remote": "********************:torpedo/whispered-built-assets.git", "plugins": {}, "entryPlugins": {"app": ["layout-app", "enterprise5", "account5", "message-center", "new-feature"], "group": ["layout-app", "enterprise5", "account5", "message-center", "new-feature"], "ldap": ["layout-app", "enterprise5", "account5", "message-center", "new-feature"], "bluemoon": ["layout-bluemoon", "enterprise5", "new-feature"], "cocall": ["layout-cocall", "new-feature"], "debugger": ["layout-debugger", "new-feature"], "dingtalk": ["layout-din<PERSON><PERSON>", "new-feature", "message-center"], "emobile": ["layout-thirdparty", "new-feature"], "feishu": ["layout-feishu", "new-feature"], "huawei": ["layout-h<PERSON><PERSON>", "new-feature"], "kdcloud": ["layout-kdcloud", "new-feature-kdcloud"], "thirdparty": ["layout-thirdparty", "new-feature"], "wechat": ["layout-weixin", "new-feature"], "weixin": ["layout-weixin", "new-feature", "message-center"], "qywxhybrid": ["layout-qywxhybrid", "enterprise5", "account5", "message-center", "new-feature"], "nbbank": ["layout-nbbank", "new-feature"], "dtapphybrid": ["layout-din<PERSON><PERSON>", "new-feature"], "hybrid": ["layout-hybrid", "new-feature"], "fesco": ["layout-fesco", "enterprise5", "account5", "message-center", "new-feature"]}, "ignorePlugins": ["experience", "layout-basic", "messageRedirect", "report-adv"]}}