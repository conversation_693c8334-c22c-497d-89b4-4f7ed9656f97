import { Resource } from '@ekuaibao/fetch'
import Money from '../../elements/puppet/Money'
const datalinkResource = new Resource('/api/v1/datalink')
import { getBoolVariation } from '../../lib/featbit'

export const getBudgetAdjustDetails = param => {
  if (getBoolVariation('use-post-fetch-budgets-adjust-detail')) {
    return datalinkResource.POST('/byDataLinkId/batch', param?.ids)
  } else {
    return datalinkResource.GET('/byDataLinkId/[ids]', param)
  }
}

export const getBudgetAdjustColumns = entityId => {
  return [
    {
      title: i18n.get('预算名称'),
      dataIndex: `form.E_${entityId}_name`,
      key: `form.E_${entityId}_name`,
      width: 140,
      render: value => {
        return value ? (
          <div className="text-ellipsis" style={{ width: 120 }}>
            {value}
          </div>
        ) : (
          '-'
        )
      }
    },
    {
      title: i18n.get('预算周期'),
      dataIndex: `form.E_${entityId}_periodTimeName`,
      key: `form.E_${entityId}_periodTimeName`,
      width: 120
    },
    {
      title: i18n.get('调整前金额'),
      dataIndex: `form.E_${entityId}_beforeBudgetMoney`,
      key: `form.E_${entityId}_beforeBudgetMoney`,
      width: 120,
      render(value) {
        return value?.standard ? <Money value={value} /> : '-'
      }
    },
    {
      title: i18n.get('调整后金额'),
      width: 120,
      dataIndex: `form.E_${entityId}_afterBudgetMoney`,
      key: `form.E_${entityId}_afterBudgetMoney`,
      render(value) {
        return value?.standard ? <Money value={value} /> : '-'
      }
    },
    {
      title: i18n.get('差额'),
      width: 120,
      dataIndex: `form.E_${entityId}_difference`,
      key: `form.E_${entityId}_difference`,
      render(value) {
        return value?.standard ? <Money value={value} /> : '-'
      }
    },
    {
      title: i18n.get('预算余额'),
      width: 120,
      dataIndex: `form.E_${entityId}_balance`,
      key: `form.E_${entityId}_balance`,
      render(value) {
        return value?.standard ? <Money value={value} /> : '-'
      }
    }
  ]
}

export function filterBudgetAdjustTemplate(data: [], entityId: string) {
  if (!data) {
    return []
  }
  const blacklist = [
    `E_${entityId}_parentId`,
    `E_${entityId}_budgetId`,
    `E_${entityId}_nodeId`,
    `E_${entityId}_batchNo`,
    `E_${entityId}_code`
  ]
  return data.filter((item: any) => {
    item.components = item.components.filter((component: any) => {
      return !blacklist.includes(component.field)
    })
    return item
  })
}
