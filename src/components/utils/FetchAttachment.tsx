import React, { PureComponent } from 'react'
import { isArray, isObject } from '@ekuaibao/helpers'
import { Fetch, Resource} from '@ekuaibao/fetch'
import { parseAsShowValue, parseValue } from './fnAttachment'
import { getV } from '../../lib/help'

const aiResult = new Resource('/api/extend/ai')

interface ElemTypeIF {
  type: string
  entity: string
}

interface DataTypeIF {
  type: string
  elemType: ElemTypeIF
}

interface FieldIF {
  dataType: DataTypeIF
}

interface IProps {
  value: AttachmentIF[]
  onChange: (value: string | any[]) => void
  field: FieldIF
}

export interface AttachmentIF {
  fileId: string
  fileName: string
  key: string
  url?: string
}

export interface AttachmentResponseIF {
  id: string
  key: string
  name: string
  platform: string
  regionUrl: string
  thumbUrl: string
  url: string
}

export function FetchAttachment<P extends IProps>(WrappedComponent: React.ComponentType<P>) {
  return class AttachmentWrapper extends PureComponent<P> {
    async componentWillReceiveProps(nextProps: P) {
      if (nextProps.value !== this.props.value && needFetchData(nextProps)) {
        const { value, onChange } = nextProps
        const values = await fetchAttachment(value)
        onChange?.(values)
      }
    }

    async componentDidMount() {
      const { value, onChange } = this.props
      if (needFetchData(this.props)) {
        const values = await fetchAttachment(value)
        onChange?.(values)
      }
    }

    preGetValue() {
      const { value } = this.props
      return (value || []).map(parseValue)
    }

    preSetValue() {
      const { value } = this.props
      return (value || []).map(parseAsShowValue)
    }

    render() {
      return <WrappedComponent {...this.props} />
    }
  }
}

export function needFetchData(props: IProps): boolean {
  const { field, value } = props
  if (!value || !field) {
    return false
  }
  if (field.dataType.type !== 'list' && field.dataType.elemType.type !== 'attachment') {
    return false
  }
  if (isArray(value)) {
    return !!value
      .filter(line => !line.key.startsWith('DP:'))
      .find(line => line && line.fileId && !isObject(line.fileId))
  }
  return false
}

export async function fetchAttachment(values: AttachmentIF[]) {
  if (!values?.length) return values
  const { fileIds, fileMap } = getFileIds(values)
  if (fileIds.length) {
    const res = await Fetch.POST(`/api/v1/attachment/attachments/ids`, null, { body: { ids: fileIds } }, {
      hiddenLoading: true
    } as any)
    return values.map(line => {
      const id = getV(line, 'fileId.id', line.fileId)
      const item = res?.items?.find((v: AttachmentResponseIF) => v.id === id)
      return item ? { ...fileMap[id], fileId: item } : line
    })
  }
  return values
}

const attachmentMap: { [key: string]: AttachmentIF } = {}
export const fetchAttachmentWithCache = async (value: AttachmentIF[]) => {
  const { fileIds, fileMap } = getFileIds(value)
  const toFetchItems = fileIds.filter(id => !attachmentMap[id]).map(id => fileMap[id])
  const items = await fetchAttachment(toFetchItems)
  items?.forEach(item => {
    const id = getV(item, 'fileId.id', item.fileId)
    attachmentMap[id] = item
  })
  return value.map(line => {
    const id = getV(line, 'fileId.id', line.fileId)
    return attachmentMap[id] || line
  })
}

function getFileIds(values: AttachmentIF[]) {
  const fileIds: string[] = []
  const fileMap: { [key: string]: AttachmentIF } = {}
  values.forEach(line => {
    if (!line.key.startsWith('DP:')) {
      const fileId = getV(line, 'fileId.id', line.fileId)
      if (fileId && typeof fileId === 'string') {
        fileIds.push(fileId)
        fileMap[fileId] = line
      }
    }
  })
  return { fileIds, fileMap }
}

/**
 * 过滤AI结果中的空值
 * 递归处理对象和数组，移除空值属性
 * @param data 要过滤的数据
 * @returns 过滤后的数据
 */
export const filterEmptyValues = (data: any): any => {
  // 处理 null、undefined、空字符串
  if (data === null || data === undefined || data === '') {
    return undefined;
  }

  // 处理空对象 {}
  if (typeof data === 'object' && !Array.isArray(data) && Object.keys(data).length === 0) {
    return undefined;
  }

  // 处理空数组 []
  if (Array.isArray(data) && data.length === 0) {
    return undefined;
  }

  // 处理数组
  if (Array.isArray(data)) {
    const filteredArray = data
      .map(item => filterEmptyValues(item))
      .filter(item => item !== undefined);
    
    return filteredArray.length > 0 ? filteredArray : undefined;
  }

  // 处理对象
  if (typeof data === 'object' && data !== null) {
    const filteredObject: any = {};
    let hasValidProperties = false;

    for (const [key, value] of Object.entries(data)) {
      const filteredValue = filterEmptyValues(value);
      if (filteredValue !== undefined) {
        filteredObject[key] = filteredValue;
        hasValidProperties = true;
      }
    }

    return hasValidProperties ? filteredObject : undefined;
  }

  // 其他类型直接返回
  return data;
};

export const fetchAttachmentAIResult = async (
  attachmentKey: string, 
  fields: { label: string, type: string, isArray?: boolean }[], 
  datalinkFieldMap: { [key: string]: { label: string, type: string, isArray?: boolean }[] },
  specificationId: string
) => {
  // 首先调用原始接口获取 ID
  const initialResponse = await aiResult.POST(`/flow/autoFill`, {
    key: attachmentKey,
    fields,
    datalinkFieldMap,
    specificationId
  })
  
  const { id } = initialResponse
  if (!id) {
    throw new Error('AI 接口返回的 ID 为空')
  }
  
  // 轮询获取结果，最多轮询3分钟（300秒）
  const maxDuration = 3 * 60 * 1000 // 3分钟
  const pollInterval = 6000 // 6秒轮询一次
  const startTime = Date.now()
  
  return new Promise((resolve, reject) => {
    const poll = async () => {
      try {
        // 检查是否超时
        if (Date.now() - startTime > maxDuration) {
          reject({})
          return
        }
        
        // 请求结果接口
        const result = await aiResult.GET(`/flow/getResult/$${id}`, {}, null, { hiddenLoading: true })

        // 如果有结果则返回
        if (result?.code === 200) {
          // 过滤空值
          // const filteredItems = filterEmptyValues(result.items);
          // resolve({ value: filteredItems })
          resolve({ value: result?.data ?? {} })
          return
        }
        
        // 如果状态是失败，则直接返回错误
        if (result?.code === 500) {
          reject({})
          return
        }
        
        if (result.code === 300) {
          // 处理中，继续轮询
          setTimeout(poll, pollInterval)
        }
      } catch (error) {
        // 如果是网络错误或其他错误，继续轮询
        if (Date.now() - startTime <= maxDuration) {
          setTimeout(poll, pollInterval)
        } else {
          reject({})
        }
      }
    }
    
    // 开始轮询
    poll()
  })
}

export default FetchAttachment