import { app as api } from '@ekuaibao/whispered'
import { parseAsSaveValue } from './fnAttachment'
import { getNodeValueByPath } from '../../lib/util'
import { invoiceOptions } from '../../plugins/invoice-form/utils/config'
import { isObject } from '@ekuaibao/helpers'

/**
 * 校验级别
 * @param rule.level 0 轻松模式
 * @param rule.level 1 严格模式
 */
export function required(field, props) {
  return (rule, value, callback) => {
    const { type, labelCopy, optional, editable, maxLength, minLength } = field
    if (value && typeof value === 'string') {
      value = value.trim()
    }
    //money类型比较特殊，保存也需要必填
    // if (type === 'money') {
    //   if (optional && (!value || !value.standard)) {
    //     return callback()
    //   }
    //   if (value === null || value === undefined) {
    //     return callback(i18n.get('{__k0}不可为空', { __k0: labelCopy }))
    //   }
    // }

    if (rule.level === 0) {
      return callback()
    }

    if (optional) {
      return callback()
    }

    if (type === 'city' && value === '[]') {
      callback(i18n.get('{__k0}不可为空', { __k0: labelCopy }))
    }

    if (rule.level === 1) {
      if (
        value === null ||
        value === undefined ||
        (typeof value === 'string' && value.length === 0) ||
        (Array.isArray(value) && value.length === 0) ||
        value.start === 0 ||
        value.end === 0
      ) {
        if(type === 'details') {
          return callback(i18n.get('{__k0}为必填项', { __k0: labelCopy }))
        }
        return callback(i18n.get('{__k0}不可为空', { __k0: labelCopy }))
      } else if (value?.allMatchList && !value.id) {
        return callback(i18n.get('{__k0}不可为空', { __k0: labelCopy }))
      } else if (value) {
        //不可编辑时 走初始的 最大值最小值
        if (editable && value.length > maxLength) {
          if (type === 'text' || type === 'textarea') {
            return callback(i18n.get('validate-max-length', { __k0: labelCopy, __k1: maxLength }))
          }
          return callback(i18n.get(`{__k0}长度不能超过{__k1}`, { __k0: labelCopy, __k1: maxLength }))
        } else if (editable && value.length < minLength) {
          if (type === 'text' || type === 'textarea') {
            return callback(i18n.get('validate-min-length', { __k0: labelCopy, __k1: minLength }))
          }
        } else if (!editable && value.length > 1000) {
          return callback(i18n.get(`{__k0}长度不能超过{__k1}`, { __k0: labelCopy, __k1: 1000 }))
        }
      }
    }

    callback()
  }
}

/**
 * 检验文本最小长度
 * 检验文本最大长度
 * @param {*} field
 * @param {*} props
 * @returns
 */
export function validateTextLength(field, props) {
  return (rule, value, callback) => {
    const { labelCopy, editable, minLength, maxLength, optional } = field
    if (rule.level === 0 && !value) {
      return callback()
    }
    if (rule.level === 1 && value && !optional) { // 必填已经在required方法里面校验过了，如果是选填的，填写了值就需要校验
      return callback()
    }
    if (value && editable) {
      if (value.length < minLength) {
        return callback(i18n.get('validate-min-length', { __k0: labelCopy, __k1: minLength }))
      }
      if (value.length > maxLength) {
        return callback(i18n.get('validate-max-length', { __k0: labelCopy, __k1: maxLength }))
      }
    }
    callback()
  }
}

export function tripDataLinkSceneValidator(field, props) {
  return async (rule, value, callback) => {
    if (rule.level === 0) {
      return callback()
    }
    if (rule.level === 1) {
      const sceneRequired = await api.invoke('checkSceneRequired')
      if (sceneRequired === 'invalid') {
        const sceneRequiredEnsured = await api.invoke('checkSceneRequired')
        if (sceneRequiredEnsured === 'invalid') {
          return callback(' ')
        }
      } else {
        return callback()
      }
      callback()
    }
  }
}

export function numberMinMax(field, props) {
  return (rule, value, callback) => {
    if ((rule.level === 0 || field.optional) && !value) {
      return callback()
    }
    let error = maxMinValidator(field, value)
    if (!error) {
      callback()
    }
    callback(error)
  }
}

export function dataLinkEditValidator(field, props) {
  return (rule, value, callback) => {
    const { importMode, fieldBus, behaviour, showType } = field
    //无论选填还是必填只要填写了一项都会校验，必填的时候没填写也得校验
    if (behaviour === 'REF') {
      return callback()
    }
    if (
      importMode === 'SINGLE' &&
      behaviour !== 'UPDATE' &&
      value &&
      value.length > 0 &&
      (!field.optional || Object.values(value[0].dataLinkForm).find(o => o)) &&
      showType !== 'TABLE'
    ) {
      return fieldBus
        .getValueWithValidate(rule.level)
        .then(_ => {
          return callback()
        })
        .catch(_ => {
          return callback(i18n.get('信息填写不完整'))
        })
    } else {
      const dataLinkEntity = api.getState()['@bill']?.dataLinkEntity
      const hasEmptyData = []
      value?.forEach((it, index) => {
        const { dataLinkTemplateId, dataLinkForm } = it
        const currDataLinkEntity = dataLinkEntity.find(it => it.templateId === dataLinkTemplateId)
        if (currDataLinkEntity) {
          const components = currDataLinkEntity.components?.filter(i => i.type !== 'switcher')
          components.some(oo => {
            const { field, optional } = oo
            const val = dataLinkForm[field]
            if (!optional && (!val || val?.length === 0)) {
              hasEmptyData.push(index + 1)
              return true
            }
          })
        }
      })
      if (hasEmptyData.length > 0) {
        return callback(`第${hasEmptyData.join()}信息填写不完整,请填写`)
      }
      return callback()
    }
  }
}

export function tripDataLinkValidatorNew(field, props) {
  return (rule, value, callback) => {
    const { tripError, optional } = field
    if (tripError && !optional) {
      return callback(tripError)
    } else {
      return callback()
    }
  }
}

export function tripDataLinkValidator(field, props) {
  return (rule, value, callback) => {
    const { isDetail } = props //
    const { tripError } = field
    //无论选填还是必填只要填写了一项都会校验，必填的时候没填写也得校验
    if (isDetail && value && value.length > 0 && !field.optional && rule.level === 1) {
      if (tripError) {
        return callback(tripError)
      } else {
        return callback()
      }
    } else {
      return callback()
    }
  }
}

export function dateRangeValidator(field, props) {
  return (rule, value, callback) => {
    if ((rule.level === 0 || field.optional) && !value) {
      return callback()
    }
    if (!value) {
      return callback(i18n.get('请选择') + field.labelCopy)
    }
    let { start, end } = value
    if (start.valueOf() > end.valueOf()) {
      return callback(i18n.get('日期范围选择错误'))
    }
    return callback()
  }
}

export function repaymentValidator(field, props) {
  return (rule, value, callback) => {
    let { defaultValue } = field
    if (defaultValue && defaultValue.type === 'predefine' && defaultValue.value === 'repayment.date') {
      let loanDate = props.form.getFieldValue('loanDate')
      if (value && value < loanDate) {
        return callback(i18n.get('please-reselect'))
      }
    }
    return callback()
  }
}

export function scaleValidator(field) {
  return (rule, value, callback) => {
    if ((rule.level === 0 || field.optional) && !value) {
      return callback()
    }
    let { dataType, scale: scaleDataLink } = field
    let scale = scaleDataLink || (dataType ? dataType.scale : 0)
    let error = fnScaleValidator(scale, value)
    if (error) {
      return callback(error)
    }
    return callback()
  }
}

export function contractAmountValidator(field, props) {
  return (rule, value, callback) => {
    const { name, labelCopy } = field
    const { bus } = props
    if (rule.level === 0) {
      return callback()
    }
    if (field.type === 'money') {
      if (field.optional && (!value || !value.standard)) {
        return callback()
      }
      // 前面的 required 已经校验undefined
      if (!field.optional && value !== undefined && !value.standard) {
        return callback(i18n.get('{__k0}不可为空', { __k0: labelCopy }))
      }
    }
    if (value?.standard && name === 'contractAmount' && bus.$_settleAmountTotal) {
      if (Number(value?.standard || 0) < bus.$_settleAmountTotal) {
        return callback('合同金额不能小于结算总额')
      }
    }
    return callback()
  }
}

export function moneyValidator(field, props) {
  return (rule, value, callback) => {
    if (!field.editable && field?.defaultValue?.type === 'costStandard' && value?.costStandardError) {
      return callback(i18n.get('请联系管理员在币种设置中维护本位币与费标金额币种的汇率'))
    }

    if (rule.level === 0) {
      return callback()
    }

    if (field.type === 'money') {
      if (field.optional && (!value || !value.standard)) {
        return callback()
      }
    }

    if (field.defaultValue && field.defaultValue.type === 'invoiceSum' && (!value || !value.standard)) {
      return callback(i18n.get('该字段需根据导入的发票自动计算，请添加发票'))
    }



    if (rule.level === 0 && !value) {
      return callback()
    }

    const configs = getNodeValueByPath(props, 'billSpecification.configs', [])
    const chargeAgainst = configs.find(item => item.ability === 'chargeAgainst')
    if (rule.level === 1 && !value) {
      return callback()
    }

    if (field.isIgnore) {
      return callback()
    }

    let {
      rate,
      standard,
      standardScale,
      foreign,
      foreignScale,
      foreignStrCode,
      budget,
      budgetScale,
      budgetStrCode
    } = value

    if (field.name === 'receivingAmount') {
      if (!rate && foreign) {
        return callback(i18n.get('金额及汇率不能为空，请联系管理员维护企业汇率'))
      }
    }

    let standardScaleError = fnScaleValidator(standardScale, standard, i18n.get('本位币金额'))
    if (standardScaleError) {
      return callback(standardScaleError)
    }

    if (budgetStrCode) {
      let budgetScaleError = fnScaleValidator(budgetScale, budget, field.label)
      if (budgetScaleError) {
        return callback(budgetScaleError)
      }
      budgetScaleError = maxMinValidator(field, budget, i18n.get('预算币金额'))
      if (budgetScaleError) {
        return callback(budgetScaleError)
      }
    }

    if (foreignStrCode) {
      let foreignScaleError = fnScaleValidator(foreignScale, foreign, field.label)
      if (foreignScaleError) {
        return callback(foreignScaleError)
      }
      foreignScaleError = maxMinValidator(field, foreign, i18n.get('外币金额'), chargeAgainst)
      if (foreignScaleError) {
        return callback(foreignScaleError)
      }
    }

    let standardError = maxMinValidator(field, standard, i18n.get('本位币金额'), chargeAgainst)
    if (standardError) {
      return callback(standardError)
    }

    if (Number(rate) <= 0) {
      let rateError = i18n.get('汇率输入不合法')
      return callback(rateError)
    }

    return callback()
  }
}

export function allowModifyField(field, whiteList) {
  const { editable = true } = field
  return editable ? (whiteList ? !!~whiteList.indexOf(field.field) : editable) : editable
}

export function detailAllowModifyField(field, whiteList) {
  //TODO field.editable是否要判断
  return whiteList ? !!~whiteList.indexOf(field.field) : true
}

export function attachmentValidator(field, props) {
  return (rule, value, callback) => {
    const { labelCopy, optional } = field

    if (rule.level === 0 || optional) {
      return callback()
    }

    if (rule.level === 1) {
      if (value === null || value === undefined || (Array.isArray(value) && value.length === 0)) {
        return callback(i18n.get('{__k0}不可为空', { __k0: labelCopy }))
      }
      const vv = value && value.map(parseAsSaveValue)
      let errorMsg = ''
      vv.forEach(v => {
        const { fileName, key, fileId } = v
        if (!fileName || !key || !fileId) {
          errorMsg = i18n.get('{__k0}不可为空', { __k0: labelCopy })
          return errorMsg
        }
      })
      return errorMsg.length ? callback(errorMsg) : callback()
    }

    return callback()
  }
}

function maxMinValidator(field, value, label = '', chargeAgainst) {
  let { min = -Infinity, max = Infinity, editable } = field
  min = min ? Number(min) : -Infinity
  max = max ? Number(max) : +Infinity
  if (!!chargeAgainst && chargeAgainst.isChargeAgainst) {
    min = max * -1
  }
  value = Number(value)
  if (editable && value <= max && value >= min) {
    return undefined
  }
  if (!editable && value <= 999999999999999 && value >= -999999999999999) {
    return undefined
  }
  return i18n.get('beyond-the-scope', { __k0: label, __k1: min, __k2: max })
}

function fnScaleValidator(scale, value, label = '') {
  let error = undefined
  if (!value && value * 1 !== 0) {
    error = i18n.get('请输入金额')
    return error
  }
  let re = /^-?[0-9]+$/
  if (scale) {
    re = new RegExp(`^-?(([1-9]\\d*)|0)(\\.\\d{1,${scale}})?$`)
  }
  if (!re.test(value)) {
    error =
      scale > 0
        ? i18n.get('please-input-number', { __k0: label, __k1: scale })
        : i18n.get('{__k0}请输入整数', { __k0: label })

    return error
  }
  return error
}

export function vPhotoValidator(field) {
  return (rule, value, callback) => {
    if (!value) {
      return callback(i18n.get('请输入订单号'))
    }
    if (!!~value.indexOf(' ')) {
      return callback(i18n.get('请勿输入空格'))
    }
    return callback()
  }
}

export function isZero(value) {
  if (!value) return false
  if (typeof value === 'string') {
    return value === '0'
  }
  if (typeof value === 'number') {
    return value === 0
  }
  return false
}

export function requiredInvoiceForm(field, props) {
  return (rule, value, callback) => {
    if (rule.level === 0) {
      return callback()
    }

    let { type, invoiceCorporationId, invoices, attachments, invoiceConfirm } = value
    if (!field.editable && type === 'exist' && invoices && !invoices.length && attachments && !attachments.length) {
      callback(i18n.get('发票导入失败，请重新添加'))
    }
    if (type === 'noExist' && field?.invoiceType?.noExistConfig) {
      const options = invoiceOptions(field?.invoiceType, props?.currentFlowNode)
      if (!options?.some(i => i.type === type)) {
        callback(i18n.get('请添加发票'))
      }
    }
    if (type === 'unify' && !invoiceCorporationId) {
      callback(i18n.get('请添加开票方'))
    }
    // let amountObj = props?.form?.getFieldValue('amount')
    // let isZeroAmount = isZero(amountObj?.standard)

    if (
      field.invoiceType.isRequired &&
      type === 'exist' &&
      (!attachments || !attachments.length) &&
      (!invoices || !invoices.length) &&
      !invoiceConfirm &&
      // !isZeroAmount &&
      !field.optional
    ) {
      return callback(i18n.get('请添加发票'))
    }

    return callback()
  }
}

export const validatorStaffRange = (field, props) => {
  const { allowExternalStaff, label, editable, allowInteriorStaff = true } = field
  return (rule, value, callback) => {
    if (rule.level === 0) {
      return callback()
    }
    if (editable && allowInteriorStaff && allowExternalStaff) {
      return callback()
    }
    if (value && editable) {
      const staffsVisibilityMap = api.getState()['@common'].staffsVisibilityMap || {}
      const externalStaffMap = api.getState()['@common'].externalStaffMap || {}
      const getStaff = staffId => {
        return isObject(staffId) ? staffId : staffsVisibilityMap[staffId] || externalStaffMap[staffId]
      }
      const staffs = Array.isArray(value) ? value : [value]
      if (!allowExternalStaff) {
        if (staffs.find(o => getStaff(o)?.external)) {
          const externalRoot = api.getState()['@common'].externalDepartments?.data
          return callback(
            i18n.get(`{label}的取值规则不允许选择`, { label }) +
            (externalRoot ? externalRoot[0].name : i18n.get('外部人员'))
          )
        }
      }
      if (!allowInteriorStaff) {
        if (staffs.find(o => !getStaff(o)?.external)) {
          return callback(i18n.get(`{label}的取值规则不允许选择`, { label }) + i18n.get('内部员工'))
        }
      }
    }
    return callback()
  }
}

/**
 * 根据取消依赖配置是否请需要清空值
 * @param {*} allowCancelDependence
 * @param {*} value
 */
export function isAllowCancelDependenceClearValue(allowCancelDependence, dependValue, fieldValue) {
  if (allowCancelDependence && !dependValue && fieldValue) {
    return false
  }
  return true
}

export function payeeInfoValidator(field, props) {
  return (rule, value, callback) => {
    const { billSpecification } = props
    const { editable, optional } = field
    if (rule.level === 0 || optional) {
      return callback()
    }
    const payConfig = billSpecification?.configs?.find(el => el.ability === 'pay') || {}
    if (value && editable && payConfig?.allowSelectionReceivingCurrency) {
      const error = []
      if (!value.receivingCurrency) {
        error.push(i18n.get('收款币种不可为空'))
      }
      if (!value.id) {
        error.push(i18n.get('收款账户不可为空'))
      }
      if (error.length) {
        return callback(error.join('，'))
      }
    }
    return callback()
  }
}
