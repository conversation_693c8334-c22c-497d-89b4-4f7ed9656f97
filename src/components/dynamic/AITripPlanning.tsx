import { app, app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import EnhanceTitleHook from '../../plugins/basic-elements/enhance-title-hook'
import { OutlinedGeneralCamera, OutlinedGeneralVoice, TwoToneLogoAi } from '@hose/eui-icons'
import { Button, Dialog, ImageUploader, ImageViewer, ProgressBar, TextArea, Toast } from '@hose/eui-mobile'
import AIPic1 from '../../images/AITrip-hello.png'
import AIBg from '../../images/AITrip-Bg.png'
import styles from './AITripPlanning.module.less'
import { get } from 'lodash'
import Popup from '@ekuaibao/popup-mobile/esm/index'
import { toast } from '../../lib/util'
import { EnhanceConnect } from '@ekuaibao/store'
import loadable from '@loadable/component'
import VoiceToTextPopup from './VoiceToTextPopup'
import { uuid } from '@ekuaibao/helpers'

const FilesUploader = loadable(() => import('@ekuaibao/uploader/esm/FilesUploader'))
const HuaWeiUploader = loadable(() => import('@ekuaibao/uploader/esm/HuaWeiUploader'))
import { defaultInvalidSuffixes, onInvalidFile } from '../../lib/invalidSuffixFile'
import { getUploadUrl } from '../utils/fnAttachment'
import React from 'react'
const { buildData, getUploadUrl, getToken, uploadDone } = api.require(
  '@components/utils/fnAttachment'
)
const maxSize = 64

// @ts-ignore
@EnhanceTitleHook(i18n.get('AI 规划行程'))
@EnhanceConnect(state => ({
  uploadServiceUrl: state['@common'].uploadServiceUrl
}))
export default class AITripPlanning extends Component<any, any> {
  isStopFetch: boolean = false;
  currentRequestId: string = '';
  constructor(props: any) {
    super(props)
    this.state = {
      flexible: props.AITripContextDetail?.travelFlex ? 1 : 0,
      billSpecification: props.billSpecification,
      commonGroupList: null,
      orderText: '',
      data: null,
      token: '',
      attachmentInfos: [],
      attachmentArrs: [],
      uploaderFileList: [],

      isRecording: false,
      recordDuration: 0,

      showNewAIFeatTextGuide: !localStorage.getItem('showNewAIFeatTextGuide'),
      showNewAIFeatSubmitGuide: !localStorage.getItem('showNewAIFeatSubmitGuide'),
      hasVoicePermission: false,
      version: '2.9.17'
    }
  }

  private __result: any

  componentWillMount() {
    getToken().then(token => {
      this.setState({ token })
    })
  }

  componentDidMount(): void {
    this.getDeviceInfo()
    .then(info => {
      this.setState({ version: info?.version })
      console.log('原生设备信息:', info)
    })
    .catch(err => {
      this.setState({ version: 'noVersion' })
      console.error('获取设备信息失败:', err)
    })
  }

  componentWillUnmount(): void {
    this.isStopFetch = true;
    Popup?.hide()
    Toast?.clear()
  }

  readNewAIFeatSubmitGuide = () => {
    this.setState({
      showNewAIFeatSubmitGuide: false
    })
    localStorage.setItem('showNewAIFeatSubmitGuide', 'false')
  }

  renderBottom = () => {
    const { orderText, showNewAIFeatSubmitGuide } = this.state
    const allowOrder = orderText
    return (
      <div className="bottom-bar" style={{ position: 'relative' }}>
        {showNewAIFeatSubmitGuide && allowOrder &&
          <div className="popover-third" onClick={() => this.readNewAIFeatSubmitGuide()}>
            <div className="popover-title-second">{i18n.get('点这里，自动创建行程明细')}</div>
            <div className="popover-arrow-second" />
          </div>
        }
        <Button
          className={allowOrder ? "bottom-btn" : "bottom-btn-disabled"}
          block
          category="secondary"
          size="middle"
          theme={allowOrder ? "highlight" : "default"}
          onClick={allowOrder ? this.createTripDetail : undefined}
        >
          <div className="bottom-bar-text">{i18n.get('创建明细')}</div>
        </Button>
      </div>
    )
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  handleDone = uploaderFileList => {
    const { data, attachmentInfos } = this.state
    let attachments

    // 不直接清空 uploaderFileList，保持 UI 的文件列表，避免闪烁
    const tempUploaderFileList = [...uploaderFileList]

    uploadDone.call(this, {
      list: uploaderFileList,
      value: attachments,
      onChange: list => {
        const updatedAttachmentInfos = [...attachmentInfos, ...uploaderFileList.map(el => get(el, 'response.fileId'))]

        const newWayPoint = { ...data, attachments: list }
        this.setState({
          data: newWayPoint,
          attachmentInfos: updatedAttachmentInfos,
          uploaderFileList: tempUploaderFileList
        })
        this.setState({ uploaderFileList: [] })
      }
    })
  }

  handleOnStart = file => {
    if (file && file.length > 0) {
    } else {
      toast.info(i18n.get('attachment-max-msg', { __k0: maxSize - 4 }))
    }
  }

  handleDelete = (line, index) => {
    let { data, attachmentInfos } = this.state
    let { attachments } = data
    attachments = attachments?.slice(0)
    attachmentInfos = attachmentInfos?.slice(0)
    attachments?.splice(index, 1)
    attachmentInfos?.splice(index, 1)
    this.setState({ data: { ...data, attachments } })
    this.setState({ attachmentInfos: attachmentInfos })
  }

  handleInvalidFiles = (invalidFiles, type) => {
    let { invalidSuffixes = defaultInvalidSuffixes } = this.props
    onInvalidFile(invalidFiles, invalidSuffixes, type)
  }

  renderPicView = (line, index, isEdit) => {
    let fileItem = {}
    if (line.status) {
      fileItem = line
    } else {
      const { key, fileName, fileId } = line
      const { url = '', thumbUrl = '' } = this.state.attachmentArrs.find(el => el.id === fileId) || {}
      fileItem = {
        key,
        url: url || line.url || '',
        thumbUrl: thumbUrl || line.thumbUrl || '',
        fileId
      }
    }

    const fileList = fileItem.url
      ? [
          {
            url: fileItem.url,
            // 如果你想保留 fileId 或其他字段，也可以自定义添加进去
            ...fileItem
          }
        ]
      : []

    return (
      <ImageUploader
        key={index}
        value={fileList}
        onChange={newFileList => {
          if (newFileList.length === 0) {
            // 说明用户删除了图片
            this.handleDelete(line, index)
          }
        }}
        maxCount={1}
        upload={async file => {
          // 上传逻辑（你可以用你原来的上传函数）
          try {
            const result = await this.uploadImage(file) // 你自己的上传函数
            return {
              url: result.url
            }
          } catch (e) {}
        }}
        showUpload={isEdit}
        preview={() => {
          if (fileItem.url) {
            ImageViewer.show({ image: fileItem.url })
          }
        }}
      />
    )
  }

  renderFilesUploader = () => {
    const { token } = this.state
    const { uploadServiceUrl, invalidSuffixes = defaultInvalidSuffixes } = this.props
    const uploadUrl = uploadServiceUrl && uploadServiceUrl.uploadUrl

    return (
      <div style={{ flex: 1 }}>
        {window.__PLANTFORM__ == 'HUAWEI' && window.isAndroid ? (
          <HuaWeiUploader
            accept=".png,.jpg,.jpeg"
            multiple={false}
            maxCount={1}
            action={IS_STANDALONE ? getMinioUploadUrl() : uploadUrl}
            onChange={this.handleChange}
            onDone={this.handleDone}
            onStart={this.handleOnStart}
            data={file => buildData(file, token, uploadServiceUrl)}
          >
            <Button className="input-btn-l" category="secondary">
              <OutlinedGeneralCamera style={{ margin: '-3px 4px 0 0' }}/>
              {i18n.get('图片识别')}
            </Button>
          </HuaWeiUploader>
        ) : (
          <FilesUploader
            accept=".png,.jpg,.jpeg"
            multiple={false}
            maxCount={1}
            action={IS_STANDALONE ? getUploadUrl : uploadUrl}
            type={IS_STANDALONE}
            maxSize={maxSize}
            onChange={this.handleChange}
            onDone={this.handleDone}
            onStart={this.handleOnStart}
            onInvalidFile={this.handleInvalidFiles}
            invalidSuffixes={invalidSuffixes}
            data={file => buildData(file, token, uploadServiceUrl)}
          >
            <Button className="input-btn-l" category="secondary">
              <OutlinedGeneralCamera style={{ margin: '-3px 4px 0 0' }}/>
              {i18n.get('图片识别')}
            </Button>
          </FilesUploader>
        )}
      </div>
    )
  }

  renderFilesUploaderDisabled = () => {
    return (
      <div style={{ flex: 1, marginRight: 8 }}>
        <Button
          style={{ width: '100%', color: '#C9CDD4' }}
          className="input-btn-l"
          category="secondary"
          onClick={() => {
            Toast.show({
              icon: null,
              content: i18n.get('最多只能上传一张图片')
            })
          }}
        >
          <OutlinedGeneralCamera style={{ marginRight: 4 }} />
          {i18n.get('图片识别')}
        </Button>
      </div>
    )
  }

  createTripDetail = async () => {
    this.readNewAIFeatSubmitGuide()
    this.isStopFetch = false;
    const requestId = uuid();
    this.currentRequestId = requestId;
    const { orderText, data, flexible, billSpecification } = this.state

    let tripProgressRef: TripProgressContent | null = null;

    const waitingDialog = Dialog.show({
      closeOnAction: true,
      actions: [
        {
          key: 'cancel',
          cancel: true,
          onClick: () => {
            this.isStopFetch = true;
          },
          text: '取消'
        }
      ],
      image: AIPic1,
      content: <TripProgressContent  ref={ref => tripProgressRef = ref} />
    })

    const fileKey = data?.attachments?.[0]?.fileId?.key
    const fileURL = data?.attachments?.[0]?.fileId?.url

    if (!orderText) {
      return
    }

    const requestParams = {
      flexible,
      specificationId: billSpecification?.id,
      query: orderText,
      ...(fileKey ? { fileKey } : {}),
      ...(fileURL ? { fileURL } : {})
    }

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('timeout'));
      }, 15 * 1000); // 15秒归类超时
    });
  
    try {
      const res = await Promise.race([
        api.invokeService('@bill:post:order:ai:plan:travel', requestParams),
        timeoutPromise
      ]);
  
      if (this.isStopFetch || requestId !== this.currentRequestId) return;

      tripProgressRef?.forceFinish?.();

      setTimeout(() => {
        waitingDialog.close();
  
      const failureData = res?.value?.failures?.data;
      const successData = res?.value?.successes;
      const answer = res?.value?.answer;
  
      if ((!failureData || Object.keys(failureData)?.length === 0) && successData?.length > 0) {
        this.props.layer?.emitOk({ newTrip: res?.value });
      } else if (typeof failureData === 'object' && Object.keys(failureData)?.length > 0) {
        const formatToMonthDay = (dateStr: string) => {
          if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return '';
          return dateStr.slice(5).replace('-', '.');
        };
  
        Dialog.show({
          closeOnAction: true,
          title: '部分行程创建失败',
          actions: [
            {
              key: 'ok',
              cancel: true,
              text: '手动创建',
              onClick: () => {
                this.props.layer?.emitOk({ newTrip: res?.value });
              }
            }
          ],
          content: (
            <div>
              {Object.keys(failureData).map((tipText, index) => (
                <div key={index} style={{ marginTop: index !== 0 ? '24px' : 0 }}>
                  <div style={{ fontSize: '16px', color: '#333', marginBottom: '8px' }}>{tipText}</div>
                  <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: '12px' }}>
                    <thead>
                      <tr style={{ backgroundColor: '#f5f5f5', textAlign: 'left' }}>
                        <th style={{ padding: '5px', minWidth: 66 }}>行程类型</th>
                        <th style={{ padding: '5px' }}>城市</th>
                        <th style={{ padding: '5px' }}>日期</th>
                      </tr>
                    </thead>
                    <tbody>
                      {failureData[tipText].map((item, idx) => (
                        <tr key={idx}>
                          <td style={{ padding: '5px', borderBottom: '0.5px solid rgba(29, 33, 41, 0.10)', minWidth: 66 }}>{item?.类型}</td>
                          <td style={{ padding: '5px', borderBottom: '0.5px solid rgba(29, 33, 41, 0.10)' }}>
                            {item?.出发城市 || item?.住宿城市 || item?.用车城市 || item?.用餐城市}
                          </td>
                          <td style={{ padding: '5px', borderBottom: '0.5px solid rgba(29, 33, 41, 0.10)' }}>
                            {item?.返回日期 ? `${formatToMonthDay(item?.出发日期)}-${formatToMonthDay(item?.返回日期)}` : formatToMonthDay(item?.出发日期)}
                            {item?.入住日期 ? `${formatToMonthDay(item?.入住日期)}-${formatToMonthDay(item?.入住日期)}` : formatToMonthDay(item?.住宿日期)}
                            {item?.开始日期 ? `${formatToMonthDay(item?.开始日期)}-${formatToMonthDay(item?.开始日期)}` : formatToMonthDay(item?.结束日期)}
                            {formatToMonthDay(item?.用餐日期)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ))}
            </div>
          )
        });
      } else if ((!failureData || Object.keys(failureData)?.length === 0) && successData?.length === 0 && answer) {
        Dialog.show({
          closeOnAction: true,
          actions: [{ key: 'cancel', cancel: true, text: '调整输入' }],
          content: answer
        });
      } else if ((!failureData || Object.keys(failureData)?.length === 0) && successData?.length === 0 && !answer) {
        Dialog.show({
          closeOnAction: true,
          actions: [{ key: 'cancel', cancel: true, text: '调整输入' }],
          content: '建议您输入清晰的差旅需求，然后重试'
        });
      }
        
      }, 800);
    } catch (e) {
      waitingDialog.close();
  
      Dialog.show({
        closeOnAction: true,
        title: '行程创建失败',
        actions: [
          {
            key: 'create',
            style: { color: 'rgba(37,85,255,1)' },
            onClick: () => {
              this.props.layer?.emitCancel({});
            },
            text: '手动创建'
          },
          {
            key: 'cancel',
            cancel: true,
            text: '重试'
          }
        ],
        content: '抱歉，AI助理掉线了，请您稍后重试或手动创建行程'
      });
    }
  };

  postMessageToNative(data: any) {
    const msg = JSON.stringify(data)
    if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
      window.ReactNativeWebView.postMessage(msg)
    } else {
      window.postMessage(msg, '*')
    }
  }

  getDeviceInfo(): Promise<VersionInfo> {
    const userAgent = navigator.userAgent.toLowerCase()
    const isAndroidApp = userAgent.includes('android') && userAgent.includes('ekuaibao')
  
    if (!isAndroidApp) {
      return Promise.reject(new Error('当前环境不是Android App'))
    }
  
    const message = {
      action: 'call:getVersionInfo',
      messageType: 'getVersionInfo',
      callbackId: 'get-device-info',
    }
  
    this.postMessageToNative(message)
  
    return new Promise((resolve, reject) => {
      function handleMessage(event: MessageEvent) {
        try {
          const data = JSON.parse(event.data)
          if (data?.callbackId === 'get-device-info') {
            window.removeEventListener('message', handleMessage)
            resolve(data.data)
          }
        } catch (error) {
          window.removeEventListener('message', handleMessage)
          reject(error)
        }
      }
  
      window.addEventListener('message', handleMessage)
    })
  }

  checkVoicePermissionAndVersion = (): Promise<void> => {
    if (this.state.hasVoicePermission) return Promise.resolve();
    const userAgent = navigator.userAgent.toLowerCase()
    const isAndroid = userAgent.includes('android')

    const triggerMicAuthorization = (): Promise<void> => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        return Promise.reject(new Error('当前设备不支持录音权限申请'))
      }
    
      return navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(stream => {
          stream.getTracks().forEach(track => track.stop())
          this.setState({ hasVoicePermission: true })
        })
        .catch(err => {
          // 拒绝权限或设备异常
          Toast.show({
            content: i18n.get('请在设置中检查麦克风权限')
          })
          return Promise.reject(new Error('未授权使用麦克风'))
        })
    }
  
    // iOS 不需要版本校验，直接通过
    if(this.state.version === 'noVersion'){
      return triggerMicAuthorization()
    }
    
    if (window?.isAndroid) {
      const currentVersion = this.state.version
      const minimumVersion = '2.9.18'
    
      const compareVersion = (v1: string, v2: string) => {
        const a = v1.split('.')?.map(Number)
        const b = v2.split('.')?.map(Number)
        for (let i = 0; i < Math?.max(a?.length, b?.length); i++) {
          const n1 = a[i] || 0
          const n2 = b[i] || 0
          if (n1 < n2) return -1
          if (n1 > n2) return 1
        }
        return 0
      }
  
      console.log('当前版本:', currentVersion, '最低版本:', minimumVersion)
    
      if (compareVersion(currentVersion, minimumVersion) < 0) {
        Toast.show({
          content: i18n.get('当前版本过低，暂不支持语音输入，请升级 App')
        })
        return Promise.reject(new Error('当前版本过低，暂不支持语音输入，请升级 App'))
      } else {
        return triggerMicAuthorization()
      }
    } else {
      return triggerMicAuthorization()
    }
  }

  handleVoiceInput = async () => {
    try {
      await this.checkVoicePermissionAndVersion()
    } catch (err) {
      return
    }
  
    try {
      await this.voiceInput()
    } catch (err) {
      console.log('用户取消语音输入:', err)
    }
  }

  voiceInput = () => {
    return new Promise((resolve, reject) => {
      Popup.show(
        <VoiceToTextPopup
          onOk={data => {
            Popup.hide()
            resolve(data)
            const newOrderText = (this.state.orderText + data.voiceOrederText).slice(0, 300)
            this.setState({ orderText: newOrderText })
          }}
          onCancel={() => {
            Popup.hide()
            reject('用户取消')
          }}
        />,
        {
          animationType: 'slide-up',
          maskClosable: true,
          wrapClassName: 'popup-wrapper-style'
        }
      )
    })
  }

  onTextAreaChange = (value: any) => {
    this.readNewAIFeatTextGuide()
    if (value.length > 300) {
      Toast.show({
        icon: null,
        content: i18n.get('最多可输入300字')
      })
      return
    }else{
      this.setState({ orderText: value })
    }
  }

  readNewAIFeatTextGuide = () => {
    this.setState({
      showNewAIFeatTextGuide: false
    })
    localStorage.setItem('showNewAIFeatTextGuide', 'false')
  }

  render() {
    const { orderText, attachmentInfos, showNewAIFeatTextGuide } = this.state
    let readonly = false

    return (
      <div className={styles['AITrip-view-wrapper']} style={{ backgroundImage: `url(${AIBg})` }}>
        <div className="content-layer">
          <div className="content">
            <div className="center-logo-box">
              <div className="center-logo-bg">
                <TwoToneLogoAi className="center-logo" />
              </div>
            </div>
            <div className="title">{i18n.get('您好，我是 AI 规划行程助理')}</div>
            <div className="guide-text" style={{ position: 'relative' }}>
              <div>
                {i18n.get(
                  '把繁琐的填写工作交给 AI ，您可以上传图片或者使用打字、语音输入，让 AI 根据您的差旅需求快速创建行程(暂不支持港澳台及国际城市)'
                )}
              </div>
              {showNewAIFeatTextGuide &&
                <div className="popover-second" onClick={() => this.readNewAIFeatTextGuide()}>
                  <div className="popover-title">{i18n.get('输入您的出差需求，让 AI 帮您规划行程')}</div>
                  <div className="popover-second-arrow" />
                </div>
              }
            </div>
            <div className="chat-box">
              <div style={{ flex: 1 }}>
              <TextArea
                className="text-area"
                maxLength={300}
                autoSize={{ minRows: 18, maxRows: 18 }}
                style={{ maxHeight: attachmentInfos.length > 0 ? 'calc(100vh - 482px)' : 'calc(100vh - 402px)' }}
                value={orderText}
                placeholder={i18n.get(
                  '请输入您的行程，您可以输入：明天从北京去杭州出差三天，然后返回'
                )}
                onChange={e => this.onTextAreaChange(e)}
              ></TextArea>
              </div>
              <div className="pic-area">
                {attachmentInfos &&
                  attachmentInfos.length > 0 &&
                  attachmentInfos.map((line, index) => this.renderPicView(line, index, !readonly))}
              </div>

              <div className="input-btn-box">
                <div className='input-btn-box-l'>
                  {attachmentInfos.length > 0 ? this.renderFilesUploaderDisabled() : this.renderFilesUploader()}
                </div>
                <div className='input-btn-box-r'>
                  <Button className="input-btn-r" category="secondary" onClick={this.handleVoiceInput}>
                    <OutlinedGeneralVoice style={{ margin: '-2px 4px 0 0' }} />
                    {i18n.get('语音输入')}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        {this.renderBottom()}
      </div>
    )
  }
}

class TripProgressContent extends React.Component {
  state = {
    percent: 0
  }

  intervalId: NodeJS.Timeout | null = null
  stepIndex = 0

  stepConfigs = [
    { start: 0, end: 20, duration: 4000 },
    { start: 20, end: 50, duration: 6000 },
    { start: 50, end: 99, duration: 5000 }
  ]

  componentDidMount() {
    this.startStep(0)
  }

  componentWillUnmount() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
  }

  startStep = (index: number) => {
    const step = this.stepConfigs[index]
    if (!step) return

    const { start, end, duration } = step
    const totalSteps = end - start // 增长的百分比数
    const intervalTime = duration / totalSteps // 每 1% 需要的时间

    let current = start
    this.setState({ percent: current })

    this.intervalId = setInterval(() => {
      current += 1
      if (current >= end) {
        clearInterval(this.intervalId!)
        this.intervalId = null
        this.setState({ percent: end }, () => {
          this.startStep(index + 1)
        })
      } else {
        this.setState({ percent: current })
      }
    }, intervalTime)
  }

  forceFinish = () => {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.setState({ percent: 100 });
  }

  render() {
    const { percent } = this.state
    let progressText = 'AI助理正在分析出差需求...'
    if (percent > 20) {
      progressText = 'AI助理思考中...'
    }
    if (percent > 50) {
      progressText = 'AI助理规划中...'
    }
    return (
      <div>
        <div style={{ marginBottom: 12, color: 'rgba(29, 33, 41, 0.90)'}}>{i18n.get('AI助理正在为您规划出差行程，感谢您的耐心等待')}</div>
        <ProgressBar percent={this.state.percent} text />
        <div style={{ fontSize: '12px', color: 'rgba(29, 33, 41, 0.70)'}}>{i18n.get(progressText)}</div>
      </div>
    )
  }
}
