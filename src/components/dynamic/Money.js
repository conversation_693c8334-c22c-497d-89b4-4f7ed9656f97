/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { allowModifyField, contractAmountValidator, moneyValidator, required } from '../utils/validatorUtil'
import CurrencyMoney from '../../elements/currency/currency-money'
import fnDefineIsFormula from '../utils/fnDefineIsFormula'
import { changeFormValueByFieldName, standardValueMoney } from '../utils/fnInitalValue'
import { updateCurrencyValueByDimension } from '../utils/fnCurrencyObj'
import { get, isObject } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { getBillReceivingCurrency } from '../../plugins/feetype/parts/feeTypeInfoHelper'

@EnhanceField({
  descriptor: {
    type: 'money'
  },
  wrapper: wrapper(),
  validator(field, props) {
    const { isvalidatorLev } = field
    return isvalidatorLev === 1
      ? [required(field, props)]
      : [
          required(field, props),
          field.name === 'contractAmount' ? contractAmountValidator(field, props) : moneyValidator(field, props)
        ]
  },
  initialValue() {
    return undefined
  }
})
export default class MoneyWrapper extends PureComponent {
  constructor(props) {
    super()
    api.dataLoader('@common.currencyConfig').load()
    this.state = {
      isAuto: fnDefineIsFormula(props.calFields, props.field)
    }
  }

  componentDidMount() {
    const { originalValue } = this.props
    const defaultValue = get(this, 'props.field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum' && originalValue) {
      const details = originalValue.details || originalValue.trips
      let sumValue = standardValueMoney(0)
      details?.forEach(el => {
        const form = el.feeTypeForm || el.tripForm
        const defVal = isObject(form[defaultValue.value]) ? form[defaultValue.value].standard : form[defaultValue.value]
        if (defVal) {
          sumValue.standard = new Big(sumValue.standard).plus(Number(defVal)).toFixed(sumValue.standardScale)
        }
      })
      this.handleChange(sumValue)
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus && bus.watch('detail:value:change', this.handleDetailValueChange)
    bus && bus.watch('trip:value:change', this.handleDetailValueChange)
    bus && bus.on('dimension:currency:change', this.handleDimensionCurrencyChange)
    bus && bus.on('detail:amount:change', this.handleDetailAmountChange)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus && bus.un('detail:value:change', this.handleDetailValueChange)
    bus && bus.un('trip:value:change', this.handleDetailValueChange)
    bus && bus.un('dimension:currency:change', this.handleDimensionCurrencyChange)
    bus && bus.un('detail:amount:change', this.handleDetailAmountChange)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value) {
      let { bus, field, value, isDetail, form } = nextProps
      if (value && value.rate == '') {
        delete value.rate
      }
      if (field.name === 'loanMoney') {
        bus && bus.emit('loanMoney:changed', value) //自动计算的情况要更新一下
      }
      if (!!~['amount', 'apportionMoney'].indexOf(field.name)) {
        bus.emit('element:money:value:changed', value)
      }
      if (!field?.editable && field?.defaultValue?.type === 'costStandard' && value?.costStandardError) {
        form?.validateFields?.([field?.name])
      }

      isDetail && bus.emit('detail:money:change', { field: field.name, value })
      // if (field.name === 'amount') {
      //   this.handleRelationValueChange(value, field.name)
      // }
    }
  }
  handleDetailAmountChange = amount => {
    const { onChange, field, isDetail } = this.props
    if (field.field === 'amount' && isDetail) {
      onChange?.(standardValueMoney(amount))
    }
  }

  handleDimensionCurrencyChange = ({ currency, rates }) => {
    const { value } = this.props
    const data = updateCurrencyValueByDimension(value, currency, rates, true)
    this.handleChange(data)
  }

  handleDetailValueChange = details => {
    const defaultValue = get(this.props, 'field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      const dimensionCurrency = api.getState()['@bill'].dimensionCurrencyInfo
      let data = standardValueMoney(0, dimensionCurrency?.currency)
      details?.forEach(el => {
        const form = el.feeTypeForm || el.tripForm
        if (form?.[defaultValue?.value]) {
          data.standard = new Big(data?.standard)
            .plus(Number(form?.[defaultValue?.value]?.standard))
            .toFixed(data.standardScale)
        }
      })
      if (this.props.value?.standard !== data?.standard) {
        this.handleChange(data)
      }
    }
  }
  handleChange = value => {
    let { onChange, bus, field, detailId, external, tripId, isDetail, receivingCurrencyNum } = this.props
    const notEmptyValue = receivingCurrencyNum && ['amount', 'receivingAmount'].includes(field.name) // 有收款币种的时候，不清空值，这里需要值给收款币种做判断

    if (field.optional && value && !value.standard && value.standard !== 0 && !notEmptyValue) {
      //如果是选填的话，value是空则赋值成undefined
      value = undefined
    }
    if (value && value.rate == '') {
      delete value.rate
    }
    let type = get(field, 'defaultValue.type')
    onChange?.(value)
    let deleteSource = { detailId, tripId, fieldName: field.name }
    let isResetMoney = external && type !== 'formula' && bus
    isResetMoney && bus.emit('element:money:value:changed:external', deleteSource)
    isDetail && bus.emit('detail:money:change', { field: field.name, value })
    if (!!~['amount', 'apportionMoney'].indexOf(field.name)) {
      bus.emit('element:money:value:changed', value)
    }
    if (field.name === 'loanMoney') {
      bus && bus.emit('loanMoney:changed', value)
    }
    if (field.name === 'reimbursementMoney') {
      bus && bus.emit('reimbursementMoney:changed', value)
    }
    if (field.name === 'payMoney') {
      bus && bus.emit('payMoney:changed', value)
    }
    // if (field.name === 'receivingAmount') {
    //   this.handleRelationValueChange(value, field.name)
    // }
  }

  handleRelationValueChange = (value, changeFieldName) => {
    const { bus, receivingCurrencyNum } = this.props
    const relationFields = ['receivingAmount', 'amount']
    if (bus && !!receivingCurrencyNum && relationFields.includes(changeFieldName)) {
      const fnCompareEqual = (oldValue, newValue) => {
        return (
          Number(oldValue?.standard) === Number(newValue?.standard) &&
          oldValue?.standardNumCode === newValue?.standardNumCode &&
          oldValue?.foreignNumCode === newValue?.foreignNumCode
          // && parseFloat(oldValue?.foreign) === parseFloat(newValue?.foreign)
        )
      }

      bus.getValue().then(formValue => {
        const currentValue = formValue[changeFieldName]
        if (!fnCompareEqual(currentValue, value)) {
          const { formData, isChangeValue } = changeFormValueByFieldName(changeFieldName, value, formValue, {
            receivingCurrencyNum
          })
          let updateFormValue = isChangeValue
          if (updateFormValue && changeFieldName === 'amount') {
            const newReceivingAmount = get(formData, 'receivingAmount')
            const oldReceivingAmount = get(formValue, 'receivingAmount')
            updateFormValue = !fnCompareEqual(newReceivingAmount, oldReceivingAmount)
          }
          if (updateFormValue) {
            bus.setValue(formData)
          }
        }
      })
    }
  }

  render() {
    let {
      value,
      form,
      field,
      calFields = {},
      flowAllowModifyFields,
      bus,
      external,
      selectCurrencyDisable,
      foreignNumCode,
      isModifyBill,
      cannotEditAmountField,
      showAllFeeType,
      shouldSaveFeetype,
      hasCurrencyField,
      billSpecification = {},
      timeField,
      receivingCurrencyNum,
      loanRate,
      isForeignCurrencyEdit
    } = this.props
    const { name, defaultValue } = field
    const { configs = [] } = billSpecification
    const writtenOff = configs.find(item => item.ability === 'writtenOff')
    // 本位币统一配置只对费用金额生效
    if (name !== 'amount') {
      selectCurrencyDisable = false
    }
    const isAuto = fnDefineIsFormula(calFields, field)
    let formulaValue = (defaultValue && defaultValue.formulaValue) || ''

    let editable = allowModifyField(field, flowAllowModifyFields)

    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModifyBill && !showAllFeeType && editable && cannotEditAmountField) editable = false

    return (
      <CurrencyMoney
        hasCurrencyField={hasCurrencyField}
        clear
        form={form}
        writtenOff={writtenOff}
        field={field}
        bus={bus}
        value={value}
        isAuto={isAuto}
        editable={isAuto ? false : editable}
        formulaValue={formulaValue}
        external={external}
        onChange={this.handleChange}
        selectCurrencyDisable={selectCurrencyDisable}
        foreignNumCode={foreignNumCode}
        shouldSaveFeetype={shouldSaveFeetype}
        timeField={timeField}
        receivingCurrencyNum={receivingCurrencyNum}
        loanRate={loanRate}
        isForeignCurrencyEdit={isForeignCurrencyEdit}
      />
    )
  }
}
