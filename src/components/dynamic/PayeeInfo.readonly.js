/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-11-05 23:59:08
 * @Description  :
 * 描述功能/使用范围/注意事项
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import PayeeDetailItem from '../../elements/puppet/PayeeDetailItem'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { getAccountById } from '../../plugins/bill/bill.action'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { Toast } from 'antd-mobile'

@EnhanceField({
  descriptor: {
    type: 'payeeInfo',
    test({ type }) {
      return type === 'ref:pay.PayeeInfo' // 借款单?
    }
  },
  wrapper: wrapper(true, false)
})
@EnhanceConnect(state => ({
  allCurrencyRates: state['@common'].allCurrencyRates || []
}))
export default class PayeeInfoReadonly extends PureComponent {
  onClick = () => {
    getAccountById(this.props.value.id).then(res => {
      if (res?.items?.length) {
        api.open('@basic:AddPayeeNew', { data: res.items[0], title: '查看收款信息', disabled: true })
      } else {
        Toast.show({
          icon: 'fail',
          content: i18n.get('收款账户不存在')
        })
      }
    })
  }
  componentDidMount() {
    api.dataLoader('@common.payeeConfig').reload()
    api.dataLoader('@common.powers').load()
    api.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PEE')
    api.invokeService('@bill:get:payee:config:check')
  }
  render() {
    const {
      bus,
      value,
      field,
      multiplePayeesMode,
      payPlanMode,
      payeePayPlan,
      emptyPlaceholder,
      noPayInfo,
      allCurrencyRates,
      receivingCurrency
    } = this.props
    const label = fnGetFieldLabel(field)
    if ((!value || value?.length === 0) && emptyPlaceholder) {
      return emptyPlaceholder || '-'
    }
    let receivingCurrencyStrCode = ''
    if (receivingCurrency) {
      if (!allCurrencyRates?.length) {
        api.dataLoader('@common.allCurrencyRates').reload()
      }
      const currency = allCurrencyRates.find(item => item.numCode === receivingCurrency)
      receivingCurrencyStrCode = currency?.strCode
    }
    return (
      <div style={{ display: 'flex', width: '100%' }}>
        <PayeeDetailItem
          bus={bus}
          dataSource={value}
          label={label}
          receivingCurrency={{ strCode: receivingCurrencyStrCode }}
          readonly
          onClick={this.onClick}
          multiplePayeesMode={multiplePayeesMode}
          payPlanMode={payPlanMode}
          payeePayPlan={payeePayPlan}
          noPayInfo={noPayInfo}
        />
      </div>
    )
  }
}
