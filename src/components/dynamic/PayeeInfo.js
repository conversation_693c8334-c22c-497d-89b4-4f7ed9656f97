import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import PayeeDetailItem from '../../elements/puppet/PayeeDetailItem'
import { required, allowModifyField, payeeInfoValidator } from '../utils/validatorUtil'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import fnGetFieldLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { EnhanceConnect } from '@ekuaibao/store'
import { Dialog } from '@hose/eui-mobile'
import { getBoolVariation } from '../../lib/featbit'
import { useNewAutomaticAssignment } from '../utils/fnAutoDependence'

@EnhanceField({
  descriptor: {
    type: 'payeeInfo', // 申请单?
    test({ type }) {
      return type === 'ref:pay.PayeeInfo' // 借款单?
    }
  },
  wrapper: wrapper(false, false),
  validator(field, props) {
    return [required(field, props), payeeInfoValidator(field, props)]
  },
  initialValue(props) {
    const { defaultPayeeInfo, multiplePayeesMode, payPlanMode, payeePayPlan, field, value, isModifyBill } = props
    if (field.name === 'feeDetailPayeeId') {
      return ''
    } else if (multiplePayeesMode) {
      return { multiplePayeesMode, payPlanMode, payeePayPlan }
    } else if (!!value) {
      return value
    } else if (!isModifyBill) {
      //审批中修改的话不返回defaultPayeeInfo
      return defaultPayeeInfo
    }
    return undefined
  }
})
@EnhanceConnect(state => ({
  dimensionCurrencyInfo: state['@bill'].dimensionCurrencyInfo,
  standardCurrency: state['@common'].standardCurrency,
  historyCurrencyInfo: state['@bill'].historyCurrencyInfo,
  allCurrencyRates: state['@common'].allCurrencyRates
}))
export default class PayeeInfo extends PureComponent {
  constructor(props) {
    super(props)
    const { billSpecification, field } = props
    const payConfig = billSpecification?.configs?.find(el => el.ability === 'pay') || {}
    const { dependence } = field
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.state = {
      payConfig,
      isDependence,
      dependenceMap,
      dependenceList: [],
      allCurrencyRates: [],
      receivingCurrency: {}
    }
  }

  async componentWillMount() {
    const { bus, allCurrencyRates } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
    bus.on('dimension:currency:init', this.initDimensionCurrencyChange)
    bus.on('dimension:currency:change', this.handleDimensionCurrencyChange)
    bus.on('payPlan:payeeInfo:switcher', this.handleSwitcherChange)
    bus.watch('set:history:currency:rate', this.setHistoryCurrencyAndChangeRate)
    if (!allCurrencyRates?.length) {
      await api.dataLoader('@common.allCurrencyRates').reload()
    }
    this.initReceivingCurrency()
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
    bus.un('dimension:currency:init', this.initDimensionCurrencyChange)
    bus.un('dimension:currency:change', this.handleDimensionCurrencyChange)
    bus.un('payPlan:payeeInfo:switcher', this.handleSwitcherChange)
    bus.un('set:history:currency:rate', this.setHistoryCurrencyAndChangeRate)
  }

  componentWillReceiveProps(nextProps) {
    const { defaultPayeeInfo: nextDefaultPayeeInfo, isModifyBill } = nextProps
    const { defaultPayeeInfo, onChange, value } = this.props
    const { isDependence, dependenceList } = this.state
    if (defaultPayeeInfo !== nextDefaultPayeeInfo && !value?.id && !isModifyBill) {
      if (!dependenceList.length && !isDependence) {
        onChange?.(this.fnGetValue(nextDefaultPayeeInfo))
      } else if (!dependenceList.find(v => v.id === nextDefaultPayeeInfo?.id)) {
        onChange?.(this.fnGetValue())
      }
    }
  }

  initReceivingCurrency = () => {
    const { payConfig } = this.state
    const {
      onChange,
      value = {},
      dimensionCurrencyInfo,
      standardCurrency,
      allCurrencyRates,
      receivingCurrency,
      originalValue
    } = this.props
    if (!payConfig?.allowSelectionReceivingCurrency) return
    const allReceivingCurrency = this.getAllReceivingCurrency(
      dimensionCurrencyInfo?.currency || standardCurrency,
      allCurrencyRates,
      true
    )
    const isReceivingCurrencyEmpty = allReceivingCurrency.length === 0
    if (isReceivingCurrencyEmpty) return

    let currentReceivingCurrency = allReceivingCurrency[0]
    const initReceivingCurrencyNumCode = receivingCurrency || originalValue?.receivingCurrency

    if (initReceivingCurrencyNumCode) {
      currentReceivingCurrency =
        allReceivingCurrency.find(item => item.numCode === initReceivingCurrencyNumCode) || allReceivingCurrency[0]
    }

    this.setState({ receivingCurrency: currentReceivingCurrency, allReceivingCurrency })
    onChange && onChange({ ...value, receivingCurrency: currentReceivingCurrency.numCode })
  }

  getAllReceivingCurrency = (currentStandardCurrency = {}, rates = [], updateReceivingCurrencyNumCode = false) => {
    const { payConfig } = this.state
    let allReceivingCurrency = rates
    if (updateReceivingCurrencyNumCode) {
      allReceivingCurrency = rates.filter(v => v.originalId === currentStandardCurrency?.numCode) || []
    }

    const hasStandardCurrency = allReceivingCurrency.some(v => v.numCode === currentStandardCurrency.numCode)
    if (!hasStandardCurrency) {
      allReceivingCurrency.unshift(currentStandardCurrency)
    }

    if (payConfig.currencyRange?.length > 0) {
      allReceivingCurrency = allReceivingCurrency.filter(currency => payConfig.currencyRange.includes(currency.numCode))
    }
    return allReceivingCurrency
  }

  initDimensionCurrencyChange = ({ currency, rates }) => {
    const { receivingCurrency, originalValue } = this.props
    const initReceivingCurrency = receivingCurrency || originalValue?.receivingCurrency
    if (initReceivingCurrency) {
      this.fnUpdateCurrencyInfo(currency, rates, true, initReceivingCurrency)
    } else {
      this.fnUpdateCurrencyInfo(currency, rates, false)
    }
  }

  handleDimensionCurrencyChange = ({ currency, rates }) => {
    const { payConfig, receivingCurrency } = this.state
    if (receivingCurrency?.numCode === currency?.numCode) {
      return
    }
    this.fnUpdateCurrencyInfo(currency, rates, true)
    if (payConfig?.allowSelectionReceivingCurrency) {
      this.checkDetails(currency, true)
    }
  }

  setHistoryCurrencyAndChangeRate = async historyCurrency => {
    const dimensionCurrencyInfo = api.getState()['@bill']?.dimensionCurrencyInfo
    const { standardCurrency } = this.props
    const currency = dimensionCurrencyInfo?.currency || standardCurrency || {}
    this.fnUpdateCurrencyInfo(currency, historyCurrency, false)
  }

  fnUpdateCurrencyInfo = (
    currency,
    rates,
    updateReceivingCurrencyNumCode = false,
    initReceivingCurrencyNumCode = ''
  ) => {
    const { payConfig } = this.state
    if (!payConfig?.allowSelectionReceivingCurrency) return
    const { bus, onChange, value = {} } = this.props
    const allReceivingCurrency = this.getAllReceivingCurrency(currency, rates, updateReceivingCurrencyNumCode)

    const isReceivingCurrencyEmpty = !allReceivingCurrency.length
    this.setState({ allReceivingCurrency, isReceivingCurrencyEmpty })

    if (initReceivingCurrencyNumCode) {
      const currentReceivingCurrency =
        allReceivingCurrency.find(item => item.numCode === initReceivingCurrencyNumCode) || allReceivingCurrency[0]
      this.setState({ receivingCurrency: currentReceivingCurrency })
      bus.setValidateLevel(0)
      onChange && onChange({ ...value, receivingCurrency: currentReceivingCurrency?.numCode })
    } else if (updateReceivingCurrencyNumCode) {
      const currentReceivingCurrency = isReceivingCurrencyEmpty ? {} : allReceivingCurrency[0]
      this.setState({ receivingCurrency: currentReceivingCurrency })
      bus.setValidateLevel(0)
      onChange && onChange({ ...value, receivingCurrency: currentReceivingCurrency?.numCode })
    }
  }

  fnGetValue = data => {
    const { payConfig, receivingCurrency } = this.state
    if (payConfig?.allowSelectionReceivingCurrency && receivingCurrency) {
      return {
        ...(data || {}),
        receivingCurrency: receivingCurrency.numCode
      }
    }
    return data
  }

  handleClick = () => {
    const { bus, onChange, field, flowAllowModifyFields, value, template } = this.props
    const { isDependence, dependenceList = [] } = this.state
    const { allowClear = false } = field
    const editable = allowModifyField(field, flowAllowModifyFields)
    if (editable) {
      const selectedPayeeId = value ? value.id : ''
      const isFeeDetailPayeeId = field?.name === 'feeDetailPayeeId'
      let list
      if (isDependence) {
        if (isFeeDetailPayeeId) {
          const depFieldNames = field.dependence.map(el => el.dependenceId)
          const hasDepField = !!template.find(el => depFieldNames.includes(el.name))
          if (hasDepField) {
            list = dependenceList
          }
        } else {
          list = dependenceList
        }
      }
      bus
        .invoke('element:ref:select:payee', selectedPayeeId, list, isDependence, isFeeDetailPayeeId, {
          filterRules: field.filterRules
        })
        .then(data => {
          if (allowClear) {
            onChange?.(this.fnGetValue(data))
          } else {
            data && onChange?.(this.fnGetValue(data))
          }
        })
    }
  }

  // 档案关系取值
  currentIdx = null
  handleDependenceChange = ({ key, id, dependenceFeeType = false }, options = {}) => {
    const { field, flowId, value, onChange, billState, isNewCreate, detailId, form } = this.props
    const { isDependence, dependenceMap } = this.state
    const { dependence, dataType, dependenceCondition } = field
    // 非单据新建及费类新增的时候
    const isNotNewBillAndFee = billState !== 'new' || (!isNewCreate && detailId)
    const isInitLoad = isNotNewBillAndFee && options?.isInit
    if (!isDependence || value?.multiplePayeesMode) return
    const isNeedFetch = dependence.find(v => v.dependenceId === key)
    if (!isNeedFetch) return
    const list = dependenceMap.map((v, i) => {
      // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
      const dependenceId = dependence[i].dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })
    this.currentIdx = id
    api
      .invokeService('@bill:get:dependenceList', {
        recordSearch: list,
        entity: dataType.entity,
        dependenceFeeType,
        flowId,
        dependenceCondition
      })
      .then(data => {
        const reqIdx = id // 闭包记录id
        let { items } = data
        if (this.currentIdx === reqIdx) {
          let newValue = undefined
          if (isInitLoad) {
            const fieldValue = form.getFieldsValue()
            // 初始化进来 当前value 值赋值
            newValue = fieldValue[field?.field] ?? undefined
            if (
              getBoolVariation('cyxq-75173-payee-dependence-optimize') &&
              !!newValue &&
              items?.length &&
              items.findIndex(v => v.id === newValue?.id) === -1
            ) {
              newValue = undefined
            }
            onChange(this.fnGetValue(newValue))
          } else {
            if (value && items.findIndex(v => v.id === value.id) === -1) {
              onChange?.(this.fnGetValue())
            }
            if (items.length === 1 && !useNewAutomaticAssignment()) {
              newValue = items[0]
              onChange?.(this.fnGetValue(items[0]))
            }
          }
          // TODO: 档案关系埋点
          let { billData, billSpecification, feeType, dataSource } = this.props
          const oldValue = this.props?.value
          let newBillData = billData
          let message = '单据上的档案关系赋值'
          if (feeType) {
            message = '明细上的档案关系赋值'
          } else {
            newBillData = dataSource
          }
          api?.logger?.info(message, {
            specificationId: billSpecification?.id,
            specificationName: billSpecification?.name,
            flowId: newBillData?.flowId || newBillData?.id,
            code: newBillData?.code || newBillData?.form?.code,
            sceneName: '档案关系',
            feeTypeId: feeType?.id,
            feeTypeName: feeType?.name,
            field: field?.field,
            dependField: key,
            oldValue,
            newValue
          })
          this.setState({
            dependenceList: items
          })
        }
      })
  }

  handleSetPayeeInfo = isMultiple => {
    const { bus, form } = this.props
    const { payeeComponent } = api.getState()['@bill'].payeeComponentData
    api.invokeService('@bill:set:payee:component:visibility', { visible: isMultiple, payeeComponent })
    bus.getFieldsValue().then(data => {
      let details = data?.details ?? []
      if (isMultiple) {
        details = details.map(detail => {
          if (!~detail.specificationId.components.findIndex(el => el.field === 'feeDetailPayeeId')) {
            detail.specificationId.components.push(payeeComponent)
          }
          return detail
        })
      } else {
        details = details.map(detail => {
          const index = detail.specificationId.components.findIndex(el => el.field === 'feeDetailPayeeId')
          if (index >= 0) {
            // 如果是程序是通过多收款人模式加的就去删除，否则是通过模板配置来的
            const feeDetailPayeeId = detail.specificationId.components[index]
            if (feeDetailPayeeId?.manualAdd) {
              detail.specificationId.components.splice(index, 1)
              delete detail.feeTypeForm.feeDetailPayeeId
            }
          }
          return detail
        })
      }
      form.setFieldsValue({ details })
      setTimeout(() => bus.emit('bill:edit:form:changed:should:save', false), 300)
    })
  }

  handleChange = (payeesMode, payPlans) => {
    const { bus, onChange, isModifyBill, flowAllowModifyFields, defaultPayeeInfo, value, form } = this.props
    const canEditPayPlan =
      !isModifyBill || (isModifyBill && (!flowAllowModifyFields || !!~flowAllowModifyFields.indexOf('payPlan')))
    const { multiplePayeesMode, payPlanMode } = payeesMode
    if (multiplePayeesMode) {
      const formValue = form.getFieldsValue()
      bus.emit(
        'payPlan:visible:change',
        {
          onChange,
          showPayPlan: true,
          canEditPayPlan,
          payPlans,
          ...payeesMode
        },
        formValue?.details
      )
      this.handleSetPayeeInfo(!payPlanMode)
    } else {
      const { id, multiplePayeesMode } = value ?? {}
      const { isDependence } = this.state
      const flag = (!id && !isDependence) || multiplePayeesMode
      if (flag && !isModifyBill) onChange(this.fnGetValue(defaultPayeeInfo))
      bus.emit('payPlan:visible:change', {
        onChange,
        showPayPlan: false,
        canEditPayPlan: false
      })
      this.handleSetPayeeInfo(false)
    }
  }

  handleSwitcherChange = ({ switcherCheck, payPlans, ...payeesMode }) => {
    this.handleChange({ multiplePayeesMode: switcherCheck, ...payeesMode }, payPlans)
  }

  openSelectCurrency = () => {
    const { allReceivingCurrency, receivingCurrency } = this.state
    const { dimensionCurrencyInfo, standardCurrency, onChange, value = {}, bus } = this.props
    const currentStandardCurrency = dimensionCurrencyInfo?.currency || standardCurrency || {}
    if (!receivingCurrency?.strCode) {
      return Dialog.alert({
        iconType: 'warn',
        content: i18n.get('暂无收款币种，请联系管理人员')
      })
    }
    api
      .open('@basic:SelectCurrency', {
        standardCurrency: allReceivingCurrency.find(v => v.numCode === currentStandardCurrency.numCode) || {},
        allCurrencyRates: allReceivingCurrency.filter(v => v.numCode !== currentStandardCurrency.numCode)
      })
      .then(e => {
        if (e?.numCode === receivingCurrency?.numCode) {
          return
        }
        this.setState({ receivingCurrency: e || {} })
        bus.setValidateLevel(0)
        onChange && onChange({ ...value, receivingCurrency: e?.numCode })
        this.checkDetails(e, false)
      })
  }

  checkDetails = (e, updateAmount = true) => {
    const { form, bus } = this.props
    const { isShowDialog } = this.state
    if (isShowDialog) return
    setTimeout(() => {
      const { details } = form?.getFieldsValue()
      bus.emit('payee:receivingCurrency:changed', { receivingCurrency: e, updateAmount })
      if (details?.length > 0) {
        this.setState({ isShowDialog: true })
        Dialog.alert({
          content: i18n.get('收款币种已更改，请检查金额'),
          onConfirm: () => {
            this.setState({ isShowDialog: false })
          }
        })
      }
    })
  }

  render() {
    const { payConfig, receivingCurrency } = this.state
    const { bus, value, field, isModifyBill, flowAllowModifyFields = [] } = this.props
    const { name, optional } = field
    let placeholder = getPlaceholder(field)
    const label = fnGetFieldLabel(field)
    placeholder = placeholder || i18n.get('请选择{__k0}', { __k0: label })
    placeholder = optional ? i18n.get('(选填)') + placeholder : placeholder
    const editable = !isModifyBill || (isModifyBill && !flowAllowModifyFields.length)
    const { multiplePayeesMode, payPlanMode, payeePayPlan } = value ?? {}

    return (
      <div style={{ display: 'flex', width: '100%' }}>
        <PayeeDetailItem
          bus={bus}
          dataSource={value}
          openSelectCurrency={this.openSelectCurrency}
          receivingCurrency={receivingCurrency}
          label={label}
          placeholder={placeholder}
          editable={editable}
          onClick={this.handleClick}
          payConfig={payConfig}
          allowMultiplePayees={payConfig.allowMultiplePayees && name !== 'feeDetailPayeeId'}
          multiplePayeesMode={multiplePayeesMode}
          payPlanMode={payPlanMode}
          payeePayPlan={payeePayPlan}
          onChange={this.handleChange}
        />
      </div>
    )
  }
}
