/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { app } from '@ekuaibao/whispered'
import { allowModifyField, attachmentValidator } from '../utils/validatorUtil'
import { Upload2QiNiu } from '../../elements/puppet/Upload'
import { fnClickAttachments, parseAsShowValue, uploadDone } from '../utils/fnAttachment'
import { wrapper } from '../layout/FormWrapper'
import { Toast, Dialog} from '@hose/eui-mobile'
import cloneDeep from 'lodash/cloneDeep'
import { isFunction } from '@ekuaibao/helpers'
import isEqual from 'lodash/isEqual'

import { FetchAttachment, fetchAttachmentAIResult } from '../utils/FetchAttachment'
import { parseAIResultValue, extractIdsFromArray, extractIdFromCityData, extractIdsFromCityArray, removeEmptyObjects  } from './parseAIResult'
import { getDataLinkEditTemplate } from '../../plugins/bill/bill.action'
import styles from './AIAttachment.module.less'

import { AIFillFormTracker } from '../../lib/aiFillFormTracker'

@EnhanceField({
  descriptor: {
    type: 'aiAttachments'
  },
  wrapper: wrapper(),
  validator(field, props) {
    return [attachmentValidator(field, props)]
  }
})
@FetchAttachment
export default class AIAttachment extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
       // 记录已标记AI填充的字段
      aiFilledFields: {}
    }
  }

  componentDidMount() {
    app.dataLoader('@common.uploadServiceUrl').load()
    const { bus } = this.props;
    if (bus) {
      // 监听表单值变化事件
      bus.on('bill:value:changed:forAIAttachment', this.handleFormValueChange);
    }
  }

  componentWillUnmount() {
    const { bus } = this.props;
    if (bus) {
      bus.un('bill:value:changed:forAIAttachment', this.handleFormValueChange);
    }
  }

  preSetValue(value) {
    return (value || []).map(parseAsShowValue)
  }

  handleChange = (list, needUpload = true) => {
    const { value = [], onChange } = this.props
    if (needUpload) {
      uploadDone({ list, value, onChange }, true)
    } else {
      onChange?.(value?.concat(list))
    }
  }

  handleDelete = (line, index) => {
    const { value, onChange } = this.props

    Dialog.confirm({
      title: i18n.get('确定删除该附件'),
      content: i18n.get('删除后无法撤回，请谨慎操作'),
      confirmText: i18n.get('删除'),
      confirmAction: { danger: true },
      cancelText: i18n.get('取消'),
      onConfirm: () => {
        if(line?.key){
          onChange?.(value.filter((v) => v.key !== line?.key))
        } else {
          onChange?.(value.filter((_v, i) => i !== index))
        }

        Toast.show({
          icon: 'success',
          content: i18n.get('删除成功')
        });
      }
    })
    
  }

  handleClick = (line, index) => {
    const { bus, value } = this.props
    fnClickAttachments({ bus, value, line, index })
  }

   // 表单值变化处理函数
   handleFormValueChange = (changedValues) => {
    if (!changedValues) return;
    console.log('handleFormValueChange changedValues', changedValues, this.state.aiFilledFields)
    const { aiFilledFields }  = this.state
    // 检查是否是AI填充的字段被修改
    Object.keys(changedValues).forEach(fieldName => {
      const aiInfo = aiFilledFields[fieldName];
      const changedValue = changedValues[fieldName]

      // 人员单选也会变更一次对象
      if(typeof changedValue === 'object'){
        const justId = changedValue?.id
        if (justId && isEqual(justId, aiInfo)) {
          return
        }
      }

      if (
        (changedValue?.id && aiInfo?.id && String(changedValue?.id) === String(aiInfo?.id)) ||
        (changedValue?.id && aiInfo && String(changedValue?.id) === String(aiInfo))
      ) {
        return;
      }

      if (Array.isArray(changedValue)) {
        const justIds = changedValue.map(item => item.id)
        if (justIds.length > 0 && isEqual(justIds, aiInfo)) {
          return;
        }
      }

      if (typeof changedValue === 'string' && Array.isArray(aiInfo)) {
        const justIds = changedValue.split(',')
        if (justIds.length > 0 && isEqual(justIds, aiInfo)) {
          return;
        }
      }

      // 业务对象的数据，过滤空数据对吧
      if(Array.isArray(changedValue) && changedValue.every(item => item.dataLinkTemplateId)){
        // 过滤掉dataLinkForm中的undefined值
        const changedValueWithOutEmpty = changedValue.map(item => ({
          ...item,
          dataLinkForm: Object.fromEntries(
            Object.entries(item.dataLinkForm || {}).filter(([, value]) => value !== undefined)
          )
        })).filter(item => Object.keys(item.dataLinkForm).length > 0);
        if (aiInfo && !isEqual(removeEmptyObjects(changedValueWithOutEmpty), removeEmptyObjects(aiInfo))) {
          this.removeAiMarkForField(fieldName, aiInfo, changedValueWithOutEmpty);
        }
      } else {
        // 只有当值与AI填充时不同时才移除标记
        if (aiInfo && !isEqual(removeEmptyObjects(changedValue), removeEmptyObjects(aiInfo))) {
          this.removeAiMarkForField(fieldName, aiInfo, changedValue);
        }
      }
    });
  }

  // 移除AI标记和高亮
  removeAiMarkForField(fieldKey, aiInfo, changedValues) {
    const { bus, flowId, feetypeId, template } = this.props
    const curFieldObj = template.find(item => item.field === fieldKey)
    try{
      const trackParams = {
        form_id: flowId || bus.newFormId,
        field_id:fieldKey,
        field_name: curFieldObj?.label || curFieldObj?.cnLabel,
        original_ai_value:aiInfo,
        user_modified_value:changedValues
      }
      console.log(this.props,'AIFillFormTracker.trackAIFillFormModify',trackParams)
      AIFillFormTracker.trackAIFillFormModify(trackParams)
    }catch(error){
      console.error('AIFillFormTracker.trackAIFillFormModify error', error)
    }
    try {
      // 移除AI图标
      const iconElement = document.querySelector(`#${feetypeId ? `feetype-info-editable-${fieldKey}-ai-icon` : `${fieldKey}-ai-icon`} `);
      if (iconElement) {
        iconElement.remove();
      }
    } catch (error) {
      console.error('移除AI标记失败:', error);
    }
  }

   // 添加AI样式表
   addAiStyles = () => {
    if (!document.getElementById('ai-styles')) {
      const styleEl = document.createElement('style');
      styleEl.id = 'ai-styles';
      styleEl.textContent = `
        .ai-filled-field input, 
        .ai-filled-field .ant-select-selector,
        .ai-filled-field .ant-picker,
        .ai-filled-field textarea {
          border: 1px solid var(--eui-primary-pri-500) !important;
          transition: all 0.3s;
        }
        
        .ai-icon {
          display: inline-flex;
          margin-left: 4px;
          vertical-align: middle;
          color: var(--eui-primary-pri-500);
          position: relative;
          top: -1px;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        .ai-icon svg {
          animation: fadeIn 0.5s;
        }
      `;
      document.head.appendChild(styleEl);
    }
  }

  // 为字段添加AI图标和高亮
  addAiIconAndHighlight = (fieldName) => {
    const { feetypeId } = this.props
    try {
      const domkey = feetypeId ? `#feetype-info-editable #${fieldName}` : `#${fieldName}`
      const iconContainerId = feetypeId ? `feetype-info-editable-${fieldName}-ai-icon` : `${fieldName}-ai-icon`
      // 查找字段元素
      const fieldElement = document.querySelector(domkey);
      if (!fieldElement) return;

      // 查找标签元素
      const labelElement = fieldElement.querySelector('.vertical-label');
      const dataLinkFieldElement = fieldElement.querySelector('.label-style')
      if (!labelElement && !dataLinkFieldElement) return;

      // 检查是否已存在AI图标
      if (!document.getElementById(iconContainerId)) {
        // 创建AI图标元素
        const iconContainer = document.createElement('span');
        iconContainer.id = iconContainerId;
        iconContainer.className = 'ai-icon';

        // 渲染React组件到DOM元素
        this.renderIconToElement(iconContainer);

        // 添加到标签后面
        labelElement && labelElement.appendChild(iconContainer);
        dataLinkFieldElement && dataLinkFieldElement.appendChild(iconContainer);
      }
    } catch (error) {
      console.error('添加AI图标失败:', error);
    }
  }
  // 渲染React组件到DOM元素
  renderIconToElement = (element) => {
    try {
      // 使用React DOM渲染TwoToneGeneralAiSummary组件
      const ReactDOM = require('react-dom');
      ReactDOM.render(
        <div data-ai-icon-wrapper className={styles['ai-icon-wrapper']}>
          <TwoToneGeneralAutofill fontSize={16} />
          <span>AI填写</span>
        </div>,
        element
      );
    } catch (error) {
      // 如果无法使用React DOM渲染，回退到简单的HTML
      element.innerHTML = `<div data-ai-icon-wrapper class="ai-icon-wrapper-html">
        <svg id="outlined-general/signature" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" focusable="false" aria-hidden="true" style="max-width: 100%; max-height: 100%;"><path d="M8.57155 31.7219C8.67172 31.0677 8.93264 30.4487 9.33091 29.9202L27.1302 6.29967C28.4597 4.53537 30.9677 4.18289 32.732 5.51239L34.7651 7.04441C36.5294 8.37391 36.8819 10.8819 35.5524 12.6462L17.7531 36.2667C17.3548 36.7952 16.8316 37.2167 16.2304 37.4933L10.4155 40.1687C8.96831 40.8345 7.36146 39.6236 7.60261 38.049L8.57155 31.7219Z" stroke="url(#paint0_linear_35_53505)" stroke-width="4" stroke-linecap="round" stroke-dasharray="0 0 0 95.33"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,95.3301773071289;            0,47.66508865356445,47.66508865356445,0;            95.3301773071289,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></path><path d="M35.1234 36.5242L31.4038 35.0762C31.2844 35.0295 31.1823 34.9496 31.1103 34.8467C31.0384 34.7439 31 34.6228 31 34.4988C31 34.3749 31.0384 34.2538 31.1103 34.1509C31.1823 34.0481 31.2844 33.9682 31.4038 33.9215L35.1234 32.4724C35.471 32.3373 35.7414 32.0628 35.8623 31.7196L37.3872 27.4209C37.431 27.2981 37.5136 27.1915 37.6235 27.116C37.7334 27.0406 37.865 27 38 27C38.135 27 38.2666 27.0406 38.3765 27.116C38.4864 27.1915 38.569 27.2981 38.6128 27.4209L40.1366 31.7207C40.2586 32.0628 40.5279 32.3373 40.8766 32.4735L44.5962 33.9215C44.7156 33.9682 44.8177 34.0481 44.8897 34.1509C44.9616 34.2538 45 34.3749 45 34.4988C45 34.6228 44.9616 34.7439 44.8897 34.8467C44.8177 34.9496 44.7156 35.0295 44.5962 35.0762L40.8766 36.5242C40.5279 36.6604 40.2586 36.9338 40.1366 37.277L38.614 41.5768C38.5707 41.7001 38.4883 41.8073 38.3784 41.8832C38.2684 41.9592 38.1365 42 38.0012 42C37.8658 42 37.7339 41.9592 37.6239 41.8832C37.514 41.8073 37.4316 41.7001 37.3883 41.5768L35.8634 37.277C35.8036 37.1079 35.7066 36.9531 35.5792 36.8232C35.4517 36.6933 35.2966 36.5914 35.1246 36.5242H35.1234Z" stroke="url(#paint1_linear_35_53505)" stroke-width="3" stroke-linecap="round" stroke-dasharray="0 0 0 44.734"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,44.734230041503906;            0,22.367115020751953,22.367115020751953,0;            44.734230041503906,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></path><path d="M5.52955 16.0798L4.52494 15.7089C4.36991 15.6521 4.23709 15.5544 4.14354 15.4283C4.04999 15.3022 4 15.1535 4 15.0013C4 14.8491 4.04999 14.7004 4.14354 14.5743C4.23709 14.4482 4.36991 14.3505 4.52494 14.2937L5.52955 13.9245C6.11157 13.7094 6.56104 13.2755 6.76465 12.7349L7.22182 11.5103C7.27633 11.3617 7.38079 11.2324 7.5204 11.1409C7.66001 11.0493 7.82771 11 7.99976 11C8.17182 11 8.33952 11.0493 8.47913 11.1409C8.61874 11.2324 8.72319 11.3617 8.77771 11.5103L9.23487 12.7349C9.43656 13.2755 9.88796 13.7094 10.47 13.9245L11.4727 14.2937C11.6283 14.3502 11.7617 14.4479 11.8557 14.5742C11.9498 14.7005 12 14.8496 12 15.0022C12 15.1548 11.9498 15.3038 11.8557 15.4302C11.7617 15.5565 11.6283 15.6542 11.4727 15.7107L10.47 16.0781C9.88796 16.2933 9.43656 16.7289 9.23487 17.2677L8.77579 18.4923C8.72084 18.6403 8.61632 18.7689 8.47694 18.8599C8.33756 18.951 8.17034 19 7.9988 19C7.82726 19 7.66004 18.951 7.52066 18.8599C7.38128 18.7689 7.27677 18.6403 7.22182 18.4923L6.76465 17.2677C6.66459 17.0003 6.50249 16.7556 6.28937 16.5504C6.07625 16.3451 5.8171 16.184 5.52955 16.0781V16.0798Z" fill="url(#paint2_linear_35_53505)" stroke-dasharray="0 0 0 24.296"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,24.29570960998535;            0,12.147854804992676,12.147854804992676,0;            24.29570960998535,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></path><circle cx="25" cy="41" r="2" fill="url(#paint3_linear_35_53505)" stroke-dasharray="0 0 0 11.314"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,11.313708305358887;            0,5.656854152679443,5.656854152679443,0;            11.313708305358887,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></circle><defs><linearGradient id="paint0_linear_35_53505" x1="5.80318" y1="41.9975" x2="42.8162" y2="18.7391" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient><linearGradient id="paint1_linear_35_53505" x1="30.6957" y1="33.331" x2="45.3479" y2="34.569" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient><linearGradient id="paint2_linear_35_53505" x1="3.82609" y1="14.3765" x2="12.19" y2="15.1337" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient><linearGradient id="paint3_linear_35_53505" x1="22.913" y1="40.6883" x2="27.095" y2="41.0668" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient></defs></svg>
        <span>AI填写</span>
      </div>`;
    }
  }

// 统一的字段过滤条件
  isValidField = (item) => 
    item.hide === false && 
    item.editable === true && 
    item.defaultValue?.type !== 'formula'

  isValidFieldDataLink = item =>
    item.dataType?.type === 'list'
    && item.type === "dataLinkEdits"
    && ["INSERT", "MORE"].includes(item.behaviour)  // 支持添加业务对象
    && item.hide === false  // 隐藏的
    && item.editable === true  // 系统计算的
    && item.defaultValue?.type !== 'formula'   // 自动计算的

  // 构建字段映射函数
  buildFieldInfo = (item, type, isArray = false, labelKey = 'label') => ({
    label: item[labelKey] || item.label,
    type,
    isArray
  })

  buileFieldFilter = item => {
    // 基础字段类型
    if (['text', 'date', 'dateRange', 'number', 'money'].includes(item.dataType?.type)) {
      return this.buildFieldInfo(item, item.dataType?.type, false, 'cnLabel')
    }

    // 自定义档案字段 (单选)
    if (item.type?.startsWith('ref:basedata.Dimension')) {
      return this.buildFieldInfo(item, item.type.replace(/^ref:/, ''), false, 'cnLabel')
    }

    // 自定义档案字段 (多选)
    if (item.type?.startsWith('list:ref:basedata.Dimension')) {
      return this.buildFieldInfo(item, item.type.replace(/^list:ref:/, '').replace(/:select$/, ''), true, 'cnLabel')
    }

    // 员工字段 (单选)
    if (item.type === 'ref:organization.Staff') {
      return this.buildFieldInfo(item, 'organization.Staff', false, 'cnLabel')
    }

    // 员工字段 (多选)
    if (item.type === 'list:ref:organization.Staff') {
      return this.buildFieldInfo(item, 'organization.Staff', true, 'cnLabel')
    }

    // 枚举字段
    if (item.type?.startsWith('ref:basedata.Enum')) {
      return this.buildFieldInfo(item, item.type.replace('ref:', ''), false, 'cnLabel')
    }

    // 部门字段
    if (item.type === 'ref:organization.Department') {
      return this.buildFieldInfo(item, 'organization.Department', false, 'cnLabel')
    }

    // 城市字段
    if (item.type === 'city') {
      return this.buildFieldInfo(item, 'basedata.city', item.multiple ?? false, 'cnLabel')
    }

    // 收款信息字段
    if (item.type === 'payeeInfo') {
      return this.buildFieldInfo(item, 'payeeInfo', false, 'cnLabel')
    }

    return null
  }

  buileFieldFilterDataLink = item => {
    // 基础字段类型
    if (['text', 'date', 'dateRange', 'number', 'money'].includes(item.type)) {
      return this.buildFieldInfo(item, item.type)
    }

    // 自定义档案字段 (单选)
    if (item.type === 'ref' && item.entity?.startsWith('basedata.Dimension')) {
      return this.buildFieldInfo(item, item.entity, false)
    }

    // // 自定义档案字段 (多选)
    // if (item.type === 'ref' && item.entity?.startsWith('basedata.Dimension')) {
    //   return this.buildFieldInfo(item, item.entity, true)
    // }

    // 员工字段 (单选)
    if (item.type === 'ref' && item.entity === 'organization.Staff') {
      return this.buildFieldInfo(item, 'organization.Staff', false)
    }

    // 员工字段 (多选)
    if (item.type === 'list' && item?.elemType?.type === 'ref' && item?.elemType?.entity === 'organization.Staff') {
      return this.buildFieldInfo(item, 'organization.Staff', true)
    }

    // 枚举字段
    if (item.type === 'ref' && item.entity?.startsWith('basedata.Enum')) {
      return this.buildFieldInfo(item, item.entity, false)
    }

    // 部门字段
    if (item.type === 'ref' && item.entity === 'organization.Department') {
      return this.buildFieldInfo(item, 'organization.Department', false)
    }

    // 城市字段
    if (item.type === 'ref' && item.entity === 'basedata.city') {
      return this.buildFieldInfo(item, 'basedata.city', true)
    }

    // 业务对象
    if (item.type === 'ref' && item.entity?.startsWith('datalink.DataLinkEntity')) {
      return this.buildFieldInfo(item, item.entity, false)
    }

    // 收款信息字段
    if (item.type === 'ref' && item.entity?.startsWith('pay.PayeeInfo')) {
      return this.buildFieldInfo(item, item.entity, false)
    }

    return null
  }

  handleAIResult = async (line) => {
    const event_click_time = Date.now()
    const { bus, flowId, feetype, billSpecification, field } = this.props
    try {
      bus.has('ai:summary:button:click') && bus.emit('ai:summary:button:click')
      const trackParams = {
        form_id: flowId || bus.newFormId,
        file_id: line?.response?.fileId?.id || line?.response?.key,
        form_type: billSpecification?.type,
        form_template_id: billSpecification?.id,
        form_template_name: billSpecification?.name,
        form_expense_id: feetype?.id,
        form_expense_name: feetype?.name,
        file_name: line?.name,
        file_type: line?.name?.split('.')?.pop(),
      }
      AIFillFormTracker.trackAIFillFormSummary(trackParams)
    } catch (error) {
      console.error('AIFillFormTracker.trackAIFillFormSummary error', error)
    }
    // 收集所有支持的字段类型
    const fields = this.props.template
      .filter(this.isValidField)
      .map(this.buileFieldFilter)
      .filter(Boolean)
    
    // 新增业务对象多选字段支持
    const datalinkFieldMap = {}
    
    this.props.template
      .filter(this.isValidFieldDataLink)
      .forEach(item => {
        datalinkFieldMap[item.cnLabel.trim()] = item.referenceData.fields
          .filter(item => !item.formula)
          .map(this.buileFieldFilterDataLink)
          .filter(Boolean)
      })

    if(!line?.response?.key){
      return null
    }

    let errorMessage = ''
    const result = await fetchAttachmentAIResult(line.response.key, fields, datalinkFieldMap, billSpecification?.id).catch(error => {
      console.error('获取AI结果失败', error)
      errorMessage = error.msg || error.errorMessage;
      Toast.show({
        content: i18n.get(error.msg || error.errorMessage || 'AI 提取失败，请重试'),
        type: 'error'
      })
      return null
    })
    // 业务对象字段类型
    const dataLinkTypeMap = {}
    Object.keys(datalinkFieldMap).forEach(key => {
      dataLinkTypeMap[key] = 'list:dataLinkEdits'
    })
    const typeMap = fields.reduce((acc, item) => {
      acc[item.label] = item.type
      return acc
    }, dataLinkTypeMap)


    try {
      const event_display_time = Date.now()
      const processing_duration_ms = event_display_time - event_click_time
      const fieldsLength = fields?.length || 0
      const datalinkFieldLength = (typeof datalinkFieldMap === 'object' && datalinkFieldMap !== null) ? Object.keys(datalinkFieldMap)?.length : 0
      const extracted_fields_count = fieldsLength + datalinkFieldLength
      const trackParams = {
        form_id: flowId || bus.newFormId,
        file_id: line?.response?.fileId?.id || line?.response?.key,
        form_type: billSpecification?.type,
        form_template_id: billSpecification?.id,
        form_template_name: billSpecification?.name,
        form_expense_id: feetype?.id,
        form_expense_name: feetype?.name,
        file_name: line?.name,
        file_type: line?.name?.split('.')?.pop(),
        recognition_status: result?.value ? 'success' : 'failed',
        error_msg: errorMessage,
        extracted_fields_list: result?.value,
        is_apply_all: field?.autoFill,
        processing_duration_ms,
        extracted_fields_count,
      }
      console.log(this.props,'AIFillFormTracker.trackAIFillFormSummaryResult',trackParams)
      AIFillFormTracker.trackAIFillFormSummaryResult(trackParams)
    } catch (error) {
      console.error('AIFillFormTracker.trackAIFillFormSummaryResult error', error)
    }

    return {
      result: result?.value,
      typeMap,
      datalinkFieldMap,
      fields,
      errorMessage
    }
  }

  parseDataLinkForm = async (result, typeMap = {}, template) => {
    const { fields, id } = template.referenceData
    const { subTypeId, behaviour, showTemplateId, importMode } = template
    console.log('parseDataLinkForm result', result, typeMap, fields)
    const res = await getDataLinkEditTemplate({
      id,
      type: ["INSERT", "MORE"].includes(behaviour),
      tableTemplateId: showTemplateId
    }).payload
    const items = subTypeId ? res.items.filter(i => i?.entity?.id === subTypeId) : res.items
    if (!Array.isArray(result)) {
      if(typeof result === 'object' && result !== null){
        result = [result]
      }else{
        return []
      }
    }
    const valueArray = result?.map(item => {
      const valueItem = {
        dataLinkForm: {},
        dataLinkId: null,
        dataLinkTemplateId: items[0]?.templateId
      }
      const fieldsMapArray = Object.keys(item).map(key => {
        const field = fields.find(item => item.label === key)
        if (field && field.name) {
          const val = item[key]
          const type = typeMap[key]
          if (!val) return null
          if (type === 'dateRange' && (!val?.start && !val?.end)) {
            return null
          }
          const value = parseAIResultValue(val, type)
          if(!value){
            return null
          }

          return {
            key: field.name,
            value,
          }
        }
        return null
      }).filter(Boolean)
      fieldsMapArray.forEach(item => {
        valueItem.dataLinkForm[item.key] = item.value
      })
      return valueItem
    }) || []
    if (importMode === "SINGLE") {
      return valueArray?.slice(0, 1)
    }
    return valueArray
  }

  handleApplyAIResult = async (result, typeMap = {}, datalinkFieldMap = {}, fields = []) => {
    const arrayMapForLabel = fields?.reduce?.((acc, item) => {
      acc[item.label] = item.isArray
      return acc
    }, {})

    const noResult = Object.keys(result).every(key => {
      let value = result[key]
      const type = typeMap?.[key]
      if (!value || value?.length === 0) return true
      if (type === 'dateRange' && (!value?.start && !value?.end)) {
        return true
      }
      if(type === 'money' &&  value?.standard === '') return true
      if (type === 'list:dataLinkEdits' && Array.isArray(value)) {
        // value = value.filter(item => Object.values(item).filter(Boolean).length > 0)
        // if (value.length === 0) return true
        value = value.map(item => {
          Object.keys(item).forEach(childKey => {
            const dataLinkItem = datalinkFieldMap[key]?.find?.(item => item.label === childKey)
            // 处理数组字段
            if (Array.isArray(item[childKey])) {
              item[childKey] = item[childKey].filter(x => x?.id || x?.key);
            }

            // 处理城市字段
            if (!dataLinkItem?.isArray && ['basedata.city'].includes(dataLinkItem?.type)) {
              item[childKey] = extractIdFromCityData(item[childKey])
            }

            if (dataLinkItem?.isArray && ['basedata.city'].includes(dataLinkItem?.type)) {
              item[childKey] = extractIdsFromCityArray(item[childKey])
            }

            // 单选字段
            if (!dataLinkItem?.isArray && (
              ['organization.Staff', 'organization.Department', 'payeeInfo', 'pay.PayeeInfo'].includes(dataLinkItem?.type) ||
              dataLinkItem?.type?.startsWith('basedata.Dimension') ||
              dataLinkItem?.type?.startsWith('basedata.Enum')
            )) {
              item[childKey] = item[childKey]?.id ? item[childKey] : undefined
            }

            // 多选字段
            if (dataLinkItem?.isArray && (
              dataLinkItem?.type?.startsWith('basedata.Dimension') ||
              ['organization.Staff'].includes(dataLinkItem?.type)
            )) {
              item[childKey] = extractIdsFromArray(item[childKey])
            }
          });
          return item;
        }).filter(item => Object.values(item).filter((value => {
          // 检查值是否为空
          if (value === null || value === undefined) return false;
          if (typeof value === 'boolean') return value; // 布尔值为true时不为空
          if (Array.isArray(value)) return value.length > 0; // 数组不为空
          if (typeof value === 'object') return Object.keys(value).length > 0; // 对象不为空
          if (!Array.isArray(value) && typeof value === 'object') return value?.key || value?.id; // id或key不为空
          return value !== ''; // 字符串不为空
        })).filter(Boolean).length > 0)
        if (value.length === 0) return true
      }
      return false
    })
    if (noResult) {
      Dialog.alert({
        title: i18n.get('请重试'),
        content: i18n.get('当前Al 提取字段均为无内容，无法自动填单，请换附件重试'),
      })
      return false
    }

    // 首先收集所有需要处理的字段
    const fieldsToProcess = Object.keys(result).map(key => {
      const template = this.props.template.find(item => item.enLabel.trim() === key || item.cnLabel.trim() === key)
      if (!template || !template.field) return null

      const fieldValue = this.props.form.getFieldValue(template.field)
      console.log('handleApplyAIResult fieldValue', fieldValue, this.props)

      const type = typeMap?.[key]
      let noValue = !fieldValue 
      if(type === 'money' && fieldValue?.standard === ''){
        noValue = true
      }

      if (type === 'list:dataLinkEdits' && Array.isArray(fieldValue)) {
        noValue = !(fieldValue?.filter(item => {
          const formValues = Object.values(item?.dataLinkForm || {});
          // 检查是否所有值都为空
          return formValues.some(value => {
            // 检查值是否为空
            if (value === null || value === undefined) return false;
            if (typeof value === 'boolean') return value; // 布尔值为true时不为空
            if (Array.isArray(value)) return value.length > 0; // 数组不为空
            if (typeof value === 'object') return Object.keys(value).length > 0; // 对象不为空
            return value !== ''; // 字符串不为空
          });
        })?.length > 0);
      }

      if (type === 'organization.Staff' && arrayMapForLabel?.[key]) {
        noValue = !fieldValue?.length
      }

      if (type?.startsWith('basedata.Dimension') && arrayMapForLabel?.[key]) {
        noValue = !fieldValue?.length
      }

      if (type === 'payeeInfo') {
        noValue = !fieldValue?.id
      }

      // 只处理未填写的字段
      if (!noValue) return null

      let value = result[key]
      if (!value || value?.length === 0) return null
      if (type === 'dateRange' && (!value?.start && !value?.end)) {
        return null
      }
      if(type === 'money' &&  value?.standard === '') return null
      if (type === 'list:dataLinkEdits' && Array.isArray(value)) {
        // value = value.filter(item => Object.values(item).filter(Boolean).length > 0)
        // if (value.length === 0) return null
        value = value.map(item => {
          Object.keys(item).forEach(childKey => {
            const dataLinkItem = datalinkFieldMap[key]?.find?.(item => item.label === childKey)
            // 处理数组字段
            if (Array.isArray(item[childKey])) {
              item[childKey] = item[childKey].filter(x => x?.id || x?.key);
            }

            // 处理城市字段
            if (!dataLinkItem?.isArray && ['basedata.city'].includes(dataLinkItem?.type)) {
              item[childKey] = extractIdFromCityData(item[childKey])
            }

            if (dataLinkItem?.isArray && ['basedata.city'].includes(dataLinkItem?.type)) {
              item[childKey] = extractIdsFromCityArray(item[childKey])
            }

            // 单选字段
            if (!dataLinkItem?.isArray && (
              ['organization.Staff', 'organization.Department', 'payeeInfo', 'pay.PayeeInfo'].includes(dataLinkItem?.type) ||
              dataLinkItem?.type?.startsWith('basedata.Dimension') ||
              dataLinkItem?.type?.startsWith('basedata.Enum')
            )) {
              item[childKey] = item[childKey]?.id ? item[childKey] : undefined
            }

            // 多选字段
            if (dataLinkItem?.isArray && (
              dataLinkItem?.type?.startsWith('basedata.Dimension') ||
              ['organization.Staff'].includes(dataLinkItem?.type)
            )) {
              item[childKey] = extractIdsFromArray(item[childKey])
            }
          });
          return item;
        }).filter(item => Object.values(item).filter((value => {
          // 检查值是否为空
          if (value === null || value === undefined) return false;
          if (typeof value === 'boolean') return value; // 布尔值为true时不为空
          if (Array.isArray(value)) return value.length > 0; // 数组不为空
          if (typeof value === 'object') return Object.keys(value).length > 0; // 对象不为空
          if (!Array.isArray(value) && typeof value === 'object') return value?.key || value?.id; // id或key不为空
          return value !== ''; // 字符串不为空
        })).filter(Boolean).length > 0)

        value = value.map(item => {
          const res = { ...item }
          Object.keys(res).forEach(resKey => {
            const resValue = res[resKey]
            // 处理空数组
            if (Array.isArray(resValue)) {
              const finalVal = resValue.filter(item => !(item && typeof item === 'object' && Object.keys(item).length === 0))?.map(item => item?.name)?.filter(Boolean)
              if (finalVal.length === 0) {
                delete res[resKey]
              }
            }
            // 处理空对象
            if (resValue && typeof resValue === 'object' && !Array.isArray(resValue) && Object.values(resValue).filter(Boolean).length === 0) {
              delete res[resKey]
            }
          })
          return res
        })

        if (value.length === 0) return null
      }

      // 单选城市
      // if (type === 'basedata.city') {
      //   value = extractIdFromCity(value, arrayMapForLabel?.[key])
      // }


      if (!arrayMapForLabel?.[key] && ['basedata.city'].includes(type)) {
        value = extractIdFromCityData(value)
      }

      if (arrayMapForLabel?.[key] && ['basedata.city'].includes(type)) {
        value = extractIdsFromCityArray(value)
      }

      // 单选字段
      if (!arrayMapForLabel?.[key] && (
        ['organization.Staff', 'organization.Department', 'payeeInfo'].includes(type) ||
        type?.startsWith('basedata.Dimension') ||
        type?.startsWith('basedata.Enum')
      )) {
        value = value?.id ? value : undefined
      }

      // 多选字段
      if (arrayMapForLabel?.[key] && (
        type?.startsWith('basedata.Dimension') ||
        ['organization.Staff'].includes(type)
      )) {
        value = extractIdsFromArray(value)
      }

      return {
        field: template.field,
        value,
        type,
        template,
        key
      }
    }).filter(Boolean)

    // 处理业务对象字段
    const processedFields = []
    // 使用for...of循环处理异步操作
    for (const fieldInfo of fieldsToProcess) {
      try {
        let finalValue = fieldInfo.value

        // 如果是业务对象类型，需要异步处理
        if (fieldInfo.type === 'list:dataLinkEdits') {
          const childMap = {}
          // 业务对象字段类型
          datalinkFieldMap[fieldInfo.key]?.forEach(item => childMap[item.label] = item.type)
          console.log('handleApplyAIResult childMap', childMap, fieldInfo.template.referenceData.fields)

          // 异步处理业务对象数据
          finalValue = await this.parseDataLinkForm(
            result[fieldInfo.key],
            childMap,
            fieldInfo.template
          )
        }

        processedFields.push({
          field: fieldInfo.field,
          value: finalValue,
          type: fieldInfo.type
        })
      } catch (error) {
        console.error(`处理字段 ${fieldInfo.field} 时出错:`, error)
      }
    }

    console.log('handleApplyAIResult processedFields', processedFields)

    if (processedFields.length === 0) {
      Dialog.alert({
        title: i18n.get('请重试'),
        content: i18n.get('当前AI 提取字段已被填写，请删除字段内容后重试'),
      })
      return false
    }

    // 添加AI样式表
    this.addAiStyles();

    // 使用setTimeout并确保表单已加载
    setTimeout(() => {
      // 记录已填充的字段
      const newAiFilledFields = { ...this.state.aiFilledFields };

      // 逐个设置字段值而不是一次性设置所有
      processedFields.forEach(item => {
        try {
          const aiValue = parseAIResultValue(item.value, item.type)

          // 设置字段值
          this.props.form.setFieldsValue({
            [item.field]: aiValue
          });

          // 标记字段为AI填充
          newAiFilledFields[item.field] = aiValue

          // 添加高亮和图标
          if (aiValue) {            
            this.addAiIconAndHighlight(item.field);
          }
        } catch (error) {
          console.error(`设置字段 ${item.field} 时出错:`, error)
        }
      });

      // 设置报错level，然后只校验AI填单字段
      this.props.bus.setValidateLevel(0);
      isFunction(this.props.bus?.getValueWithValidate) && this.props.bus?.getValueWithValidate(processedFields.map(item => item.field)).then(res => {
        console.log('handleApplyAIResult Validate', res)
      }).catch(err => {
        console.log('handleApplyAIResult err', err)
      })

      Toast.show({
        icon: 'success',
        content: i18n.get('AI填写成功，请查看')
      })
      // 更新状态
      this.setState({ aiFilledFields: cloneDeep(newAiFilledFields) });
    }, 100)
  }

  render() {
    const { value, field, flowAllowModifyFields } = this.props
    const { name, label, ...others } = field
    const isEdit = allowModifyField(field, flowAllowModifyFields)
    const fileList = this.preSetValue(value)
    return (
      <Upload2QiNiu
        {...others}
        isEdit={isEdit}
        isRequire={!field.optional}
        label={label}
        fileList={fileList}
        onChange={this.handleChange}
        onDelete={this.handleDelete}
        onClick={this.handleClick}
        isHoseEUI
        isShowOptional={false}
        className='attachment-upload-wrapper'
        suffixesPath="BILL"
        attachmentField={field}
        suffixesFiled={name}
        useAI={true}
        autoApplyAIResult={field?.autoFill}
        onAIResult={this.handleAIResult}
        onApplyAIResult={this.handleApplyAIResult}
      />
    )
  }
}
