import { app, callback } from './index'
import { Fetch, FetchError, GET, POST } from '@ekuaibao/fetch'
import { uuid, sha1 } from '@ekuaibao/helpers'
import { session } from '@ekuaibao/session-info'
import { Endpoint } from '@ekuaibao/rpc'
import { QuerySelect } from 'ekbc-query-builder'
import { sourceCustomerUrl } from '@ekuaibao/lib/lib/enums'
import { fixWeixinPrint, removeLoading, toast, alert, getUrlParamString } from '../lib/util'
import resignationUrl from '../lib/enums.sourcechannelmap.resignationUrl'
import error from './error'
import { addNch5Code } from '../lib/nch5'
import { getV } from '../lib/help'
import {addRumAction} from '../lib/dataflux'
import { FMP_MONITOR_CONFIG_MAP, FMPMonitorOptions } from '../lib/FMPConfigMap'
import qs from 'qs'

const loginurl = '/api/ekbmall/v2/platform/login'
const geturl = '/api/ekbmall/v2/platform/url'

const ACTIONS = [
  'batchSubmitCustom',
  'batchSubmitExpense',
  'batchAgreeExpense',
  'batchAgreeCustom',
  'batchSubmitLoan',
  'batchAgreeLoan',
  'batchSubmitRequisition',
  'batchPayRequisition',
  'batchAgreeRequisition',
  'batchAddnodeExpense',
  'batchAddnodeLoan',
  'batchAddnodeRequisition'
]

export const params: { [key: string]: any } = qs.parse(location.search.slice(1))

export function setFetchParams() {
  if (params?.accessToken && params?.corpId) {
    Fetch.accessToken = params.accessToken as string
    Fetch.corpId = params.corpId as string
    Fetch.ekbCorpId = params.corpId as string
  }
}

function getMessageCode(): Promise<string> {
  return new Promise((resolve, reject) => {
    resolve('debug')
  })
}

function getOpenURL(sourceKey: string, isDingTalk = false, pushMessageCode?: string) {
  const { accessToken } = Fetch
  return GET(loginurl, isDingTalk ? { accessToken, pushMessageCode } : { accessToken }).then((res: any) => {
    GET(geturl).then((resurl: any) => {
      const HOST = `${resurl.value}/buy/#`
      let openURL = `${HOST}/flight?token=${encodeURIComponent(res.token)}`
      if (sourceKey) {
        openURL = `${HOST}/flight/order/${sourceKey}?token=${encodeURIComponent(res.token)}`
      }
      return (location.href = openURL)
    })
  })
}

export async function checkUrl(platform: string) {
  if (!params || !params.source) {
    return ''
  }

  if (params.roleDefId) {
    alert('请于电脑端查看', '档案关系')
    return Promise.reject(new Error(i18n.get('请于电脑端查看档案关系')))
  }
  if (params.source === 'esignature') {
    alert('请于电脑端查看')
    return Promise.reject(new Error(i18n.get('请于电脑端查看')))
  }
  // 处理微信打印后，不能记住页面状态的问题
  fixWeixinPrint(() => {
    if (params.source === 'printRemind') {
      app.emit('refresh:messageListView')
    }
  })
  // 钉钉消息推送查看单据详情
  if (params?.isChangeSource) {
    try {
      const result = await Fetch.GET('/api/flow/v2/backlogs/query/backlogId/$' + params.flowId)
      if (result?.value) {
        params.source = result?.value?.source ? result?.value?.source : ''
        params.backlogId = result?.value?.backlogId ? result?.value?.backlogId : ''
      }
    } catch (err) {
      toast.fail(err)
    }
  }
  if (params.source === 'applyEventDetail') {
    // 申请事项消息  // 和message-center 里一样，但是不知道为何会报一个莫名错误，所以改成这种形式
    let param = {
      join: `expenseInfoList.id,flow,/flow/v1/flows`,
      // join$1: `id,requisitionDetail,/flow/v1/flows?join=form.specificationId,specificationId,/form/v1/specificationVersions`,
      join$2: 'specificationId,specification,/form/v1/specificationVersions',
      join$3: `changeLogs.operatorId,operatorId,/v1/organization/staffs`,
      join$4: `changeLogs.fromStaff,fromStaff,/v1/organization/staffs`,
      join$5: `changeLogs.toStaffs,toStaffs,/v1/organization/staffs`,
      join$6: `ownerId,ownerId,/v1/organization/staffs`
    }
    let query = new QuerySelect()
    query = query.value()
    Fetch.POST('/api/form/v2/requisition/my', param, { body: query }).then(res => {
      const line = res?.items?.find((v: any) => v.id === params.flowId)
      app.invokeService('@requisition:save:current:requisition', line).then(_ => {
        app.go('/requisitionDetail/' + true)
      })
    })
    return
  }
  const sourceKey = params.source.split('-')
  switch (sourceKey[0]) {
    case 'requisition_info_list':
      return '#/requisition'
    case 'trip_trip_remind': // 出行提醒
      return `#/messageRedirect/${encodeURIComponent('http://cn.mikecrm.com/td0kMCg')}`
    case 'batchSubmitPermit':
    case 'batchAgreePermit':
    case 'batchRemindPermit':
      return '#/batchPermit'
    case 'message_center_weekly_report': // 管理周报
      return `#/messageRedirect/${encodeURIComponent('http://cn.mikecrm.com/CRihhTf')}`
    case 'mall_booking_remind': // 机票预订
      return GET(loginurl).then((res: any) => {
        GET(geturl).then((resurl: any) => {
          location.href = `${resurl.value}/buy/#/flight?token=${encodeURIComponent(res.token)}`
        })
      })
    case 'mall_auto_login':
      if (platform === 'dingtalk') {
        getMessageCode().then(pushMessageCode => {
          getOpenURL(sourceKey[1], true, pushMessageCode)
        })
      } else {
        getOpenURL(sourceKey[1])
      }
  }

  const fnGetFlow = () => {
    const flowId = decodeURIComponent(params?.flowId)?.includes('$')
      ? params?.flowId
      : encodeURIComponent(params.flowId)
    return GET('/api/flow/v1/flows/$' + flowId)
      .then((result: any) => {
        const flow = result.value
        const processed = params.logId + 1 < flow.logs.length
        if (flow.state === 'draft') {
          return `#/bill/${flow.formType}/` + encodeURIComponent(params.flowId)
        }
        const message = flow.state !== 'rejected' ? '/message' : ''
        return (
          (processed || flow.state !== 'rejected' ? '#/detail/' : `#/rejected/${flow.formType}/`) +
          params.flowId +
          message
        )
      })
      .catch(err => {
        error(err?.errorMessage || err?.message || err?.msg)
      })
  }

  const staffId = session?.user?.get('staff') && session?.user?.get('staff')?.id
  if (params.source === 'workrecord') {
    const result = await GET('/api/flow/v3/backlogs/current/$' + encodeURIComponent(params.flowId))
    if (result?.value?.id) {
      return `#/approve/approving/${result?.value?.type}/${result?.value?.id}/null/null/${result?.value?.flowId || params.flowId}`
    } else {
      return `#/message/detail/workrecord/${encodeURIComponent(params.flowId)}`
    }
  } else if (params.source === 'DSLMessage') {
    return `#/blockUIMessage/${params.type}/${encodeURIComponent(params.sourceId)}`
  } else if (params.source === 'flow') {
    if (!params.flowId) {
      error(i18n.get('参数错误'))
    } else {
      return fnGetFlow()
    }
  } else if (params.source === 'backlog') {
    const backlogId = decodeURIComponent(params?.backlogId)?.includes('$')
      ? params?.backlogId
      : encodeURIComponent(params.backlogId)
    return Promise.all([
      GET('/api/flow/v2/backlogs/$' + backlogId),
      POST('/api/flow/v2/backlogs/count', null, {
        body: { filterBy: '(state.in("APPROVING","PAYING"))' }
      })
    ])
      .then(result => {
        const backlog = result[0].value
        const count = result[1].count
        if (platform === 'feishu' && backlog.state === 'APPROVING') {
          //添加返回到审批列表的处理，当审批列表为一个的时候不会返回到审批列表，当审批列表大于1个的时候会返回到审批列表
          const u1 = `#/thirdParty/${platform}/approve/approving/${backlog.type}/${backlogId}/null/${backlog.flowId}`
          const backUrl = backlog.type === 'permit' ? '#/batchPermit' : '#/approvemessage'
          // const x = count > 1 ? [backUrl, u1] : u1
          const x = [backUrl, u1]
          return x
        }
        const u1 = `#/approve/approving/${backlog.type}/${backlogId}/null/null/${backlog.flowId}`
        const u2 = '#/detail/' + encodeURIComponent(params.flowId) + '/message'
        const backUrl = backlog.type === 'permit' ? '#/batchPermit' : '#/approvemessage'
        const x = backlog.state === 'PROCESSED' ? (count > 1 ? [backUrl, u2] : u2) : count > 1 ? [backUrl, u1] : u1
        return x
      })
      .catch(err => {
        console.error(err)
        return fnGetFlow()
      })
  } else if (params.source === 'repaymentApply') {
    window.$_privilegeId = params.privilegeId
    return GET('/api/v1/loan/repayment/$' + encodeURIComponent(params.repaymentApplyId)).then((result: any) => {
      const repayment = result.value
      if (repayment.ownerId === staffId) {
        return repayment.state === 'APPROVE' || repayment.state === 'AGREE'
          ? '#/repaymentApply/' + encodeURIComponent(params.repaymentApplyId)
          : '#/loanpackagedetail/' + encodeURIComponent(repayment.loanInfoId)
      }
      return '#/repaymentApply/' + encodeURIComponent(params.repaymentApplyId)
    })
  } else if (params.source === 'loanInfo') {
    return POST('/api/v1/loan/loanInfo/isCancel/$id', {
      id: params.loanInfoId
    })
      .then((result: any) => {
        if (result?.value?.isCancel) {
          error('已取消共享此借款包')
        } else {
          return '#/loanpackagedetail/' + encodeURIComponent(params.loanInfoId)
        }
      })
      .catch(err => {
        error(err?.errorMessage || err?.message || err?.msg)
      })
  } else if (params.source === 'batchPayExpense') {
    return '#/home/<USER>/expense'
  } else if (params.source === 'batchPayLoan') {
    return '#/home/<USER>/loan'
  } else if (params.source === 'batchPayRequisition') {
    return '#/home/<USER>/' + encodeURIComponent('requisition')
  } else if (params.source === 'batchAgreeRequisition') {
    if (params.operation === 'approving') {
      return '#/approvemessage'
    }
    return '#/home/<USER>/' + encodeURIComponent('requisition')
  } else if (ACTIONS.indexOf(params.source) > -1) {
    return `#/approvemessage`
  } else if (
    params.source === 'batchRejectExpense' ||
    params.source === 'batchRejectLoan' ||
    params.source === 'batchRejectRequisition'
  ) {
    return '#/rejectedmessage/' + params.source
  } else if (
    params.source === 'batchRemindExpense' ||
    params.source === 'batchRemindRequisition' ||
    params.source === 'batchRemindLoan'
  ) {
    return '#/approvemessage'
  } else if (params.source === 'printRemind') {
    if (params.flowId) {
      return '#/detail/' + encodeURIComponent(params.flowId)
    }
    return '#/home/<USER>/printList'
  } else if (params.source === 'order_writeOff_remind') {
    return '#/message/bill/expense/true'
  } else if (params.source && params.source.indexOf('tpp_remind') > -1) {
    return '#/didierp/message'
  } else if (params && params.source === 'comment') {
    let { flowId } = params
    return GET('/api/flow/v2/carbonCopy/byFlowId/$' + `${encodeURIComponent(flowId)}`).then((result: any) => {
      let url = ''
      let value = result && result.value
      if (value) {
        //抄送单据获取详情
        let carbonCopyId = value.id
        let state = value.state
        url = `#/detail/${encodeURIComponent(flowId)}/${state}/carbonCopy/${encodeURIComponent(carbonCopyId)}`
      } else {
        url = '#/detail/' + encodeURIComponent(flowId)
      }
      return url
    })
  } else if (params.source === 'dimission') {
    if (platform === 'kdcloud') {
      let url = resignationUrl.KD_CLOUD
      location.replace(Fetch.prefixOrigin(url))
      return { hash: undefined as any, isNeedPushState: false }
    } else {
      let url = location.origin + '/applet/' + resignationUrl.DING_TALK
      if (platform === 'dingtalk') {
        if (history.replaceState) {
          // 钉钉Android端内置浏览器location.replace不兼容
          history.replaceState(null, document.title, url)
          history.go(0)
          return { hash: undefined, isNeedPushState: false }
        }
      }
      return location.replace(url)
    }
  } else if (params.source === 'nonCompleteRoute') {
    if (window.isPC) {
      toast.info(i18n.get('请前往「移动端」查看用车补贴未完行程'), 6000)
      return Promise.reject(new Error(i18n.get('请前往「移动端」查看用车补贴未完行程')))
    }
    return '#/recordingtrip'
  } else if (params.source === 'budgetalert') {
    return '#/reports5'
  } else if (params.source === 'assistPlatform') {
    return `#/assistance/${encodeURIComponent(params.assistId)}`
  } else if (params.source === 'exportManage') {
    return '#/mine/exportManage'
  } else if (params.source === 'amortizePlanList') {
    return `#/message/pc/${params.source}`
  } else {
    return Promise.reject(new Error(i18n.get('参数错误')))
  }
}

export function startupApp(preHashs: string | string[] | any) {
  preHashs && app.use(require('../plugins/messageRedirect').default)

  if (preHashs && Array.isArray(preHashs) && preHashs.length) {
    preHashs = preHashs.map(hash => ({
      pathname: hash.slice(1),
      search: location.search,
      state: (location as any).state
    }))
  } else if (preHashs && typeof preHashs === 'string') {
    preHashs = [preHashs.slice(1)]
  }

  preHashs &&
    app.useHistory({
      initialEntries: preHashs,
      initialIndex: preHashs.length
    })

  callback()

  // 加载阿里人机校验代码
  addNch5Code()

  removeLoading()
}

export let fastClick: any = null

export function startupIndex(platform?: string, fn?: Function) {
  callback()
    .then(() => {
      if (fn) {
        fn()
      }
      /// 关闭fastclick插件，理由：此插件会引起部分安卓机型【已知vivo】h5链接跳转功能失效。影响：第三方集成ekb的定制项目中ios版app如果采用已废弃的UIWebview老内核【如技术落后的钉钉】在iPhone上的点击反应很慢。
      // if (/iphone/.test(navigator.userAgent.toLowerCase())) {
      //   fastClick = attach(document.body)
      // }
    })
    .finally(() => {
      removeLoading()
    })
  // 加载阿里人机校验代码
  addNch5Code()

  window.history.pushState(null, '', window.location.hash)
}

export function goto(path: string) {
  if (location.hash !== path) {
    const params = Fetch.makeUrlParams({ accessToken: null })
    location.replace('?' + params + '#' + path)
  }
}
/**
 * @description 系统权限
 * @param key
 * @returns
 */
export function permissionName(key: string) {
  let config: { [key: string]: any } = {
    SYS_ADMIN: i18n.get('系统管理'),
    REPORT_VIEW: i18n.get('费用报表管理'),
    BUDGET_MANAGE: i18n.get('预算管理'),
    LOAN_MANAGE: i18n.get('借款管理'),
    EXPENSE_MANAGE: i18n.get('报销单管理'),
    REQUISITION_MANAGE: i18n.get('申请管理'),
    CUSTOM_REPORT_VIEW: i18n.get('高级报表'),
    SETTLEMENT_MANAGE: i18n.get('结算方式管理'),
    TRIP_MANAGE: i18n.get('差旅管理'),
    CORP_WALLET_MANAGE: i18n.get('企业钱包管理'),
    VIRTUAL_CARD_MANAGE: i18n.get('易商卡管理'),
    INVOICE_MANAGE: i18n.get('票据管理'),
    SUPPLEMENT_INVOICE: i18n.get('补充发票'),
    SMART_REPORT: i18n.get('智能报表管理'),
    AUDIT_ADMIN: i18n.get('审计日志'),
    BANK_ACCOUNT_MANAGE: i18n.get('账户管理'),
    RECEIPT_ACCOUNT: i18n.get('账户管理-收款账户'),
    PAYMENT_ACCOUNT: i18n.get('账户管理-付款账户'),
    PAYMENT_WORKBENCH: i18n.get('支付数据管理'),
    // PAYMENT_MANAGE: i18n.get('支付管理'),
    CREDIT_MANAGE: i18n.get('信用管理'),
    MALL_MANAGE: i18n.get('商城管理'),
    INVOICE_REVIEW: i18n.get('发票复核管理'),
    INVOICE_DISCOUNT: i18n.get('进项抵扣管理'),
    CONSUMER_MATTERS: i18n.get('自动报销管理'),
    RECEIPT_MANAGE: i18n.get('收款单管理'),
    CHECKING_BILL_MANAGE: i18n.get('对账结算管理'),
    RESEARCHERS_ACTIVATE: i18n.get('人员激活管理'),
    CORP_PAYMENT_MANAGE: i18n.get('对公付款管理'),
    CONTACTS_MANAGE: i18n.get('通讯录管理'),
    KA_TOBACCO_CHECKING_MANAGER: i18n.get('部门或科室对账'),
    EXTEND_DIMENSION: i18n.get('扩展档案管理'),
    CORP_AGENCY_EXPENSE: i18n.get('企业代报销'),
    FLOW_ADMIN: i18n.get('审批流管理'),
    ROLE_ADMIN: i18n.get('角色管理'),
    INVOICE_REVIEW_QUICK_EXPENSE: i18n.get('发票复核管理（费用明细）')
  }

  return config[key] as string
}

// extract from @ekuaibao/lib/lib/enums
const PLATFORM_CHANNELID_MAP = {
  DEBUGGER: '2',
  DING_TALK: '3',
  KD_CLOUD: '8',
  WEIXIN: '4',
  APP: '2',
  HUAWEI: '9',
  THIRDPARTY: '2',
  FEISHU: '5',
  UNIAPP: '2',
  HYBRID: '2'
};

// 获取客服链接代码
export function getCustomerServiceUrlOfZHICHI(user: any, sourceId: string) {
  const { HELP_SERVICE } = window.getCorpStyle()
  // 企业品牌化设置，有在线客服并且使用的是自定义设置
  if (!!HELP_SERVICE?.custom && !!HELP_SERVICE?.visibility) {
    return HELP_SERVICE.customValue
  }
  const permissions = user.permissions || []
  const staff = user.staff || {}
  const admin = !!~permissions.indexOf('SYS_ADMIN') ? i18n.get('管理员') : i18n.get('用户')
  const name = staff.name

  const corp = staff.corporationId && staff.corporationId.name
  const c_name = name + i18n.get('（app/') + admin + i18n.get('）')
  const web_token = staff.id
  const key = 'bf62151d7cce4b1f881c8bf898bab775'
  const nonce = uuid(32)
  const timestamp = +new Date()
  let signature = `nonce=${nonce}&timestamp=${timestamp}&web_token=${web_token}&${key}`
  signature = sha1(signature)
  signature = signature.toUpperCase()
  const customerUrl = sourceCustomerUrl[window.__PLANTFORM__]

  const corpId = staff.corporationId && staff.corporationId.id
  const cellphone = staff.cellphone
  const permissionNames = permissions.map((item: any) => permissionName(item)).join(',')
  return (
    customerUrl +
    '&web_plugin_id=' +
    sourceId +
    '&nonce=' +
    nonce +
    '&timestamp=' +
    timestamp +
    '&signature=' +
    signature +
    '&web_token=' +
    web_token +
    '&c_name=' +
    encodeURIComponent(c_name) +
    '&c_org=' +
    encodeURIComponent(corp) +
    '&partnerid=' +
    web_token +
    '&uname=' +
    encodeURIComponent(name) +
    '&realname=' +
    encodeURIComponent(corp) +
    '&tel=' +
    cellphone +
    // '&params=' + encodeURIComponent(JSON.stringify({'费控权限': permissionNames, '公司名称': corp})) +
    '&customer_fields=' +
    encodeURIComponent(
      JSON.stringify({
        customField4: permissionNames,
        customField6: corp,
        // customField7: cellphone,
        customField8: corpId
      })
    )
  )
}


const getCNmae = (user: any) => {
  const permissions = user.permissions || []
  const staff = user.staff || {}
  const admin = !!~permissions.indexOf('SYS_ADMIN') ? i18n.get('管理员') : i18n.get('用户')
  const name = staff.name

  return encodeURIComponent(name + i18n.get('（app/') + admin + i18n.get('）'))
}


export const getServiceUrl = async (ops: any): Promise<string> => {
  return Fetch.GET('/api/message/v1/online/customer', ops)
}

export const getCustomerServiceUrl = async (user: any, sourceId: string, channelid?: number) => {
  try {
    const res = await getServiceUrl({
      type: 0,
      channelId: channelid || PLATFORM_CHANNELID_MAP[window.__PLANTFORM__],
      webPluginId: sourceId,
      cName: getCNmae(user),
    })
    // @ts-ignore
    return res?.value?.url || getCustomerServiceUrlOfZHICHI(user, sourceId)
  } catch {
    return getCustomerServiceUrlOfZHICHI(user, sourceId)
  }
}

export function getAskBotCustomerServiceUrl(user: any) {
  const staff = user.staff || {}
  const userName = staff.name
  const userId = staff.id
  const companyName = staff.corporation && staff.corporation.name
  // 平台
  const source = window.__PLANTFORM__
  const timestamp = +new Date()
  const appId = 5001
  const id = 199
  const apiKey = '9c5b9583655d42afab6a0037c09fa994'
  const secretKey = '4e5463784e546b774d4449334e7a51314d54677a'
  return (
    'https://ekuaibao-mall.guoranbot.com/?appId=' +
    appId +
    '&userId=' +
    userId +
    '&userName=' +
    encodeURIComponent(userName) +
    '&companyName=' +
    encodeURIComponent(companyName) +
    '&source=' +
    source +
    '&timestamp=' +
    timestamp +
    '&apiKey=' +
    apiKey +
    '&id=' +
    id +
    '&secretKey=' +
    secretKey
  )
}
//从url中获取参数
export function filterFromUrl(name = '', defaultValue: string = '') {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  const r = decodeURIComponent(window.location.search)
    .substr(1)
    .match(reg)
  if (r != null) return unescape(r[2])
  return defaultValue
}

function go2RedirectUrl(error: FetchError) {
  return new Promise<void>(resolve => {
    if (error.status === 401) {
      app?.logger?.error('AuthError401', { error, corpId: Fetch.ekbCorpId, pathname: location.pathname })
      const urlState = qs.parse(location.search.slice(1))
      if (urlState.overdueTokenRedirect) {
        location.href = urlState.overdueTokenRedirect as string
      } else {
        resolve()
      }
    } else if (error.status === 700) {
      const hash = getV(location, 'hash', '')
      const isMCError = hash.includes('/mc-error')
      if (!isMCError) {
        app.go('/mc-error')
      }
    } else if (error.status === 701) {
      const hash = getV(location, 'hash', '')
      const isSharedError = hash.includes('/shared-error') || hash.includes('/login')
      if (!isSharedError) {
        app.go('/shared-error')
      }
    } else {
      resolve()
    }
  })
}

export function fetchHandleError(action?: (error: FetchError) => void) {
  Fetch.handleError = function (err: FetchError) {
    go2RedirectUrl(err).then(() => {
      action && action(err)
    })
  }
}

export function getJumpPage(): string | undefined {
  const noPathCache = params && (params['noPathCache'] as string)
  if (!!noPathCache) {
    return location.hash.replace('#', '')
  }
  const pageType = params && (params['pageType'] as string)
  const flowId = params && (params['flowId'] as string)
  const requisitionId = params && (params['requisitionId'] as string)
  const pagePathMap: Record<string, string> = {
    home: '/home5',
    reports: '/reports5',
    reportsBudget: '/reports5/reports5-3',
    reportsEnterprise: '/reports5/reports5-2',
    recordExpends: '/record-expends',
    billList: '/billList',
    mine: '/mine',
    paying: '/paying',
    approve: '/approve',
    requisition: '/requisition',
    loanpackage: '/loanpackage',
    billDetail: '/billDetail',
    billDraft: `/bill/requisition/${flowId}`,
    new: '/home5Create',
    wCar: '/mallIframe/wCar',
    wmtaxi: `/mallIframe/wmtaxi`,
    wflight: `/mallIframe/wflight`,
    wtrain: `/mallIframe/wtrain`,
    whotel: `/mallIframe/whotel`,
    wInfo: `/mallIframe/wInfo`,
    wOrder: `/mallIframe/wOrder`,
    dingTalkServiceGroup: '/mine/dingtalkservicegroup',
    userPicker: '/userPicker/true',
    startApply: `/startApply/${flowId}`,
    login: '/login5',
    newRequisition: '/home5Create',
    assistPlatform: `/assistance/${encodeURIComponent(params?.assistId as string)}`,
    requisitionEvent: '/requisitionDetailInit/' + requisitionId,
    expenseTracker: '/record-expends'
  }
  if (pageType) {
    app.use(require('../plugins/messageRedirect').default)
    const route = pagePathMap[pageType]
    if (route === '/billDetail') {
      setTimeout(() => initMallEndpoint(), 2000)
      return `${route}/${flowId}/mall`
    } else if (route === '/home5Create') {
      setTimeout(() => initMallEndpoint(), 2000)
      return `${route}/${params?.tppBusinessType || null}`
    } else if (pageType === 'billDraft') {
      setTimeout(() => initMallEndpoint(), 2000)
      return route
    } else if (pageType === 'requisitionEvent') {
      setTimeout(() => initMallEndpoint(), 2000)
      return route
    }
    return route
  }
  return undefined
}

export function initMallEndpoint() {
  console.log('%c [ DEBUGGER ]- initMallEndpoint', 'font-size:13px; background:pink; color:green;')
  window.endpoint = new Endpoint({
    'Mall:backControl': (param: any) => {
      if (app.history?.index === 0) {
        window.endpoint?.invoke('Mall:close')
      } else {
        app.go(-1)
      }
    }
  })
  Endpoint.connect(window, window.parent)
  window?.endpoint?.listen()
  window.addEventListener('beforeunload', () => {
    window?.endpoint?.unlisten()
  })
}

export function isOPG() {
  return location.origin.includes('opg.cn')
}

/**
 * 检查是否登录过集团版APP
 * 登录过：直接跳转到集团APP
 * 没登录过：不做操作
 * formGroupApp有值时，不跳转至集团APP，维持原生原逻辑
 */
export const checkGroupAPPLogin = () => {
  // 上次是否登陆到集团版易快报
  const wereRedirectToGroupApp = localStorage.getItem('wereRedirectToGroupApp')
  // 是否从集团APP中返回
  const { formGroupApp } = params
  if (wereRedirectToGroupApp && formGroupApp) {
    localStorage.setItem('wereRedirectToGroupApp', '')
    session.remove('user')
  } else if (wereRedirectToGroupApp) {
    // 上次登录集团APP的用户，跳转至集团APP
    if (window.GROUP_URL) {
      location.href = window.GROUP_URL
    }
  }
}

/**
 * 集团版APP登录
 */
export const initGroupUser = () => {
  const user = session.get('user') || {}
  const token = getUrlParamString(location.search, 'accessToken') || user.accessToken
  const corpId = getUrlParamString(location.search, 'corpId') || user.corpId
  const params =
    !corpId || corpId === 'undefined'
      ? { accessToken: token }
      : { corpId: decodeURIComponent(corpId), accessToken: token }
  session.set('user', params)
}

/**
 * 检测是否是集群环境登录
 */
export const checkClusterLogin = () => {
  const isFromCluster = getUrlParamString(location.search, 'isFromCluster')
  const route = window.location.hash.split('/')
  if ((isFromCluster || window.PLATFORMINFO?.loginEnv === 'MC') && route.includes('login5')) {
    session.remove('user')
  }
}

/**
 * 集群环境登录
 */
export const initClusterUser = () => {
  if (window.PLATFORMINFO?.clusterURL) {
    const user = session.get('user') || {}
    const token = getUrlParamString(location.search, 'accessToken') || user.accessToken
    const corpId =
      getUrlParamString(location.search, 'ekbCorpId') || getUrlParamString(location.search, 'corpId') || user.corpId
    const params =
      !corpId || corpId === 'undefined'
        ? { accessToken: token }
        : { corpId: decodeURIComponent(corpId), accessToken: token }
    session.set('user', params)
  }
}

/**
 * 检查是否登录过轻共享新环境
 * 登录过：直接跳转到轻共享新环境
 * 没登录过：不做操作
 * formSharedApp有值时，不跳转至轻共享新环境，维持原生原逻辑
 */
export const checkSharedAPPLogin = () => {
  // 上次是否登陆到轻共享新环境
  const wereRedirectToSharedApp = localStorage.getItem('wereRedirectToSharedApp')
  // 是否从轻共享新环境中返回
  const { fromSharedApp } = params
  if (wereRedirectToSharedApp && fromSharedApp) {
    localStorage.setItem('wereRedirectToSharedApp', '')
    localStorage.setItem('SHARED_URL', '')
    session.remove('user')
  } else if (wereRedirectToSharedApp) {
    // 上次登录轻共享新环境的用户，跳转至轻共享新环境
    const SHARED_URL = localStorage.getItem('SHARED_URL')
    if (SHARED_URL) {
      location.href = SHARED_URL
    }
  }
}

/**
 * 轻共享新环境登录
 */
export const initSharedApp = () => {
  const fromApp: any = getUrlParamString(location.search, 'fromApp') || localStorage.getItem('fromApp')
  if (window.PLATFORMINFO?.platform === 'SHARED' && fromApp) {
    window.isSharedApp = true
    localStorage.setItem('fromApp', 'true')
    initGroupUser()
  }
}

/**
 * 判断企业ID中是否含有～GP
 */
export const isExistGP = () => {
  const corpId = getUrlParamString(location.search, 'ekbCorpId') || getUrlParamString(location.search, 'corpId') || ''
  const existGP = corpId.includes('~GP') || Fetch.ekbCorpId.includes('~GP')
  return existGP
}

/**
 * 在集团版中时，跳转至原生APP的登录页
 */
export const backToLogin5 = () => {
  session.remove('user')
  const logoutUrl = window.PLATFORMINFO?.logoutUrl
  const groupLogoutUrl = window.PLATFORMINFO?.groupLogoutUrl
  if (window.inGroupApp && groupLogoutUrl) {
    location.href = `${groupLogoutUrl}/applet/app.html?formGroupApp=true#/login5`
  } else if (localStorage.getItem('from_HS_SAML_Login') === 'true') {
    // 使用红杉SAML登录后，退出登录时，需要退回红杉登录页面中
    location.href = '/applet/hsSAML.html'
  } else if (window.isSharedApp && logoutUrl) {
    window.isSharedApp = false
    localStorage.removeItem('fromApp')
    location.href = `${logoutUrl}/applet/app.html?fromSharedApp=true#/login5`
  } else {
    app.go('/login5', true)
  }
}

export async function redirect(params: any) {
  switch (params['pageType']) {
    case 'approve':
      app.use(require('../plugins/messageRedirect').default)
      return '/approve'
    case 'form':
      app.use(require('../plugins/messageRedirect').default)
      const { backlogId, flowId } = params
      if (params['action'] === 'freeflow.mention' || params['action'] === 'freeflow.carbonCopy') {
        return '/detail/' + flowId
      }

      if (flowId && !backlogId) {
        return '/detail/' + flowId
      }

      if (flowId) {
        // 先查询当前待办是否存在
        return GET(`/api/flow/v3/backlogs/current/$${flowId}`).then(response => {
          const id = response?.value?.id // 待办ID
          if (!id?.length) {
            // 如果不存在直接跳转详情
            return '/detail/' + flowId
          }
          const state = response?.value?.state
          const type = response?.value?.type
          if (state !== 'PROCESSED') {
            return `/thirdParty/fromThirdParty/approve/approving/${type}/${id}/approving/${response?.value?.flowId}`
          }

          return '/detail/' + flowId
        })
      }

      return GET('/api/flow/v2/backlogs/$' + encodeURIComponent(backlogId)).then((result: any) => {
        const backlog = result.value
        if (backlog.state !== 'PROCESSED') {
          return `/thirdParty/fromThirdParty/approve/approving/${backlog.type}/${backlogId}/approving/${backlog.flowId}`
        }

        return '/detail/' + backlog.flowId
      })
    case 'delegator':
      return '/mine/delegator-list'
    case 'new':
      app.use(require('../plugins/messageRedirect').default)
      const { specificationOriginalId } = params
      return `/thirdpartNewBill/${specificationOriginalId}`
    case 'expenseTracker':
      return '/record-expends'
  }
  return undefined
}

const monitorFMP = (options: Omit<FMPMonitorOptions, 'rule'>) => {
  if (!('MutationObserver' in window)) {
    return
  }

  const {
    isFMPNode,
    params,
  } = options

  try {
    let timeoutId: NodeJS.Timeout
    let mutationObserver: MutationObserver

    if(document.visibilityState === 'hidden') return

    const handleVisibilityChange = () => {
      if(document.visibilityState === 'hidden') {
        cleanup()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('pagehide', handleVisibilityChange)

    
    mutationObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        for (const node of Array.from(mutation.addedNodes)) {
          if (node.nodeType !== Node.ELEMENT_NODE) continue
          
          if (isFMPNode(node as Element)) {
            const args = typeof params === 'function' ? params() : params
            addRumAction('first_meaning_paint', {
              ...args,
              loadedDuration: Math.floor(performance.now())
            })
            console.log(args.title, 'FMP')

            cleanup()
            return
          }
        }
      }
    })

    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    })

    // 设置超时断开连接
    timeoutId = setTimeout(() => {
      cleanup()
    }, 60 * 1000)

    const cleanup = () => {
      mutationObserver?.disconnect()
      clearTimeout(timeoutId)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('pagehide', handleVisibilityChange)
    }

  } catch (error) {}
}

export const trackFMP = (hash: string | string[]) => {
  if(!hash) return

  if(typeof hash === 'string') {
    hash = hash.replace('#', '')
  } else if(Array.isArray(hash)) {
    hash = hash[1]?.replace('#', '')
  }

  const config = Object.values(FMP_MONITOR_CONFIG_MAP).find(config => config.rule(hash))

  if (config) {
    monitorFMP({ isFMPNode: config.isFMPNode, params: config.params })
  }
}

/**
 * 注册钉钉性能监控日志
 */
export const registerDingtalkPerformance = () => {
  try {
    if (process.env.NODE_ENV === 'production' && location.hostname === 'dd2.hosecloud.com') {
      window.__BIRD_CONFIG = window.__BIRD_CONFIG || {}
      window.__BIRD_CONFIG.tags = {
        isvAppId: '2297' // isv应用id
      }

      const reportPerformance = () => {
        setTimeout(() => {
          const { send } = window.__BIRD || {}

          window.performance && window.performance.mark('FMP')
          typeof send === 'function' && send()
        }, 500) // 延迟500ms确保脚本初始化
      }

      if (document.readyState === 'complete') {
        // 页面已加载完成，直接执行
        reportPerformance()
      } else {
        // 页面还在加载，等待load事件
        window.addEventListener('load', reportPerformance)
      }
    }
  } catch(e) {}
}