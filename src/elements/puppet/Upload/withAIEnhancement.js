import React from 'react'
import { useAIEnhancement } from './hooks/useAIEnhancement'
import { enableAIAttachmentCharge } from '../../../lib/charge-util'

/**
 * 高阶组件 - 为类组件提供AI增强功能
 * @param {React.Component} WrappedComponent - 要增强的组件
 * @returns {React.Component} 增强后的组件
 */
export function withAIEnhancement(WrappedComponent) {
  function AIEnhancedComponent(props) {
    // 在每次渲染时动态检查特性开关
    const isAIEnabled = enableAIAttachmentCharge()

    if (!isAIEnabled) {
      // 如果AI功能未启用，直接渲染原组件
      return <WrappedComponent {...props} />
    }
    const {
      fileList = [],
      uploaderFileList = [],
      useAI,
      onAIResult,
      onApplyAIResult,
      autoApplyAIResult,
      ...restProps
    } = props

    // 合并文件列表
    const allFiles = [...fileList, ...uploaderFileList]

    // 使用AI增强Hook
    const aiEnhancement = useAIEnhancement(allFiles, {
      useAI,
      onAIResult,
      onApplyAIResult,
      autoApplyAIResult
    })

    return (
      <WrappedComponent
        {...restProps}
        fileList={fileList}
        uploaderFileList={uploaderFileList}
        useAI={useAI}
        onAIResult={onAIResult}
        onApplyAIResult={onApplyAIResult}
        autoApplyAIResult={autoApplyAIResult}
        aiEnhancement={aiEnhancement}
      />
    )
  }

  // 设置显示名称便于调试
  AIEnhancedComponent.displayName = `withAIEnhancement(${WrappedComponent.displayName || WrappedComponent.name})`

  // 复制静态方法
  if (WrappedComponent && typeof WrappedComponent === 'function') {
    Object.keys(WrappedComponent).forEach(key => {
      if (typeof WrappedComponent[key] === 'function') {
        AIEnhancedComponent[key] = WrappedComponent[key]
      }
    })
  }

  return AIEnhancedComponent
}

/**
 * 函数式组件形式的AI增强包装器
 * 可以直接在函数组件中使用
 */
export function AIEnhancedWrapper({ children, ...aiOptions }) {
  const aiEnhancement = useAIEnhancement(aiOptions.files || [], aiOptions)
  
  return typeof children === 'function' 
    ? children(aiEnhancement)
    : React.cloneElement(children, { aiEnhancement })
}