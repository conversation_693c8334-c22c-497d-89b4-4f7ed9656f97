import React from 'react'
import styles from './Upload.module.less'
import { Di<PERSON>, Input, But<PERSON>, AttachmentList } from '@hose/eui-mobile'
import { OutlinedDirectionUpload } from '@hose/eui-icons'
import PropTypes from 'prop-types'
import classNames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'
import UploadItem from './UploadItem'
import UploadWithConfig from './UploadWithConfig'
import SVG_ATTACHMENT from './images/attachment-img.svg'
import SVG_CAMERA from './images/camera-img.svg'
import { buildData, getUploadUrl, fnDingPanUpload, chooseFile } from '../../../components/utils/fnAttachment'
import TokenData from '@ekuaibao/lib/lib/token-data'
import { getV } from '@ekuaibao/lib/lib/help'
import { defaultInvalidSuffixes, onInvalidFile } from '../../../lib/invalidSuffixFile.tsx'
import loadable from '@loadable/component'
import { app } from '@ekuaibao/whispered'
import UploaderInstruction from './uploaderInstruction'
import { withAIEnhancement } from './withAIEnhancement'
import { enableAIAttachmentCharge } from '../../../lib/charge-util'

const FilesUploader = loadable(() => import('@ekuaibao/uploader/esm/FilesUploader'))
const HuaWeiUploader = loadable(() => import('@ekuaibao/uploader/esm/HuaWeiUploader'))

const noop = () => { }
const maxSize = 64

@EnhanceConnect(state => ({
  uploadServiceUrl: state['@common'].uploadServiceUrl,
  dpPower: state['@common'].powers.DP,
  useMultipartUpload: state['@common'].toggleManage?.['tg_file_upload_useMultipartUpload'],
  useCompress: state['@common'].toggleManage?.['tg_file_upload_useCompress']
}))
class Upload2QiNiu extends UploadWithConfig {
  token = null
  constructor(...args) {
    super(...args)

    this.state = {
      uploaderFileList: [],
      attachmentSetting: {},
      isChooseFileFromSDK: false,
    }
    this.handleChange = this.handleChange.bind(this)
    this.handleDone = this.handleDone.bind(this)
    this.tokenData = TokenData.getInstance()
  }

  componentWillUnmount() {
    super.componentWillUnmount()
    const fileServiceType = getV(this.props, 'uploadServiceUrl.fileServiceType')
    if (fileServiceType !== 'alioss') {
      this.tokenData.cleanToken()
    }
  }

  handleChange(uploaderFileList) {
    this.setState({ uploaderFileList })
  }

  handleDone(uploaderFileList) {
    this.setState({ uploaderFileList: [] }, () => {
      this.props.onChange?.(uploaderFileList)
    })
  }

  /**
   *
   * @param {File[]} invalidFiles // 错误文件列表
   * @param {'invilidaFileType' | 'otherInvalid'} type // 文件错误类型
   */
  handleInvalidFiles = (invalidFiles, type) => {
    let { invalidSuffixes = defaultInvalidSuffixes } = this.props
    onInvalidFile(invalidFiles, invalidSuffixes, type)
  }

  handleUploadPrepare = async () => {
    try {
      const fileServiceType = getV(this.props, 'uploadServiceUrl.fileServiceType')
      this.token = await this.tokenData.data(fileServiceType)
    } catch (e) {
      Dialog.alert({
        title: i18n.get('提示'),
        content: i18n.get('获取上传验证码失败,请稍候重试')
      })
      return Promise.reject(e)
    }
  }

  handleDingTalkUpload = async () => {
    const data = await fnDingPanUpload()
    if (data) {
      let { onChange } = this.props
      onChange && onChange(data, false)
    }
  }

  handleChooseFileFromSDK = async () => {
    return chooseFile()
  }

  handleFileUploadError = (error) => {
    app?.logger?.error('文件上传失败', { error })
  }

  renderAction() {
    const { ActionView, isHoseEUI } = this.props
    if (ActionView) {
      return <ActionView />
    }

    const UploadCameraAndFileView = () => {
      return (
        <div className="upload-wrapper">
          <img key="attachment" className="upload-img" src={SVG_ATTACHMENT} />
          <img key="camera" className="upload-img" src={SVG_CAMERA} />
        </div>
      )
    }

    if (isHoseEUI && window.__PLANTFORM__ !== 'WEIXIN') {
      return (
        <Button key="attachment" block className={styles['button-action']} category="secondary" theme="highlight" size='small' icon={<OutlinedDirectionUpload />}>
          {i18n.get('上传附件')}
        </Button>
      )
    }

    if (window.__PLANTFORM__ !== 'WEIXIN') {
      return (
        <div key="attachment" className="btn-upload">
          {i18n.get('上传附件')}
        </div>
      )
    }
    return UploadCameraAndFileView()
  }

  renderHeader = () => {
    const { isShowOptional = true, isEdit, isRequire, label = i18n.get('附件') } = this.props
    if (!isShowOptional) return null
    return (
      <div className={isEdit ? '' : 'header-1'}>
        {!isRequire && <span className="optional">{i18n.get('(选填)')}</span>}
        <span className="title">{label}</span>
      </div>
    )
  }

  renderWrapper = () => {
    const {
      uploadServiceUrl,
      dpPower,
      invalidSuffixes = defaultInvalidSuffixes,
      useCompress,
      useMultipartUpload,
      className = '',
      isHoseEUI,
    } = this.props
    const { isChooseFileFromSDK } = this.state

    if (dpPower) {
      const style = isHoseEUI ? { width: '100%' } : {}
      return <div style={style} onClick={this.handleDingTalkUpload}>{this.renderAction()}</div>
    }

    let accept = '/*'
    if (window.isAndroid) {
      if (window.isKdCloud || window.isFeishu || window.isDingtalk) {
        accept = '/*' //钉钉升级后需要这样写才能选到文件 6.5.40是这样的
      } else {
        accept = 'application/msexcel,application/msword,application/pdf,image/*,video/*'
      }
    } else {
      accept = this.getAccept() || accept
    }
    if (window.__PLANTFORM__ === 'APP' && window.isHarmonyOS && accept === '/*') {
      accept = 'image/*,video/*,audio/*,application/*'
    }
    const uploadUrl = uploadServiceUrl && uploadServiceUrl.uploadUrl
    const capture = window.isAndroid && window.isWelink ? 'camera' : undefined

    if (window.__PLANTFORM__ === 'HUAWEI' && window.isAndroid) {
      return (
        <HuaWeiUploader
          action={IS_STANDALONE ? getMinioUploadUrl() : uploadUrl}
          onChange={this.handleChange}
          onDone={this.handleDone}
          onStart={this.handleOnStart}
          data={file => buildData(file, this.token, uploadServiceUrl)}
        >
          {this.renderAction()}
        </HuaWeiUploader>
      )
    }

    const uploadParam = { isCompress: useCompress, useMultipartUpload }

    return (
      <FilesUploader
        bus={this.bus}
        accept={accept}
        capture={capture}
        action={IS_STANDALONE ? getUploadUrl : uploadUrl}
        type={IS_STANDALONE}
        maxSize={this.props.maxSize ? this.props.maxSize : maxSize}
        onChange={this.handleChange}
        onDone={this.handleDone}
        onStart={this.handleOnStart}
        data={file => buildData(file, this.token, uploadServiceUrl, false, uploadParam)}
        invalidSuffixes={invalidSuffixes}
        onInvalidFile={this.handleInvalidFiles}
        uploadPrepare={this.handleUploadPrepare}
        onHandleDingTalkUpload={this.handleDingTalkUpload}
        invalidSuffixesConfig={this.state?.attachmentSetting?.invalidSuffixesConfig}
        isChooseFileFromSDK={isChooseFileFromSDK}
        onChooseFileFromSDK={this.handleChooseFileFromSDK}
        onError={this.handleFileUploadError}
        className={className}
      >
        {this.renderAction()}
      </FilesUploader>
    )
  }

  getFileStatus = (item) => {
    const STATUS_MAP = {
      uploading: 'pending',
      done: 'success',
      error: 'fail',
    }

    return STATUS_MAP[item?.status] ?? item?.status
  }

  render() {
    const { cls, fileList, isEdit, onClick, onDelete, isHoseEUI, aiEnhancement, suffixesPath, attachmentField } = this.props
    const { uploaderFileList, attachmentSetting } = this.state
    const allFileList = fileList.concat(uploaderFileList)
    const attachmentList = enableAIAttachmentCharge() ? allFileList.map((item, index) => {
      const { buttonSlot, aiResultSlot } = aiEnhancement?.renderAISlots?.(item) || { buttonSlot: null, aiResultSlot: null }
      const status = this.getFileStatus(item)
      return {
        key: item.response?.key || (item.name + index),
        name: item.name,
        url: item.response?.key,
        status,
        progress: item?.progress?.percent ?? 0,
        actionSlot: status === 'success' ? () => buttonSlot : null,
        resultSlot: () => aiResultSlot
      }
    }) : []

    return (
      <div className={classNames(styles['upload-wrap'], { 'upload-wrap-disabled': !isEdit }, cls)}>
        {isEdit && (
          <div className="header upload_header_forFix">
            {this.renderHeader()}
            {isHoseEUI && window.__PLANTFORM__ !== 'WEIXIN' && <UploaderInstruction
              suffixesPath={suffixesPath}
              attachmentField={attachmentField}
              attachmentSetting={attachmentSetting}
            />}
          </div>
        )}

        {!isEdit && !allFileList.length ? (
          <Input value={i18n.get('无')} readOnly />
        ) : (
          <div className={`main ${isHoseEUI ? 'attachment-item-wrapper-eui' : ''}`}>

            {enableAIAttachmentCharge() ? <AttachmentList
              value={attachmentList}
              onDelete={(line) => onDelete?.(line)}
              onPreview={onClick}
              showDelete={isEdit}
            /> : allFileList.map((line, index) => {
              return (
                <UploadItem
                  key={index}
                  isEdit={isEdit}
                  file={line}
                  onRemoveItem={() => onDelete?.(line, index)}
                  onClickItem={() => onClick?.(line, index)}
                />
              )
            })}
          </div>
        )}

        {isEdit && this.renderWrapper()}
      </div>
    )
  }
}

Upload2QiNiu.propTypes = {
  isEdit: PropTypes.bool, //是否编辑状态
  isRequire: PropTypes.bool, //是否选填
  fileList: PropTypes.array, //已上传文件列表
  onRemoveAttachment: PropTypes.func, //移除附件
  onClick: PropTypes.func, //点击附件
  error: PropTypes.bool, // 要抛出的错误信息
  onErrorClick: PropTypes.func, //点击弹出相应的报错信息
  // AI相关props
  useAI: PropTypes.bool, // 是否启用AI功能
  onAIResult: PropTypes.func, // AI结果回调
  onApplyAIResult: PropTypes.func, // 应用AI结果回调
  autoApplyAIResult: PropTypes.bool, // 是否自动应用AI结果
  aiEnhancement: PropTypes.object, // AI增强功能对象
}

Upload2QiNiu.defaultProps = {
  isEdit: false,
  isRequire: false,
  fileList: [],
  onAddItem: noop,
  onRemoveItem: noop,
  error: false,
  useAI: false,
  autoApplyAIResult: false,
}

// 使用HOC包装组件，HOC内部会动态检查特性开关
export default withAIEnhancement(Upload2QiNiu) 

