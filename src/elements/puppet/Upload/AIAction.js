import React, { useMemo } from 'react'
import PropTypes from 'prop-types'
import { TwoToneGeneralAiSummary } from '@hose/eui-icons'
import { enableAIAttachmentCharge } from '../../../lib/charge-util'
import { Toast } from '@hose/eui-mobile'
import styles from './AIAction.module.less'

/**
 * AI操作按钮组件
 * 提供AI相关操作的用户界面
 */
export default function AIAction(props) {
  const {
    file,
    aiState,
    aiNotDisabled,
    onAIAction
  } = props

  const { AIPercent } = aiState

  const handleAIAction = async (e) => {
    e?.stopPropagation()
    e?.preventDefault()
    if(!aiNotDisabled){
      Toast.show({
        content: i18n.get('仅支持pdf、docx、png、jpg、jpeg格式')
      })
      return
    }
    
    try {
      await onAIAction?.(file)
    } catch (error) {
      console.error('AI操作失败:', error)
    }
  }

  // 计算是否显示AI按钮 - 对应hasAIButton逻辑
  const hasAIButton = useMemo(() => {
    return AIPercent === 0
  }, [AIPercent])

  // 如果不显示AI按钮，返回null
  if (!enableAIAttachmentCharge()) {
    return null
  }

  if(!hasAIButton){
    return <span className='suport-data-ai-summary'/>
  }

  return (
      <span 
        onClick={handleAIAction} 
        className={`suport-data-ai-summary ${aiNotDisabled ? styles["file-AI"] : styles["file-AI-disabled"]}`}
      >
        <TwoToneGeneralAiSummary />
        <span className={styles["file-AI-text"]}>AI 摘要</span>
      </span>
  )
}

AIAction.propTypes = {
  file: PropTypes.object.isRequired,
  aiState: PropTypes.object.isRequired,
  aiNotDisabled: PropTypes.bool.isRequired,
  onAIAction: PropTypes.func
}

AIAction.defaultProps = {
  onAIAction: () => {}
} 