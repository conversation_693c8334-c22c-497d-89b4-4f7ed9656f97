import styles from './Details.module.less'
import React, { createRef, PureComponent } from 'react'
import classNames from 'classnames'
import { Button, Checkbox, ActionPanel, List } from '@hose/eui-mobile'
import {
  OutlinedDirectionDown,
  OutlinedTipsDone,
  OutlinedDirectionRichtext,
  OutlinedDirectionRichtextQuit,
  IllustrationSmallNoSearch,
  OutlinedTipsAdd,
  OutlinedDirectionUpload,
  OutlinedTipsClose,
} from '@hose/eui-icons'
import Highlighter from 'react-highlight-words'
import { app, app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { calcDetailsAmount, calcDetailsReceivingAmount } from '@ekuaibao/lib/lib/lib-util'
import { isString, isObject } from '@ekuaibao/helpers'
import { allowModifyField } from '../../../components/utils/validatorUtil'
import { parseAsMeta } from '../../../lib/util/parser'
import commonActions from '../../../plugins/common/common.action'
import { getEntityFormById } from '../../../components/dynamic/inner/action'
import { flattenObject } from '../RiskNotice/utils'
import { showNewInvoiceFormTags } from './DetailsItemFeetype'
import { localStorageSet } from '../../../lib/util'
import {
  allowSelectionReceivingCurrency,
  filterFieldsForSort,
  formatFeeTypeList,
  getDetailHiddenFields,
  getDetailSpeHiddenFields
} from './utils'
import {
  get,
  sortedUniq,
  flatten,
  uniq,
  throttle,
  cloneDeep as _cloneDeep,
  uniqBy as _uniqBy,
  some as _some
} from 'lodash'
import DetailsItem from './DetailsItem'
import DetailTable from './DetailsTable'
import DetailsSort from './DetailsSort'
import MoneysView from '../MoneysView'
import EkbIcon from '../../ekbIcon'
import { SortType, sortTypeList, formateSortTypeMenu, sortTypeMap, flat } from './details.helper'
import { detailsTemplateAddReceivingAmount } from '../../../plugins/feetype/parts/feeTypeInfoHelper'
import { enableFlowHiddenFields } from '../../../lib/featbit'

const noop = () => {}
let itemByname = null,
  detailWrap,
  stickyHeader
const windowWith = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
const SHOW_TYPE_MAP = {
  LIST_P: 'LIST*',
  TABLE_P: 'TABLE'
}

const DEFAULT_GROUP_OF_LIST_VIEW = 'LIST'
const DEFAULT_GROUP_OF_TABLE_VIEW = 'TABLE'
const DEFAULT_GROUP_OF_SORTING_BY_EXPENSE_FIRST_LEVEL_TYPE = 'TABLE1'
const DEFAULT_GROUP_OF_SORTING_BY_EXPENSE_SECOND_LEVEL_TYPE = 'TABLE2'
const getDetailShowTypes = () => [
  { type: DEFAULT_GROUP_OF_LIST_VIEW, label: i18n.get('无分组') },
  { type: null, label: i18n.get('表格展示') },
  { type: DEFAULT_GROUP_OF_TABLE_VIEW, label: i18n.get('无分组') },
  { type: DEFAULT_GROUP_OF_SORTING_BY_EXPENSE_FIRST_LEVEL_TYPE, label: i18n.get('按一级费用类型分组') },
  { type: DEFAULT_GROUP_OF_SORTING_BY_EXPENSE_SECOND_LEVEL_TYPE, label: i18n.get('按二级费用类型分组') }
]

const VIEWS = {
  LIST: 'LIST',
  TABLE: 'TABLE'
}
const viewModeActions = [
  {
    text: i18n.get('列表视图'),
    key: VIEWS.LIST,
    'data-testid': 'detail-view-list-button'
  },
  {
    text: i18n.get('表格视图'),
    key: VIEWS.TABLE,
    'data-testid': 'detail-view-table-button'
  }
]

const fullCloseHandlerRef = {
  close: null
}

@EnhanceConnect(state => ({
  user: state['@common'].me_info,
  feeTypes: state['@common'].feetypes.data,
  travelBackInfo: state['@bill'].travelBackInfo,
  fieldsGroupData: state['@bill'].fieldsGroupData,
  remunerationSetting: state['@home'].remunerationSetting,
  RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization,
  globalFields: state['@common'].baseDataProperties.data,
  Universal: state['@common'].powers.Universal,
  feeChangeInfo: state['@bill'].feeChangeInfo,
  expenseSpecAfterFiltered: state['@home'].expenseSpecAfterFiltered,
  autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
}))
export default class Details extends PureComponent {
  constructor(props) {
    super(props)
    const { field, dataSource, user } = props
    const DETAIL_SHOWTYPE_BUTTONS = getDetailShowTypes()
    const isTestingEnterprise = isHongShanTestingEnterprise(Fetch.ekbCorpId)

    // 预置费用明细按角色展示
    this.showTypeByRoleValue = null
    if (field?.showTypeByRole && isTestingEnterprise) {
      const userRoles = get(user, 'staff.roles.values', [])
      const userRoleIds = userRoles.map(el => el.roleDefId)
      let showTypeMethod
      if (Array.isArray(field.showTypeByRole)) {
        showTypeMethod = field.showTypeByRole.find(
          el => el?.roleIds?.filter(roleId => userRoleIds.includes(roleId)).length > 0
        )
      }
      if (showTypeMethod) this.showTypeByRoleValue = showTypeMethod.showTypeNew
    }

    // 配置模板使用新的配置值：showTypeNew 来替换 showType
    let showTypeNewVal = field?.showType
    if ((field?.showTypeNew && Array.isArray(field?.showTypeNew)) || this.showTypeByRoleValue) {
      let showTypeNew = this.showTypeByRoleValue ? _cloneDeep(this.showTypeByRoleValue) : _cloneDeep(field.showTypeNew)
      let flag = false
      if (!!~showTypeNew.indexOf('STAFF')) {
        // 如果配置中包含人员分组
        const showTypeP = showTypeNew[0]
        if (SHOW_TYPE_MAP[showTypeP]) {
          showTypeNewVal = `${SHOW_TYPE_MAP[showTypeP]}-FIELDGROUP-${showTypeNew.pop()}-E`
          flag = true
        }
      }
      if (!flag) {
        // 如果配置了新的展示方式，但是没有选择人员分组，则直接取数组最后一个值作为选定制
        showTypeNewVal = showTypeNew.pop()
      }
    }
    const showTypeValue = this.showTypeByRoleValue
      ? showTypeNewVal
      : this.getViewShowType(isTestingEnterprise) ||
        window.detailShowType ||
        showTypeNewVal ||
        DETAIL_SHOWTYPE_BUTTONS[0].type
    const sortTypeList = this.getSortTypeList(dataSource)
    this.state = {
      hiddenFieldsMap: {},
      foldedGroup: new Set(),
      unfoldedGroup: new Set(),
      isTestingEnterprise,
      showType: showTypeValue,
      fieldsGroupValue: [],
      allTemplate: null,
      hasDataLinkEdits: false,
      isSticky: false,
      navHeaderHeight: 0,
      tabNavHeaderHeight: 0,
      stickyHeaderHeight: 0,
      initnalStickyHeaderHeight: 0,
      sortDataSource: dataSource,
      selectedSortMenu: sortTypeList[0],
      showDetailList: true,
      sortTypeList,
      viewActionVisible: false,
      curViewMode: this.getViewByGroupType(showTypeValue),
      isFullScreen: false,
      groupSearchValue: '',
    }
    // 英文下，以块状展示费用明细标题
    this._blockExpenseHeader = Fetch.lang === 'en-US'

    this.activeGroupingItemRef = createRef()
  }

  isTableViewMode = (viewType) => {
    return viewType.startsWith(SHOW_TYPE_MAP.TABLE_P + '-FIELDGROUP') ||
      [
        DEFAULT_GROUP_OF_TABLE_VIEW,
        DEFAULT_GROUP_OF_SORTING_BY_EXPENSE_FIRST_LEVEL_TYPE,
        DEFAULT_GROUP_OF_SORTING_BY_EXPENSE_SECOND_LEVEL_TYPE
      ].includes(viewType)
  }

  getViewByGroupType = (group) => {
    return this.isTableViewMode(group) ? VIEWS.TABLE : VIEWS.LIST;
  }

  componentWillMount() {
    const { bus } = this.props
    if (!this.props.feeTypes || !this.props.feeTypes.length) {
      api.invokeService('@common:get:feeTypes')
    }
    this.updateMulRefList(this.props)
    this.getTemplate(this.props)
    this.initFieldsGroup()
    bus?.on('on:tab:change', this.handleTabChange)
    api.dataLoader('@common.staffs').load()
  }

  componentDidMount() {
    this.initSticky()
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.dataSource !== nextProps.dataSource) {
      this.updateMulRefList(nextProps)
      this.getTemplate(nextProps)
      this.initFieldsGroup()
      let dataSourceCopy = nextProps.dataSource
      if(nextProps.dataSource?.length){
        dataSourceCopy = detailsTemplateAddReceivingAmount(nextProps.dataSource)
      }
      const sortTypeList = this.getSortTypeList(dataSourceCopy)
      this.setState({ sortDataSource: dataSourceCopy, sortTypeList })
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    window.removeEventListener('scroll', this.onScroll, true)
    bus?.un('on:tab:change', this.handleTabChange)

    if (fullCloseHandlerRef.close) {
      fullCloseHandlerRef.close()
      fullCloseHandlerRef.close = null
    }
  }

  getSortTypeList = (dataSource = []) => {
    let dateFieldsList = []
    dataSource.forEach(item => {
      const fieldsList = get(item, 'specificationId.components', [])
      const dateList = fieldsList.filter(value => value.type === 'date')
      dateFieldsList = dateFieldsList.concat(dateList)
    })
    dateFieldsList = _uniqBy(dateFieldsList, 'field')
    let dateSortTypeList = dateFieldsList.map(item => {
      return {
        type: null,
        label: item.label,
        children: formateSortTypeMenu(SortType.DATE, item.field, item.label)
      }
    })
    // 费用明细排序列表平铺
    dateSortTypeList = flat(dateSortTypeList)
    return sortTypeList.concat(dateSortTypeList)
  }

  getSpecificationType = () => {
    const { billSpecification } = this.props
    const specId = billSpecification?.id
    if (specId && typeof specId === 'string') {
      const specIdArr = specId.split(':')
      return specIdArr[0]
    }
    return undefined
  }

  // 发起导入红杉快速报销事件
  handleImportQuickExpense = async () => {
    const { bus, onHandlerImport } = this.props
    let res = await bus?.invoke('element:details:import:quick-expense')
    onHandlerImport(res)
  }

  initFieldsGroup = () => {
    // billSpecification.id
    api.dataLoader('@common.baseDataProperties').load()
      .then(resp => {
        // let staffNameArr = resp?.value?.dimensions || []
        let staffNameArr = resp?.data?.map(item => item.name) || []
        // 如果个人没有配置，查看模板配置信息
        if (!staffNameArr || !staffNameArr.length) {
          let { showType, showTypeNew } = this.props.field || {}
          if ((showTypeNew && Array.isArray(showTypeNew)) || this.showTypeByRoleValue) {
            showTypeNew = this.showTypeByRoleValue ? _cloneDeep(this.showTypeByRoleValue) : _cloneDeep(showTypeNew)
            // 取数组最后一个值，且返回一个数组，作为默认的维度展示字段
            staffNameArr = showTypeNew.splice(-1, 1)
          } else {
            staffNameArr = [showType]
          }
        }
        const singleStaffFields = filterFieldsForSort(this.props.globalFields)
        const resultStaffFields = staffNameArr
          .map(name => {
            const matchSingleStaff = singleStaffFields.filter(item => item.name === name)
            return matchSingleStaff[0] ? matchSingleStaff[0] : undefined
          })
          .filter(item => item)
        // 看看当前的显示模式是否还存在
        let { showType = '' } = this.state
        if (showType.indexOf('-FIELDGROUP-') >= 0) {
          if (!resultStaffFields.some(resultStaffField => showType.indexOf(resultStaffField.name) !== -1)) {
            // 之前的选项已被删除
            const { field } = this.props
            showType = field.showType || getDetailShowTypes()[0].type
          }
        }

        this.setState({
          fieldsGroupValue: resultStaffFields,
          showType,
          curViewMode: this.getViewByGroupType(showType)
        })
      })
  }

  updateMulRefList = props => {
    const { dataSource = [], globalFields } = props
    const dataTemp = {}
    let ids = []
    if (!!dataSource.length) {
      dataSource.forEach((el, index) => {
        const { feeTypeForm, feeTypeId, specificationId } = el
        const components = parseAsMeta(specificationId, globalFields)
        const fields = components.filter(
          el => el.showInDetails && el.type && el.type.startsWith('list:ref:basedata.Dimension')
        )
        const listRef = fields.map(el => feeTypeForm[el.field])
        if (listRef.length) {
          dataTemp[index] = fields
          ids = ids.concat(listRef)
        }
      })
      ids = sortedUniq(flatten(ids).filter(id => isString(id)))
      if (ids.length) {
        api.invokeService('@bill:get:dimension', ids.join(',')).then(res => {
          dataSource.forEach((el, index) => {
            const { feeTypeForm } = el
            const fields = dataTemp[index]
            if (fields) {
              fields.forEach(field => {
                const mulRefListIds = feeTypeForm[field.name]
                const mulRefListObjs = mulRefListIds?.map(id => res.items.find(el => el.id === id) || id)
                if (mulRefListIds && mulRefListObjs) {
                  feeTypeForm[field.name] = mulRefListObjs
                }
              })
            }
          })
          this.setState({ dataSource })
        })
      }
    }
  }

  handleDetailShowTypeButtons = (detailShowTypeButtons = []) => {
    // @todo
    // 添加表格按人员分组菜单
    const { fieldsGroupValue } = this.state
    const newFieldsGroupTableValue = fieldsGroupValue.map(item => ({
      type: `TABLE-FIELDGROUP-${item.name}-E`,
      label: item.label,
      level: 0
    }))
    detailShowTypeButtons.splice(detailShowTypeButtons.length, 0, ...newFieldsGroupTableValue)
    // 添加明细按人员分组菜单
    const newFieldsGroupListValue = fieldsGroupValue.map(item => ({
      type: `LIST*-FIELDGROUP-${item.name}-E`,
      label: item.label
    }))
    detailShowTypeButtons.splice(1, 0, ...newFieldsGroupListValue)
  }

  handleSelectGroupItem = (groupType) => {
    const { user } = this.props
    const key = user?.staff?.id + '-' + this.getSpecificationType() + '-' + 'feeDetailShowType'
    localStorageSet(key, groupType)
    this.setState({ showType: groupType })
  }

  getAllGroupFields = () => {
    const DETAIL_SHOWTYPE_BUTTONS = getDetailShowTypes()
    this.handleDetailShowTypeButtons(DETAIL_SHOWTYPE_BUTTONS)
    return DETAIL_SHOWTYPE_BUTTONS;
  }

  getViewCommonFields = () => {
    const { fieldsGroupValue } = this.state
    return [
      ...fieldsGroupValue.map(item => ({
        type: `TABLE-FIELDGROUP-${item.name}-E`,
        label: item.label,
        level: 0
      })),
      ...fieldsGroupValue.map(item => ({
        type: `LIST*-FIELDGROUP-${item.name}-E`,
        label: item.label
      }))
    ]
  }

  renderSearchEmpty = () => {
    return <div className={styles.empty}>
      <IllustrationSmallNoSearch fontSize={100} />
      <span className={styles.desc}>{i18n.get('没有找到相关内容')}</span>
    </div>
  }

  renderGroupItems = (onPopupClose) => {
    const { showType, curViewMode, groupSearchValue } = this.state
    let visibleItems = this.getAllGroupFields()
    if (groupSearchValue.trim()) {
      visibleItems = visibleItems.filter(item => item.label.includes(groupSearchValue))

      if (visibleItems.length === 0) {
        return this.renderSearchEmpty()
      }
    }
    const listItems = visibleItems.map((item, index) => {
      if (!item.type?.includes(curViewMode)) {
        return null
      }
      const isActive = item.type === showType
      const handleSelect = () => {
        this.handleSelectGroupItem(item.type)
        this.setState({ groupSearchValue: '' })
        onPopupClose()
      }
      return (
        <List.Item
          key={index}
          className={classNames(styles['list-item'], {
            [styles['active']]: isActive
          })}
          onClick={handleSelect}
        >
          {/* for scrollIntoView */}
          {isActive && (<span ref={this.activeGroupingItemRef} />)}
          <Highlighter
            highlightClassName={styles['highlight']}
            searchWords={groupSearchValue ? [groupSearchValue] : []}
            textToHighlight={item.label}
          />
          {isActive && (
            <OutlinedTipsDone className={styles.icon} />
          )}
        </List.Item>
      )
    })
    return <List className={styles['group-list']}>
      {listItems}
    </List>
  }

  handleSearchGroupItems = (value) => {
    this.setState({ groupSearchValue: value })
  }

  showActionSheet = (e) => {
    e.stopPropagation()
    app.open('@home5:EUIPopup', {
      heading: i18n.get('选择分组条件'),
      searchable: true,
      onSearch: this.handleSearchGroupItems,
      render: (onPopupClose) => {
        const mayAfterPopupRenderDone = (cb) => {
          return setTimeout(() => {
            cb()
          }, 300)
        }
        mayAfterPopupRenderDone(() => {
          if (this.activeGroupingItemRef.current) {
            this.activeGroupingItemRef.current.scrollIntoView({ block: 'center' })
          }
        })
        return this.renderGroupItems(onPopupClose)
      },
    })

  }

  getViewShowType(isTestingEnterprise) {
    if (isTestingEnterprise) return undefined
    const { user, billSpecification } = this.props
    const key = user?.staff?.id + '-' + this.getSpecificationType() + '-' + 'feeDetailShowType'
    const ShowType = localStorage.getItem(key)
    return billSpecification?.id && ShowType ? ShowType : undefined
  }
  renderAction = () => {
    const {
      onAddDetailClick,
      onImportInvoiceClick,
      onChangeHeaderAction,
      isModifyBill,
      importAble,
      billSpecification,
      flowAllowModifyFields = [],
      configs,
      billState,
      expenseSpecAfterFiltered,
      autoExpenseWithBillStriction
    } = this.props
    const canImport = !isModifyBill && importAble
    const billType = get(billSpecification, 'type')
    const disabled = this.getAddFeeTypeDisabled(isModifyBill, billType, flowAllowModifyFields)
    const blackList = ['reconciliation', 'settlement']
    const apply = configs.find(v => v.ability === 'apply') || {}
    const isEBussCard = configs?.find(item => item.isEBussCard)
    const showBatchBtn = isEBussCard && billState !== 'new' ? false : true
    const { isTestingEnterprise } = this.state
    const addText = isTestingEnterprise ? i18n.get('直接添加', {}) : i18n.get('添加消费', {})
    const importText = isTestingEnterprise ? i18n.get('发票导入', {}) : i18n.get('导入消费', {})
    const cannotImportQuickExpense = disabled || apply.onlyRequisitionDetails || isModifyBill
    if (billType === 'expense' && autoExpenseWithBillStriction) {
      return (
        <div className="detail-action">
          {showBatchBtn && (
            <Button theme='highlight' size='mini' category='text' onClick={onChangeHeaderAction}>{i18n.get('批量管理', {})}</Button>
          )}
        </div>
      )
    }
    return (
      <div className="detail-action">
        {expenseSpecAfterFiltered?.extendType === 'QuickExpense' && (
          <>
            <Button theme='highlight' size='mini' category='text' disabled={!!cannotImportQuickExpense} onClick={cannotImportQuickExpense ? noop() : this.handleImportQuickExpense}>{i18n.get('导入快速报销')}</Button>
            <div className="line" />
          </>
        )}
        {!blackList.includes(billType) && expenseSpecAfterFiltered?.extendType !== 'QuickExpense' && (
          <>
            {!isEBussCard && (
              <>
                <Button theme='highlight' size='mini' category='text' disabled={disabled || apply.onlyRequisitionDetails} onClick={disabled || apply.onlyRequisitionDetails ? noop() : onAddDetailClick}>{addText}</Button>
                <div className="line" />
              </>
            )}
            <Button theme='highlight' size='mini' category='text' disabled={!canImport || apply.onlyRequisitionDetails} onClick={!canImport || apply.onlyRequisitionDetails ? noop() : onImportInvoiceClick}>{importText}</Button>
            <div className="line" />
          </>
        )}
        {showBatchBtn && (
          <Button theme='highlight' size='mini' category='text' onClick={onChangeHeaderAction}>{i18n.get('批量管理', {})}</Button>
        )}
      </div>
    )
  }
  renderApportionAction = () => {
    const {
      dataSource = [],
      onChangeHeaderAction,
      onApportionClick,
      onBatchRemoveClick,
      flowAllowModifyFields,
      configs,
      onCopyClick,
      billSpecification,
      onCheckedAllChange,
      Universal,
      expenseSpecAfterFiltered,
      autoExpenseWithBillStriction
    } = this.props
    const billType = get(billSpecification, 'type')
    const editable = allowModifyField({ editable: true, field: 'apportion' }, flowAllowModifyFields)
    let chargeAgainst = configs.find(v => v.ability === 'chargeAgainst') || {} //冲销不能有 分摊
    let selectedItem
    const copyBtnCanUse =
      dataSource.filter(v => {
        if (v.checked) {
          selectedItem = v
          return true
        }
      }).length === 1
    let isAllCheck =
      dataSource.filter(v => {
        if (v.checked) {
          return true
        }
      }).length === dataSource.length

    const blackList = ['reconciliation', 'settlement']
    const apply = configs.find(v => v.ability === 'apply') || {}
    if (billType === 'expense' && autoExpenseWithBillStriction) {
      return (
        <div className="detail-action">
          <Checkbox
            className="detail-action-select-all"
            checked={isAllCheck}
            onChange={checked => onCheckedAllChange({ target: { checked } })}
            shape="circle"
          >
            {i18n.get('全选')}
          </Checkbox>
          {!chargeAgainst.isChargeAgainst && editable && (
            <Button theme='highlight' size='mini' category='text' onClick={onApportionClick}>{i18n.get('分摊')}</Button>
          )}
          <OutlinedTipsClose onClick={onChangeHeaderAction} fontSize={16} color={'var(--eui-icon-n2)'} />
        </div>
      )
    }
    const isEBussCard = configs?.find(item => item.isEBussCard)
    if (isEBussCard) {
      return (
        <div className="detail-action batching">
          <Checkbox
            className="detail-action-select-all"
            checked={isAllCheck}
            onChange={checked => onCheckedAllChange({ target: { checked } })}
          >
            {i18n.get('全选')}
          </Checkbox>
          <div className="line" />
          {!chargeAgainst.isChargeAgainst && editable && (
            <Button theme='highlight' size='mini' category='text' onClick={onApportionClick}>{i18n.get('分摊')}</Button>
          )}
          {billType === 'expense' && <div className="line" />}
          <Button theme='highlight' size='mini' category='text' onClick={onBatchRemoveClick}>{i18n.get('移除', {})}</Button>
          <OutlinedTipsClose onClick={onChangeHeaderAction} fontSize={16} color={'var(--eui-icon-n2)'} />
        </div>
      )
    }
    const text = expenseSpecAfterFiltered?.extendType === 'QuickExpense' ? '移至快速报销' : '移至随手记'

    return (
      <div className="detail-action batching">
        <Checkbox
          className="detail-action-select-all"
          checked={isAllCheck}
          onChange={checked => onCheckedAllChange({ target: { checked } })}
        >
          {i18n.get('全选')}
        </Checkbox>
        {!chargeAgainst.isChargeAgainst && editable && (
          <Button theme='highlight' size='mini' category='text' onClick={onApportionClick}>{i18n.get('分摊')}</Button>
        )}
        {billType === 'expense' && <div className="line" />}
        <Button theme='highlight' size='mini' category='text' onClick={onBatchRemoveClick}>{i18n.get('移除', {})}</Button>
        {!blackList.includes(billType) && <div className="line" />}
        {!blackList.includes(billType) && expenseSpecAfterFiltered?.extendType !== 'QuickExpense' && (
          <Button theme='highlight' size='mini' category='text' disabled={!(!apply.onlyRequisitionDetails && copyBtnCanUse)} onClick={() => {
            !apply.onlyRequisitionDetails && copyBtnCanUse && onCopyClick(selectedItem)
          }}>{i18n.get('复制')}</Button>
        )}
        {!blackList.includes(billType) && expenseSpecAfterFiltered?.extendType !== 'QuickExpense' && <div className="line" />}
        {!Universal && !blackList.includes(billType) && (
          <Button theme='highlight' size='mini' category='text' onClick={() => {
            onBatchRemoveClick && onBatchRemoveClick(true)
          }}>{i18n.get(text)}</Button>
        )}
        <OutlinedTipsClose onClick={onChangeHeaderAction} fontSize={16} color={'var(--eui-icon-n2)'} />
      </div>
    )
  }

  handleSort = (sortDataSource, selectedSortMenu) => {
    this.setState({ sortDataSource, selectedSortMenu })
  }

  handleChangeShowDetailList = () => {
    this.setState({ showDetailList: !this.state.showDetailList })
  }

  renderRepresentationActions = () => {
    const { isPermitForm, isEdit, hideSortBtn, dataSource } = this.props
    const { selectedSortMenu, viewActionVisible, curViewMode, sortTypeList } = this.state
    if (isPermitForm) {
      return null
    }

    return <div className={classNames(styles['detail-representation-actions'],
      { [styles['detail-representation-actions--block']]: this._blockExpenseHeader }
    )}>
      <Button
        category="text"
        onClick={this.showActionSheet}
        className={styles['action-button']}
      >
        <span className={styles.label}>{i18n.get('分组')}</span>
        <OutlinedDirectionDown className={styles.icon} fontSize={12} />
      </Button>
      {!isEdit && !hideSortBtn && (
        <DetailsSort
          dataSource={dataSource}
          selectedSortMenu={selectedSortMenu}
          sortTypeList={sortTypeList}
          onSort={this.handleSort}
          getSortData={this.fnChangeSort}
        />
      )}
      <Button className={styles['action-button']} category='text' onClick={() => this.setState({ viewActionVisible: true })}>
        <span className={styles.label}>{curViewMode === 'LIST' ? i18n.get('列表视图') : i18n.get('表格视图')}</span>
        <OutlinedDirectionDown className={styles.icon} fontSize={12}  />
      </Button>
      <ActionPanel
				cancelText={i18n.get('取消')}
				visible={viewActionVisible}
        actions={viewModeActions}
        data-testid="detail-view-action-panel"
        onAction={this.handleViewActionSelect}
				onClose={() => this.setState({ viewActionVisible: false })}
			/>
    </div>
  }

  handleViewActionSelect = (ops) => {
    const { showType } = this.state
    const nextView = ops.key
    const mayNextGroupType = this.isTableViewMode(nextView)
      ? showType.replace(SHOW_TYPE_MAP.LIST_P, SHOW_TYPE_MAP.TABLE_P)
      : showType.replace(SHOW_TYPE_MAP.TABLE_P, SHOW_TYPE_MAP.LIST_P)
    let nextDetail = mayNextGroupType
    if (this.getViewCommonFields().some(item => item.type === mayNextGroupType)) {
      // ignore
    } else if (nextView === 'TABLE') {
      nextDetail = DEFAULT_GROUP_OF_TABLE_VIEW
    } else {
      nextDetail = DEFAULT_GROUP_OF_LIST_VIEW
    }
    this.setState({
      curViewMode: nextView,
      showType: nextDetail,
      viewActionVisible: false,
    })
  }

  ifShowFullScreenSwitch = () => {
    const { curViewMode } = this.state
    const { isEdit } = this.props
    return curViewMode === VIEWS.TABLE && !isEdit
  }

  renderFullScreenAction = () => {
    const { isFullScreen } = this.state
    const toggleFullScreen = () => {
      this.setState({ isFullScreen: !isFullScreen }, () => {
        if (isFullScreen) {
          fullCloseHandlerRef.close?.()
        } else {
          app.open('@home5:fullscreenModal', {
            renderChildren: (_fn) => {
              fullCloseHandlerRef.close = _fn;
              // 克隆的方式就不需要再次走一遍render逻辑，提高渲染效率。
              return this._curRenderElement ? React.cloneElement(this._curRenderElement) : this._render()
            },
            viewMode: 'landscape',
            onGoBack: () => this.setState({ isFullScreen: false})
          })
        }
      })
    }

    return this.ifShowFullScreenSwitch()
      ? <Button onClick={toggleFullScreen} className={styles['fullscreen-switch']} icon={isFullScreen ? <OutlinedDirectionRichtextQuit /> : <OutlinedDirectionRichtext /> } category='text' />
      : null
  }

  renderHeader = () => {
    const {
      dataSource = [],
      withPadding,
      unVisibleCount,
      billSpecification,
      isEdit,
      field,
    } = this.props
    const { showDetailList, isFullScreen } = this.state
    const DETAIL_SHOWTYPE_BUTTONS = getDetailShowTypes()
    this.handleDetailShowTypeButtons(DETAIL_SHOWTYPE_BUTTONS)
    const billType = billSpecification?.type || this.props.billType
    let amount = calcDetailsAmount(dataSource)
    const hasReceivingAmount = allowSelectionReceivingCurrency(billSpecification)
    if (hasReceivingAmount) {
      amount = calcDetailsReceivingAmount(dataSource)
    }
    const detailCount = dataSource.length
    const typeMap = {
      expense: i18n.get('费用'),
      requisition: i18n.get('申请'),
      reconciliation: i18n.get('对账'),
      settlement: i18n.get('结算'),
      receipt: i18n.get('费用'),
      corpPayment: i18n.get('付款')
    }
    const title = billType && typeMap[billType] ? `${typeMap[billType]}${i18n.get('明细')}` : i18n.get('费用明细')
    return (
      <div className={classNames('detail-header', { padding: withPadding })}>
        <div className={classNames('detail-header-title', { 'detail-header-title-padding': this.ifShowFullScreenSwitch() })}>
          <span className="label-style">
            {title}
            <span className="detail-header-title-count">{detailCount >= 0 && i18n.get('(') + (detailCount - (unVisibleCount || 0)) + i18n.get(')')}</span>
            {field?.optional || !isEdit ? null : <span className="label-optional">*</span>}
          </span>
          {!this._blockExpenseHeader && !isFullScreen && <div className="detail-header-actions">
            {this.renderRepresentationActions()}
          </div>}
          {this.renderFullScreenAction()}
        </div>
        {this._blockExpenseHeader && !isFullScreen && this.renderRepresentationActions()}
        {unVisibleCount > 0 && (
          <div className="unvisible-count-text">{i18n.get('共计{__k0}条，还有{__k1}条无可见权限', { __k0: detailCount, __k1: unVisibleCount })}</div>
        )}
        {!!detailCount && (
          <div className={classNames('detail-total-amount', { 'fold-detail': !showDetailList })}>
            <div className="total-label">{i18n.get('合计总额')}</div>
            <div className="total">
              <MoneysView
                onlyTotalStandard={hasReceivingAmount}
                isTotal={true}
                primaryClassName="primary-money"
                minorClassname="minor-money"
                money={amount}
              />
              {!isEdit && !showDetailList && (
                <div className="fold-btn" onClick={this.handleChangeShowDetailList}>
                  {i18n.get('展开明细')}
                  <EkbIcon name="#EDico-arrow-more" />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }

  handleDataSourceByGroupFields = (dataSource = []) => {
    const { showType = '', fieldsGroupValue = [], selectedSortMenu } = this.state
    if (fieldsGroupValue && fieldsGroupValue.length) {
      const collapseDefaultKeys = new Set()
      const regExp = /\-FIELDGROUP\-(.*)\-E/g
      const complieResult = regExp.exec(showType)
      if (complieResult && complieResult[1]) {
        const showTypeField = complieResult[1]
        if (showTypeField) {
          let showTypeFieldLabel = ''
          // 获取所有的分组字段对应的值
          let dataSourceFiledValueArr = uniq(
            dataSource.map(item => {
              const fieldValue = item.feeTypeForm[showTypeField]
              if (isObject(fieldValue)) {
                return fieldValue?.name
              }
              if (isString(fieldValue)) {
                return fieldValue?.trim()
              }
              return fieldValue
            })
          )
          // 获取分组字段的 label
          fieldsGroupValue.some(fieldsGroupValueItem => {
            if (fieldsGroupValueItem.name === showTypeField) {
              showTypeFieldLabel = fieldsGroupValueItem.label
              return true
            }
            return false
          })
          dataSourceFiledValueArr.push('others')
          let newDataSource = []
          dataSourceFiledValueArr.forEach(dataSourceFiledValueItem => {
            if (dataSourceFiledValueItem !== 'others') {
              const currentDataSource =
                dataSource.filter(dataSourceItem => {
                  const fieldValue = dataSourceItem.feeTypeForm[showTypeField]
                  if (fieldValue) {
                    if (isObject(fieldValue)) {
                      return fieldValue?.name === dataSourceFiledValueItem
                    }
                    if (isString(fieldValue) && isString(dataSourceFiledValueItem)) {
                      return fieldValue?.trim() === dataSourceFiledValueItem?.trim()
                    }
                    return fieldValue === dataSourceFiledValueItem
                  }
                  return false
                }) || []
              if (currentDataSource && currentDataSource.length) {
                const fieldGroupTitle = `${showTypeFieldLabel}: ${dataSourceFiledValueItem}`
                const key = currentDataSource[0].feeTypeForm[showTypeField].id
                collapseDefaultKeys.add(key)
                newDataSource.push({
                  key,
                  fieldGroupTitle,
                  dataSource: currentDataSource
                })
              }
            } else {
              const isEmptyValue = (value) => [undefined, null, ''].includes(value)
              const others =
                dataSource.filter(dataSourceItem => {
                  return isEmptyValue(dataSourceItem.feeTypeForm[showTypeField])
                }) || []
              if (others && others.length) {
                newDataSource.push({
                  key: 'others',
                  fieldGroupTitle: '其他',
                  dataSource: others
                })
              }
            }
          })

          let sortDataSource = []
          if (selectedSortMenu.type === SortType.INPUT) {
            sortDataSource = newDataSource
          } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.ASC) {
            sortDataSource = newDataSource.sort((p, n) => {
              return Number(
                p.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0
              ) <
                Number(
                  n.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) ||
                    0
                )
                ? -1
                : 1
            })
          } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.DESC) {
            sortDataSource = newDataSource.sort((p, n) => {
              return Number(
                p.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0
              ) <
                Number(
                  n.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) ||
                    0
                )
                ? 1
                : -1
            })
          } else {
            sortDataSource = this.fnChangeSort(newDataSource, selectedSortMenu.type, 'dataSource[0].feeTypeForm')
          }

          if (this.showTypeByRoleValue && collapseDefaultKeys.size === 0) {
            collapseDefaultKeys.add('others')
          }
          return {
            collapseDefaultKeys,
            showFieldsGroup: true,
            dataSource: sortDataSource
          }
        }
      }
    }
    return {
      showFieldsGroup: false,
      dataSource
    }
  }

  fnChangeSort = (sortDataSource = [], type = '', path = '') => {
    const sort_type = type.includes(SortType.ASC) ? SortType.ASC : SortType.DESC
    const field = type.slice(sort_type.length + 1) || ''
    const field_path = `${path}.${field}`
    const fieldSortData = sortDataSource.filter(item => !!get(item, field_path, 0))
    const noFieldSortData = sortDataSource.filter(item => !get(item, field_path, 0))
    let sortData = []
    if (sort_type === SortType.ASC) {
      sortData = fieldSortData.sort((p, n) => {
        return Number(get(p, field_path, 0)) - Number(get(n, field_path, 0))
      })
    } else {
      sortData = fieldSortData.sort((p, n) => {
        return Number(get(n, field_path, 0)) - Number(get(p, field_path, 0))
      })
    }
    return sortData.concat(noFieldSortData)
  }

  handleCollapse = (key, collapseDefaultKeys) => {
    const { foldedGroup, unfoldedGroup } = this.state
    if (foldedGroup.has(key) || unfoldedGroup.has(key)) {
      if (foldedGroup.has(key)) {
        // 当前是折叠状态的分组
        foldedGroup.delete(key)
        unfoldedGroup.add(key)
      } else {
        // 当前是展开状态的分组
        foldedGroup.add(key)
        unfoldedGroup.delete(key)
      }
    } else {
      if (collapseDefaultKeys.has(key)) {
        // 默认展开的分组，点击后，折叠分组
        foldedGroup.add(key)
      } else {
        // 默认折叠的分组，点击后，展开分组
        unfoldedGroup.add(key)
      }
    }
    this.setState({ foldedGroup, unfoldedGroup }, this.forceUpdate)
  }

  renderDetailsList = isRemuneration => {
    let {
      isEdit,
      billType = 'expense',
      isBatch,
      onLineClick,
      onLineRemove,
      onTipsClick,
      onCheckedChange,
      globalFields,
      risks = [],
      external,
      riskInfo,
      showCheckBox,
      withPadding,
      field,
      isModifyBill,
      flowAllowModifyFields,
      limitFieldRequireds,
      showAllFeeType,
      apportionVisibleList,
      multiplePayeesMode,
      isCopyDetail,
      billSpecification,
      RiskPromptOptimization,
      configs,
      billState,
      feeChangeInfo,
      billFeeForceValidation,
      autoExpenseWithBillStriction
    } = this.props
    const { allTemplate, sortDataSource: dataSourceList, foldedGroup, unfoldedGroup, hiddenFieldsMap } = this.state
    const dataSource = formatFeeTypeList(dataSourceList, feeChangeInfo)
    const {
      showFieldsGroup = false,
      dataSource: newDataSource,
      collapseDefaultKeys
    } = this.handleDataSourceByGroupFields(dataSource)
    const showDeleteBtn = !(billType === 'expense' && autoExpenseWithBillStriction)
    if (showFieldsGroup) {
      const renderDetails = (line, i, isEditable, currentRisks, hiddenFields) => (
        <DetailsItem
          key={i}
          index={i}
          hiddenFields={hiddenFields}
          withPadding={withPadding}
          risks={currentRisks}
          isCopyDetail={isCopyDetail}
          isEdit={isEditable}
          isBatch={isBatch}
          dataSource={line}
          allTemplate={allTemplate}
          field={field}
          globalFields={globalFields}
          onClick={onLineClick.bind(
            this,
            line,
            i,
            dataSource,
            external && external[line.feeTypeForm.detailId],
            riskInfo && riskInfo[line.feeTypeForm.detailId]
          )}
          onTipsClick={onTipsClick}
          onRemove={onLineRemove}
          onCheckedChange={onCheckedChange}
          detailId={line.feeTypeForm.detailId}
          external={external && external[line.feeTypeForm.detailId]}
          riskInfo={riskInfo && riskInfo[line.feeTypeForm.detailId]}
          billType={billType}
          showCheckBox={showCheckBox}
          isModifyBill={isModifyBill}
          flowAllowModifyFields={flowAllowModifyFields}
          limitFieldRequireds={limitFieldRequireds}
          showAllFeeType={showAllFeeType}
          apportionVisibleList={apportionVisibleList}
          multiplePayeesMode={multiplePayeesMode}
          isRemuneration={isRemuneration}
          billSpecification={billSpecification}
          RiskPromptOptimization={RiskPromptOptimization}
          billFeeForceValidation={billFeeForceValidation}
          showDeleteBtn={showDeleteBtn}
        />
      )

      return (
        <div className={'fields-group-details-wrapper-container'}>
          {newDataSource.map((newDataSourceItem, index) => {
            const { fieldGroupTitle, dataSource: dataSourceItem = [], key } = newDataSourceItem
            let detailsWrapperStyle = {}
            let fnHandleCollapse = () => {}
            let CollapseIcon = () => null
            if (this.showTypeByRoleValue) {
              const fold = foldedGroup.has(key)
              const unfold = unfoldedGroup.has(key)
              // 没有折叠展开过分组时，按默认值展示
              const foldGroup = !fold && !unfold ? !collapseDefaultKeys.has(key) : fold && !unfold
              fnHandleCollapse = this.handleCollapse
              CollapseIcon = () =>
                foldGroup ? <EkbIcon name="#EDico-left-default" /> : <EkbIcon name="#EDico-down-default" />
              detailsWrapperStyle = foldGroup ? { display: 'none' } : {}
            }
            return (
              <div key={fieldGroupTitle} className={'details-wrapper-container'}>
                <div className={'field-group-title'} onClick={() => fnHandleCollapse(key, collapseDefaultKeys)}>
                  {fieldGroupTitle}
                  <CollapseIcon />
                </div>
                <div style={detailsWrapperStyle}>
                  <div className="detail-main">
                    {dataSourceItem.map((line, i) => {
                      let currentRisks = risks.filter(o => o.loc === i)
                      const hiddenFields = getDetailHiddenFields(hiddenFieldsMap, line)
                      return renderDetails(line, i, isEdit, currentRisks, hiddenFields)
                    })}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )
    }
    return (
      <div className="detail-main">
        {dataSource.map((line, i) => {
          let currentRisks = risks.filter(o => o.loc === i)
          const hiddenFields = getDetailHiddenFields(hiddenFieldsMap, line)
          return (
            <DetailsItem
              key={i}
              index={i}
              hiddenFields={hiddenFields}
              withPadding={withPadding}
              risks={currentRisks}
              isCopyDetail={isCopyDetail}
              isEdit={isEdit}
              isBatch={isBatch}
              dataSource={line}
              allTemplate={allTemplate}
              field={field}
              globalFields={globalFields}
              onClick={onLineClick.bind(
                this,
                line,
                i,
                dataSource,
                external && external[line.feeTypeForm.detailId],
                riskInfo && riskInfo[line.feeTypeForm.detailId]
              )}
              onTipsClick={onTipsClick}
              onRemove={onLineRemove}
              onCheckedChange={onCheckedChange}
              detailId={line.feeTypeForm.detailId}
              external={external && external[line.feeTypeForm.detailId]}
              riskInfo={riskInfo && riskInfo[line.feeTypeForm.detailId]}
              billType={billType}
              showCheckBox={showCheckBox}
              isModifyBill={isModifyBill}
              flowAllowModifyFields={flowAllowModifyFields}
              limitFieldRequireds={limitFieldRequireds}
              showAllFeeType={showAllFeeType}
              apportionVisibleList={apportionVisibleList}
              multiplePayeesMode={multiplePayeesMode}
              isRemuneration={isRemuneration}
              billSpecification={billSpecification}
              RiskPromptOptimization={RiskPromptOptimization}
              configs={configs}
              billState={billState}
              billFeeForceValidation={billFeeForceValidation}
              showDeleteBtn={showDeleteBtn}
            />
          )
        })}
      </div>
    )
  }

  cellInvoiceClick = (_line, dataSource) => {
    const { onTipsClick } = this.props
    const data = showNewInvoiceFormTags(dataSource)[0]
    onTipsClick && onTipsClick(data, dataSource)
  }

  cellPayeeClick = line => {
    window.detailShowType = this.state.showType
    const params = {
      start: 0,
      count: 9999,
      join: 'staffId,staffId,/v1/organization/staffs'
    }
    commonActions.listPayees(params).then(data => {
      window.payeeInfo = data.items.find(item => item.id === line.id)
      api.go('/home5-payee-info', false)
    })
  }

  renderDetailsTable = () => {
    const { dataSource = [] } = this.props
    const { showFieldsGroup = false, dataSource: newDataSource } = this.handleDataSourceByGroupFields(dataSource)
    if (showFieldsGroup) {
      return (
        <div className={'fields-group-details-wrapper-container'}>
          {newDataSource.map((newDataSourceItem, index) => {
            const { fieldGroupTitle, dataSource: dataSourceItem = [], key } = newDataSourceItem
            return (
              <div key={fieldGroupTitle} className={'details-wrapper-container'}>
                <div className={'field-group-title'}>{fieldGroupTitle}</div>
                <div>{this.renderDetailsTableWrapper(dataSourceItem)}</div>
              </div>
            )
          })}
        </div>
      )
    }
    return this.renderDetailsTableWrapper()
  }

  renderDetailsTableWrapper = (dataSourceParams = undefined) => {
    const {
      isEdit,
      billType = 'expense',
      onLineRemove,
      onTipsClick,
      onCheckedChange,
      multiplePayeesMode,
      billSpecification,
      external,
      onLineClick,
      globalFields,
      field,
      // dataSource: dataSourceList,
      onSelectAll,
      showCheckBox,
      isModifyBill,
      feeChangeInfo
    } = this.props
    const { sortDataSource: dataSourceList } = this.state
    const dataSource = formatFeeTypeList(dataSourceList, feeChangeInfo)
    let isForbid = false
    if (!dataSourceParams) {
      dataSourceParams = dataSource
    }
    const { showType, allTemplate } = this.state
    let level = showType.charAt(showType.length - 1)
    let groups = this.renderDetailsTableKind(level, dataSourceParams)
    const groupsArray = this.sortGroups(groups)
    const result = []
    for (let index = 0; index < groupsArray.length; index++) {
      const group = groupsArray[index]
      const key = group[0]?.feeTypeId?.id
      const ValidExternal = this.fnGetValidExternaForTable(external, group)
      group.map(v => {
        let risk = ValidExternal && ValidExternal[v.feeTypeForm.detailId]
        const flattenRisk = flattenObject(risk)
        isForbid = _some(flattenRisk, { isOutOfLimitReject: true })

        if (risk) {
          v.isForbid = isForbid
          v.rowStyle = { background: isForbid ? 'var(--eui-function-danger-50)' : 'var(--eui-function-warning-50)' }
          v.risk = flattenRisk?.length || 0
        }
        return v
      })
      result.push(
        <DetailTable
          showCheckBox={showCheckBox}
          handleLineRemove={onLineRemove}
          billSpecification={billSpecification}
          multiplePayeesMode={multiplePayeesMode}
          globalFields={globalFields}
          onLineClick={row => {
            onLineClick(row, row.index, dataSourceParams, external && external[row.feeTypeForm.detailId])
          }}
          type={billType}
          isEdit={isEdit}
          key={key}
          isForbid={isForbid}
          cellPayeeClick={this.cellPayeeClick}
          showType={showType}
          onTipsClick={onTipsClick}
          dataSource={group}
          allTemplate={allTemplate}
          cellInvoiceClick={this.cellInvoiceClick}
          onCheckedChange={onCheckedChange}
          onSelectAll={onSelectAll}
          showDetailNo={true}
        />
      )
    }
    return <div className="detail-main">{result}</div>
  }
  renderDetailsTAbleByFee = (dataSourceParams = []) => {
    const { dataSource = [] } = this.props
    let groups = {}
    ;(dataSourceParams || dataSource).forEach((item, i) => {
      item.index = i
      if (groups[item.feeTypeId.id]) {
        groups[item.feeTypeId.id].push(item)
      } else {
        groups[item.feeTypeId.id] = []
        groups[item.feeTypeId.id].push(item)
      }
    })
    return groups
  }
  renderDetailsTableKind = (level, dataSourceParams = []) => {
    let { feeTypes, dataSource = [] } = this.props
    if (!feeTypes || !feeTypes.length) return
    let groups = {}
    if (level === 'E') {
      level = 0
      return this.renderDetailsTAbleByFee(dataSourceParams)
    } else {
      ;(dataSourceParams || dataSource).forEach((item, i) => {
        let newItem
        let myItem = item
        myItem.index = i
        let fullname = get(item, 'feeTypeId.fullname') || get(item, 'feeTypeId.name')
        // @i18n-ignore
        let fullnameList = fullname.split('/').length > 1 ? fullname.split('/') : fullname.split('／')
        fullnameList.length = fullnameList.length < level ? fullnameList.length : level
        fullname = fullnameList.join('／') // @i18n-ignore
        newItem = this.getNewItemByName(feeTypes, fullname, level)
        myItem.newFeetype = item.feeTypeId
        groups = this.addGroupsItem(newItem?.id, myItem, groups)
      })
      return groups
    }
  }

  sortGroups = groups => {
    const { selectedSortMenu } = this.state
    const groupsArray = []
    Object.values(_cloneDeep(groups) || {}).forEach(item => {
      groupsArray.push(item)
    })
    if (selectedSortMenu.type === SortType.INPUT) {
      return groupsArray
    } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.ASC) {
      return groupsArray.sort((p, n) => {
        return Number(p.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0) <
          Number(n.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0)
          ? -1
          : 1
      })
    } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.DESC) {
      return groupsArray.sort((p, n) => {
        return Number(p.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0) <
          Number(n.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0)
          ? 1
          : -1
      })
    } else {
      return this.fnChangeSort(groupsArray, selectedSortMenu.type, '[0].feeTypeForm')
    }
  }

  fnGetValidExternaForTable = (external, group) => {
    //获取不同费用类型下table对应的超标提醒
    if (!external) return void 0
    let validExternal = {}
    group.forEach(v => {
      validExternal[v.feeTypeForm.detailId] = external[v.feeTypeForm.detailId]
    })
    return Object.keys(validExternal).length > 0 ? validExternal : void 0
  }

  getNewItemByName = (feeTypes = [], fullname) => {
    feeTypes.forEach(item => {
      if (!!~fullname.indexOf(item.name) && fullname !== item.name) {
        let nameList = fullname.split('／') // @i18n-ignore
        nameList.shift()
        let newname = nameList.join('／') // @i18n-ignore
        this.getNewItemByName(item.children, newname)
      } else if (!!~fullname.indexOf(item.name) && fullname === item.name) {
        itemByname = item
      }
    })
    return itemByname
  }

  addGroupsItem = (itemId, item, groups) => {
    if (groups[itemId]) {
      groups[itemId].push(item)
    } else {
      groups[itemId] = []
      groups[itemId].push(item)
    }
    return groups
  }

  getAddFeeTypeDisabled = (isModifyBill, billType, flowAllowModifyFields) => {
    if (isModifyBill) {
      // 审批中修改
      if (flowAllowModifyFields.length === 0) {
        // 审批流没有配置【允许审批人修改单据】(是白名单)
        return false
      } else {
        let disabled = !flowAllowModifyFields.includes('feeTypeId')
        if (billType === 'reconciliation' || billType === 'settlement') {
          disabled = true
        }
        return disabled
      }
    } else {
      return false
    }
  }

  renderEmpty = () => {
    const {
      onAddDetailClick,
      onImportInvoiceClick,
      isModifyBill,
      importAble,
      billSpecification,
      flowAllowModifyFields = [],
      configs,
      isPermitForm,
      expenseSpecAfterFiltered,
      autoExpenseWithBillStriction
    } = this.props
    const billType = get(billSpecification, 'type')
    if (billType === 'expense' && autoExpenseWithBillStriction) {
      return null
    }
    const canImport = !isModifyBill && importAble
    const apply = configs.find(v => v.ability === 'apply') || {}
    const disabled = this.getAddFeeTypeDisabled(isModifyBill, billType, flowAllowModifyFields)
    const isEBussCard = configs?.find(item => item.isEBussCard)
    const { isTestingEnterprise } = this.state
    const addText = isTestingEnterprise ? i18n.get('直接添加') : i18n.get('添加')
    const importText = isTestingEnterprise ? i18n.get('发票导入') : i18n.get('导入')
    const cannotImportQuickExpense = !canImport || apply.onlyRequisitionDetails || isModifyBill

    if (expenseSpecAfterFiltered?.extendType === 'QuickExpense') {
      return (
        <div className="detail-empty-action">
          <Button
            block
            category="secondary"
            theme="highlight"
            size="small"
            disabled={cannotImportQuickExpense}
            onClick={this.handleImportQuickExpense}
          >
            {i18n.get('导入快速报销')}
          </Button>
        </div>
      )
    }
    const { related } = app.require('@components/utils/Related')
    const { allowAdd } = related.specificationConfig
    const showImportBtn = isPermitForm ? allowAdd : true

    return (
      <div className="detail-empty-action">
        {!isEBussCard && (
          <Button
            block
            category="secondary"
            theme="highlight"
            size="small"
            disabled={disabled || apply.onlyRequisitionDetails}
            onClick={onAddDetailClick}
            icon={<OutlinedTipsAdd fontSize={20} />}
            data-testid="detail-empty-action-add"
          >
            {isPermitForm ? i18n.get('添加费用明细') : addText}
          </Button>
        )}
        {showImportBtn && (
          <Button
            block
            category="secondary"
            theme="highlight"
            size="small"
            disabled={!canImport || apply.onlyRequisitionDetails}
            onClick={onImportInvoiceClick}
            icon={<OutlinedDirectionUpload fontSize={20} />}
            data-testid="detail-empty-action-import"
          >
            {importText}
          </Button>
        )}
      </div>
    )
  }

  getTemplate = props => {
    const { dataSource } = props
    let dataLinkEdits = null
    this.getAllHiddenFields(dataSource)
    if (dataSource) {
      for (const data of dataSource) {
        dataLinkEdits = data.specificationId.components.find(item => item.type === 'dataLinkEdits')
      }
    }
    if (dataLinkEdits && dataLinkEdits.showInDetails) {
      this.setState({ hasDataLinkEdits: true })
      api.dispatch(getEntityFormById(dataLinkEdits.referenceData.id, true)).then(result => {
        this.setState({ allTemplate: result.items })
      })
    }
  }

  getAllHiddenFields = async dataSource => {
    const { isEdit } = this.props
    if(enableFlowHiddenFields()?.handleDetail && !isEdit) return
    const [changed, hiddenFieldsMap] = await getDetailSpeHiddenFields(dataSource, this.state.hiddenFieldsMap)
    if (changed) {
      this.setState({ hiddenFieldsMap })
    }
  }

  initSticky = () => {
    // const unavailableIds = ['ID_3mOAFYd10yw', 'ID_3mdZwqR14sg'] // 正雅 株洲麦格米特
    // if (!window.isIOS && window.__PLANTFORM__ === 'DING_TALK' && unavailableIds.includes(Fetch.ekbCorpId)) return

    const { isEdit, billSpecification, remunerationSetting } = this.props
    const originalId = billSpecification?.id?.split(':')?.[0]
    const isRemuneration = remunerationSetting?.specificationId == originalId

    setTimeout(() => {
      detailWrap = document?.getElementById?.('detail-wrap')
      stickyHeader = document?.getElementById?.('sticky-header')
      const navHeader = document?.getElementsByClassName?.('app-layout-header')?.[0]
      const tabNavHeader = document?.getElementsByClassName?.('am-tabs-tab-bar-wrap')?.[0]
      this.setState({
        navHeaderHeight: navHeader?.getBoundingClientRect?.()?.height ?? 0,
        tabNavHeaderHeight: tabNavHeader?.getBoundingClientRect?.()?.height ?? 0,
        stickyHeaderHeight: stickyHeader?.getBoundingClientRect?.()?.height ?? 0,
        initnalStickyHeaderHeight: stickyHeader?.getBoundingClientRect?.()?.height ?? 0
      })
    }, 1000)

    !isRemuneration && isEdit && window.addEventListener('scroll', this.onScroll, true)
  }

  onScroll = throttle(() => {
    const detailWrapRect = detailWrap?.getBoundingClientRect?.()
    const stickyHeaderRect = stickyHeader?.getBoundingClientRect?.()

    if (
      stickyHeaderRect?.height !== this.state.initnalStickyHeaderHeight ||
      stickyHeaderRect?.height !== this.state.stickyHeaderHeight
    ) {
      this.setState({
        stickyHeaderHeight: stickyHeaderRect?.height
      })
    }

    if (detailWrapRect?.top < this.state.navHeaderHeight + this.state.tabNavHeaderHeight) {
      if (Math.abs(detailWrapRect?.top) + stickyHeaderRect?.height * 2 > detailWrapRect?.height) {
        this.setState({ isSticky: false })
        return
      }
      this.setState({ isSticky: true })
    } else if (detailWrapRect?.top > this.state.navHeaderHeight + this.state.tabNavHeaderHeight) {
      this.setState({ isSticky: false })
    }

    if (detailWrapRect?.left < 0 || detailWrapRect?.left > windowWith) {
      this.setState({ isSticky: false })
    }
  }, 200)

  handleTabChange = () => {
    setTimeout(() => {
      this.onScroll()
    }, 0)
  }

  render() {
    this._curRenderElement = this._render();
    return this._curRenderElement;
  }

  _render() {
    const { isEdit, isBatch, isPermitForm, dataSource = [], billSpecification, remunerationSetting } = this.props
    const originalId = billSpecification?.id?.split(':')[0]
    const isRemuneration = remunerationSetting?.specificationId == originalId
    const {
      showType,
      allTemplate,
      hasDataLinkEdits,
      isSticky,
      navHeaderHeight,
      tabNavHeaderHeight,
      stickyHeaderHeight,
      showDetailList,
      isFullScreen,
    } = this.state
    const isEmpty = isEdit && !dataSource.length
    let dataSourceCopy = dataSource
    const billType = get(billSpecification, 'type')
    const blackList = ['reconciliation', 'settlement']
    const sticky = isSticky && dataSource?.length > 0

    return (
      <div
        id="detail-wrap"
        className={classNames(styles['detail-wrap'], {
          'dis-n': !isEdit && !dataSource.length,
          'detail-wrap-disabled': !isEdit,
          [styles['details-permit-form']]: isPermitForm
        })}
      >
        <div className="sticky-header-placeholder" style={{ height: sticky ? stickyHeaderHeight : 0 }} />
        <div
          id="sticky-header"
          style={{
            position: sticky ? 'fixed' : undefined,
            padding: sticky ? '0 16px' : undefined,
            top: `${navHeaderHeight + tabNavHeaderHeight}px`,
            left: 0,
            width: '100%',
            background: 'var(--eui-bg-float)',
            transform: 'translateZ(0)',
            zIndex: 999
          }}
        >
          {this.renderHeader()}
          {billType !== 'permit' &&
            !isEmpty &&
            !isRemuneration &&
            isEdit &&
            (!isBatch ? this.renderAction() : this.renderApportionAction())}
        </div>
        {showDetailList && (
          <>
            {isEmpty
              ? !blackList.includes(billType) && this.renderEmpty()
              : showType.startsWith('TABLE') && showType !== 'TABLE_NO_GROUP'
              ? hasDataLinkEdits
                ? allTemplate && this.renderDetailsTable()
                : this.renderDetailsTable()
              : this.renderDetailsList(isRemuneration)}
            {!isEdit && !isFullScreen && (
              <div className="fold-btn-wrapper">
                <div className="fold-btn" onClick={this.handleChangeShowDetailList}>
                  {i18n.get('收起')}
                  <EkbIcon name="#EDico-icon_uni_drop-title" />
                </div>
              </div>
            )}
          </>
        )}
      </div>
    )
  }
}
