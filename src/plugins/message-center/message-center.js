import './message-center.less'
import { PullToRefresh, ListView, Button } from 'antd-mobile'
import React from 'react'
import PageContainer from '../page-container'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import MessageItem from './message-center-item'
import { app as api } from '@ekuaibao/whispered'
import { getList, updateMessage, readAll } from './message-center.action'
import { Resource } from '@ekuaibao/fetch'
import { Dialog } from '@hose/eui-mobile'
const getCustomMessage = new Resource('/api/message/v1/messages')
const ETabs = api.require('@elements/EUITabs')
const EmptyWidget = api.require('@home5/EmptyWidget')
const SkeletonListEUI = api.require('@home5/SkeletonList')
const EKBIcon = api.require('@elements/ekbIcon')

const tabList = [
  { key: 'unread', tab: i18n.get('未读'), isRead: false },
  { key: 'read', tab: i18n.get('已读'), isRead: true }
]
const typeList = [
  { label: i18n.get('全部'), value: 'All' },
  { label: i18n.get('审批通知'), value: 'Approve' },
  { label: i18n.get('支付通知'), value: 'Payment' },
  { label: i18n.get('借款/还款通知'), value: 'Loan_Repayment' },
  { label: i18n.get('其他'), value: 'Other' }
]
@EnhanceTitleHook(props =>
  props?.location?.pathname?.split('/').includes('customMessage') ? i18n.get('自定义消息') : i18n.get('消息中心')
)
@EnhanceConnect(null, null, '@message-center/message-list/state')
export default class MessageCenter extends PageContainer {
  constructor(props) {
    super(props)
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2
    })

    this.state = {
      dataSource,
      refreshing: true,
      height: document.documentElement.clientHeight,
      total: 0,
      start: 0,
      count: 10,
      read: !!props.state.segmentedControlIndex,
      data: [],
      changeTabLoading: false,
      activeKey: 'unread',
      showSkeleton: true,
      showType: false,
      msgType: ''
    }
    this.clearSetStateValue = true
    this.initList()
  }

  componentDidMount() {
    if (this.lv) {
      this.lv.getInnerViewNode().addEventListener(
        'touchstart',
        (this.ts = e => {
          this.tsPageY = e.touches[0].pageY
        })
      )

      const scrollNode = document.scrollingElement ? document.scrollingElement : document.body
      this.lv.getInnerViewNode().addEventListener(
        'touchmove',
        (this.tm = e => {
          this.tmPageY = e.touches[0].pageY
          if (!this.domScroller) return
          if (this.tmPageY > this.tsPageY && this.scrollerTop <= 0 && scrollNode.scrollTop > 0) {
            this.domScroller.options.preventDefaultOnTouchMove = false
          } else {
            this.domScroller.options.preventDefaultOnTouchMove = undefined
          }
        })
      )
    }
  }

  componentWillUnmount() {
    if (this.lv) {
      this.lv.getInnerViewNode().removeEventListener('touchstart', this.ts)
      this.lv.getInnerViewNode().removeEventListener('touchmove', this.tm)
    }
  }

  onScroll = e => {
    //this.scrollerTop = e.scroller.getValues().top
    //this.domScroller = e
  }

  initList = () => {
    let { read, start, count, msgType } = this.state
    start = 0
    let params = { read, start, count }
    if (msgType && msgType !== 'All') {
      params.type = msgType
    }
    let pushToCustom
    const route = window.location.hash.split('/')
    if (route.includes('customMessage')) {
      //自定义消息
      pushToCustom = true
      params = { read, start, count, pushToCustom }
      getCustomMessage.GET('', params).then(res => {
        this.handleInitList(res)
        this.setState({ showSkeleton: false })
      })
    } else {
      api
        .dispatch(
          getList(params, (_state, reduce) => {
            this.handleInitList(reduce?.payload)
          })
        )
        .then(() => {
          this.setState({ showSkeleton: false })
        })
    }
  }
  handleInitList = payload => {
    let { start, count } = this.state
    start = 0
    let hasMore = false
    if (payload.count > count && payload.count > start) {
      start += count
      hasMore = true
    }

    this.setState({
      dataSource: this.state.dataSource.cloneWithRows(payload.items),
      refreshing: false,
      showFinishTxt: true,
      total: payload.count,
      hasMore,
      start,
      data: payload.items,
      changeTabLoading: false
    })
    if (this.domScroller) {
      this.domScroller.scroller.options.animationDuration = 500
    }
  }
  //下拉刷新
  onRefresh = () => {
    if (!this.manuallyRefresh) {
      this.setState({ refreshing: true })
    } else {
      this.manuallyRefresh = false
    }

    this.initList()
  }
  //当所有的数据都已经渲染过，并且列表被滚动到距离最底部不足onEndReachedThreshold个像素的距离时调用
  onEndReached = event => {
    if (!event || (event.scrollbarsOpacity && !event.scrollbarsOpacity.y)) {
      return
    }
    if (this.state.isLoading || !this.state.hasMore) {
      return
    }
    this.setState({ isLoading: true })
    let { read, start, count } = this.state
    let params = { read, start, count }
    let pushToCustom
    const route = window.location.hash.split('/')
    if (route.includes('customMessage')) {
      //自定义消息
      pushToCustom = true
      params = { read, start, count, pushToCustom }
      getCustomMessage
        .GET('', params)
        .then(res => {
          this.handleOnEndReached(res)
        })
        .catch(e => {
          this.setState({ changeTabLoading: false })
        })
    } else {
      api
        .dispatch(
          getList(params, (_state, reduce) => {
            this.handleOnEndReached(reduce?.payload)
          })
        )
        .catch(() => {
          this.setState({ changeTabLoading: false })
        })
    }
  }

  handleOnEndReached = payload => {
    let { start, count } = this.state
    let hasMore = false
    if (payload.count > count && payload.count - start > count) {
      start += count
      hasMore = true
    }
    let data = [...this.state.data, ...payload.items]
    this.setState({
      dataSource: this.state.dataSource.cloneWithRows(data),
      refreshing: false,
      showFinishTxt: true,
      total: payload.count,
      notificationCount: payload.notificationCount,
      hasMore,
      start,
      data,
      isLoading: false,
      changeTabLoading: false
    })
  }

  scrollingComplete = () => {
    // In general, this.scrollerTop should be 0 at the end, but it may be -0.000051 in chrome61.
    //if (this.scrollerTop >= -1) {
    //  this.setState({ showFinishTxt: false })
    //}
  }

  onChange = key => {
    const parmas = tabList.find(item => item.key === key)
    this.setState(
      {
        activeKey: parmas.key,
        read: parmas.isRead,
        data: [],
        changeTabLoading: true
      },
      () => {
        this.initList()
      }
    )
  }

  fnGetMallSensorParams = () => {
    const staff = api.getState()['@common'].me_info?.staff
    const isTraveler = api.getState()['@common'].mallRole?.mallRole === '0'
    const param = {
      category: isTraveler ? i18n.get('散客') : i18n.get('企业'),
      staffId: staff?.userId,
      staffName: staff?.name,
      corName: staff?.corporationId?.name,
      corpId: staff?.corporationId?.id
    }
    return param
  }

  fnMallSensorTrack = trackName => {
    api?.track(trackName, this.fnGetMallSensorParams())
  }

  rowClick(rowData) {
    if (!this.state.read) {
      api.dispatch(updateMessage(rowData.id)).then(() => {
        this.initList()
        if (this.needUpdateRead()) {
          api.invokeService('@layout:messageCenter:read', { count: this.state.total - 1 })
        }
      })
    }

    const { templateId, url, content, sourceType } = rowData
    if (sourceType === 'budgetImport') {
      return Dialog.alert({ content: '请于电脑端点击查看详情', confirmText: i18n.get('知道了') })
    }
    if (templateId === 'asyncExport:success') {
      // 导出的消息处理
      api.invokeService('@layout:preview:file', decodeURI(url))
    } else if (templateId === 'asyncPrint:success') {
      const name = content?.match(/「(\S*)」/)[1].concat('.pdf')
      // 打印的消息处理
      if (window.__PLANTFORM__ === 'APP') {
        api.invokeService('@layout:open:pdf', url, i18n.get('单据打印预览'))
      } else {
        let link = `${window.PREVIEW_DOMAIN}/view/url?url=${encodeURIComponent(url)}&name=${name}`
        const watermark = api.getState()['@common'].waterMark
        link = watermark && watermark !== '' ? link + '&watermark=' + encodeURIComponent(watermark) : link
        api.invokeService('@layout:open:link', link)
      }
    } else {
      if (sourceType === 'hoseMallMessage') {
        this.fnMallSensorTrack('invitation_mail_app_click')
      }
      // 原来的逻辑
      api.invokeService('@message-center:event:message:go', rowData)
    }
    this.clearSetStateValue = false
  }

  handleReadAll = () => {
    let pushToCustom
    let params
    const route = window.location.hash.split('/')
    if (route.includes('customMessage')) {
      //自定义消息
      pushToCustom = true
      params = { pushToCustom }
      getCustomMessage
        .PUT('/all', {}, params)
        .then(() => {
          this.handleReadAllData()
        })
        .catch(() => {})
    } else {
      api.dispatch(readAll()).then(() => {
        this.handleReadAllData()
      })
    }
  }

  needUpdateRead = () => {
    const { notificationCount } = this.state
    // 如果notificationCount有值并且不是-1(哪怕是0)时原生应用角标使用的时notificationCount的值
    return notificationCount !== -1 && notificationCount !== undefined
  }

  handleReadAllData = () => {
    this.initList()
    api.invokeService('@layout:messageCenter:read:all')
    api.invokeService('@message-center:get:message:list', {
      read: false,
      start: 0,
      count: 10
    })
  }

  handleClickMsgType = msgType => {
    this.setState(
      {
        showType: false,
        msgType,
        data: [],
        changeTabLoading: true
      },
      () => {
        this.initList()
      }
    )
  }

  renderContent = () => {
    const { changeTabLoading, total, dataSource, isLoading, refreshing, read, showSkeleton } = this.state
    if (showSkeleton) {
      return <SkeletonListEUI />
    }
    const row = (rowData, sectionID, rowID) => {
      return <MessageItem key={rowID} rowClick={this.rowClick.bind(this)} rowData={rowData} sectionID={sectionID} />
    }
    if (changeTabLoading) {
      return <SkeletonListEUI />
    }
    if (!total) {
      return <EmptyWidget size={200} type="noCentent" tips={'暂无消息'} />
    }
    return (
      <ListView
        ref={el => (this.lv = el)}
        dataSource={dataSource}
        renderFooter={() => {
          return (
            <div style={{ padding: '0.3rem', textAlign: 'center' }}>
              {isLoading ? i18n.get('加载中') : i18n.get('加载完成')}
            </div>
          )
        }}
        renderRow={row}
        initialListSize={10}
        pageSize={10}
        style={{
          height: !read ? 'calc(100% - 84px - 26px)' : 'calc(100% - 50px)',
          marginBottom: '16px',
          background: 'var(--eui-bg-base)'
        }}
        scrollerOptions={{
          scrollbars: true,
          scrollingComplete: this.scrollingComplete
        }}
        refreshControl={<PullToRefresh refreshing={refreshing} onRefresh={this.onRefresh} />}
        onScroll={this.onScroll}
        scrollRenderAheadDistance={200}
        scrollEventThrottle={20}
        onEndReached={this.onEndReached}
        onEndReachedThreshold={10}
      />
    )
  }

  render() {
    const { showType } = this.state
    const showReadAll = !this.state.read && this.state.data.length
    return (
      <div className="message-center-container">
        <div className="message-center-header">
          <div className="message-center-sort" onClick={() => this.setState({ showType: !showType })}>
            <span>{i18n.get('消息类型')}</span>
            <EKBIcon name="#EDico-titledown" className="title-down" />
          </div>
          <div className="message-center-tab">
            <ETabs animated={true} activeKey={this.state.activeKey} dataSource={tabList} onChange={this.onChange} />
          </div>
        </div>
        {showType && (
          <div className="message-center-sort-container">
            {typeList.map(item => (
              <div className="message-center-sort-item" onClick={() => this.handleClickMsgType(item.value)}>
                {item.label}
              </div>
            ))}
          </div>
        )}
        {this.renderContent()}
        {showReadAll ? (
          <div className="message-center-read-all">
            <Button onClick={this.handleReadAll}>{i18n.get('全部已读')}</Button>
          </div>
        ) : null}
      </div>
    )
  }
}
