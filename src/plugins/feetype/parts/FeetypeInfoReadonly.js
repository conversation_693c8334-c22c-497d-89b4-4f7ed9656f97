import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by nanyuantingfeng on 20/07/2017 14:08.
 **************************************************/
import React, { PureComponent } from 'react'
const parseAsMeta = app.require('@lib/parser.parseAsMeta')
const presetFormulaValue = app.require('@lib/parser.presetFormulaValue')
const parseAsReadOnlyFormTemplate = app.require('@lib/parser.parseAsReadOnlyFormTemplate')

import { Dynamic } from '@ekuaibao/template'
const readonly = app.require('@components/dynamic/index.readonly')
import { createForm } from 'rc-form'
const SelectSpecificationForFeeType = app.require('@elements/puppet/SelectSpecificationForFeeType')
const { fnPreviewAttachments } = app.require('@components/utils/fnAttachment')
import { ImportCards } from './ImportCards'
const RiskNotice = app.require('@elements/puppet/RiskNotice')
const Money = app.require('@elements/puppet/Money')
import './FeetypeInfoReadonly.less'
import { app as api } from '@ekuaibao/whispered'
import { List } from 'antd-mobile'
const { getAutoCalResultOnField } = app.require('@bill/utils/autoCalResult')
import { get, debounce, cloneDeep } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnHideFieldsNote, fnFlowHideFields } from '../../../components/utils/fnHideFields'
import { fnFlowShowFields } from '../../../components/utils/fnShowFields'
import { getSpecificationHiddenFields } from '../../bill/utils/billUtils'
import { addReceivingAmount } from './feeTypeInfoHelper'
import { resetForeignInvoiceForEntity } from '../../../components/utils/fnCurrencyObj'
import { enableFlowHiddenFields } from '../../../lib/featbit'


@EnhanceConnect(state => ({
  globalFieldsMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
  smartApproveCharge: state['@common'].powers.INTELLIGENT_APPROVAL,
  allStandardCurrency: state['@common'].allStandardCurrency,
}))
export default class FeetypeInfoReadonly extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      hiddenFields: [],
      template: [],
      value: props.value,
      templateAll: [],
      markInfo: props?.markInfo,
      disableInfo: props?.disableInfo
    }
  }

  initTemplate(props) {
    const { template, globalFields, value, billType, calFields, plan, formAllData } = props
    addReceivingAmount(formAllData, template)
    this.getHiddenFields(template)
    let temp = parseAsMeta(template, globalFields)
    temp = parseAsReadOnlyFormTemplate(temp)
    if (['settlement', 'reconciliation'].includes(billType)) {
      temp.forEach(v => {
        if (billType === 'reconciliation' && v.field === 'supplierSettlement') {
          v.hide = true
        }
        //结算单时费用模版的对账单数据互联字段需要隐藏
        if (billType === 'settlement' && v.field === 'supplierReconciliation') {
          v.hide = true
        }
      })
    }

    const flag = get(value, 'linkDetailEntities')
    if (flag) {
      const linkDetailEntities = temp && temp.find(line => line.name === 'linkDetailEntities')
      if (!linkDetailEntities) {
        const obj = { name: 'linkDetailEntities', type: 'linkDetailEntities', showLabel: false }
        temp.push(obj)
      }
    }
    const _template = []
    const { currentNodeShowFieldMap } = fnFlowShowFields(plan)
    temp.forEach(item => {
      if (currentNodeShowFieldMap[item.name]) {
        _template.push({ ...item, currentNodeShowField: true })
      } else if (calFields?.onFields?.includes(item.name)) {
        const attrs = item?.configs || null || undefined
        if (attrs?.length) {
          let index = attrs.findIndex(el => el.ability === 'caculate' && el.property === 'hide')
          if (index === -1) {
            _template.push(item)
          }
        } else {
          _template.push(item)
        }
      } else {
        _template.push(item)
      }
    })
    this.setState({
      template: _template,
      templateAll: temp
    })
  }

  getHiddenFields = async specification => {
    if(enableFlowHiddenFields()?.handleDetail) return

    const hiddenFields = await getSpecificationHiddenFields(specification)
    this.setState({ hiddenFields })
  }

  componentWillMount() {
    let { bus, needAutoCal = true } = this.props
    setTimeout(() => {
      this.initTemplate(this.props)
      if (needAutoCal) {
        this.updateAutoCalResult(true)
      }
    }, 800)
    bus.watch('element:attachments:line:click', this.handleAttachment)
    bus.on('update:calculate:detail:template', this.updateCalDetailTemplate)
    api.watch('bill:invoice:disable:byApprover', this.handleDisableInvoiceByApprover)
  }

  async componentDidMount() {
    //================解决苹果手机第一次进入费用明细滚动不了的问题================
    const parNode = document.getElementById('FeetypeInfoReadonly')
    if (parNode) {
      const observerOptions = {
        childList: true, // 观察目标子节点的变化，是否有添加或者删除
        attributes: false, // 观察属性变动
        subtree: true // 观察后代节点，默认为 false
      }
      this.observer = new MutationObserver(() => {
        const targetNode = document.querySelector('.fee-type-info-dynamic') //form表单加载了item的时候进行滚动
        if (targetNode && targetNode.children?.length) {
          parNode.scrollTop = 1
          this.observer.disconnect()
        }
      })
      this.observer.observe(parNode, observerOptions)
    }
    //================end===================================================
    const { bus, external, value } = this.props
    const valueCopy = await this.setOverInvoiceBytDimension(value)
    if(valueCopy){
      this.setState({ value:valueCopy })
    }
    external && bus.setFieldsExternalsData?.({ ...external })
  }

  componentWillReceiveProps(nextProps) {
    const { external, template, value, bus, disableInfo, markInfo } = this.props
    if (external !== nextProps.external) {
      nextProps.external && bus.setFieldsExternalsData?.({ ...nextProps.external })
    }
    if (template !== nextProps.template) {
      this.initTemplate(nextProps)
      this.updateAutoCalResult(true)
    }
    if (value !== nextProps.value) {
      this.setState({ value: nextProps.value })
    }
    if (nextProps.disableInfo && disableInfo !== nextProps.disableInfo) {
      this.setState({ disableInfo: nextProps.disableInfo })
    }
    if (nextProps?.markInfo && markInfo !== nextProps.markInfo) {
      this.setState({ markInfo: nextProps.markInfo })
    }
  }

  async setOverInvoiceBytDimension (value){
    const { formAllData, allStandardCurrency } = this.props;
    const legalEntityMultiCurrency = get(formAllData, 'legalEntityMultiCurrency')
    const baseCurrencyId = get(value, 'amount.standardNumCode','')

    // 法人实体
    if(legalEntityMultiCurrency && baseCurrencyId){
      const { items: rates = [] } = await api.invokeService('@common:get:currency:rates:by:id', baseCurrencyId)
      const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
      api.invokeService('@bill:update:dimension:currency', { currency, rates })
      value = resetForeignInvoiceForEntity(value, currency, rates)
      return value
    }
  }


  componentWillUnmount() {
    const { bus } = this.props
    bus.un('element:attachments:line:click', this.handleAttachment)
    bus.un('update:calculate:detail:template', this.updateCalDetailTemplate)
    api.un('bill:invoice:disable:byApprover', this.handleDisableInvoiceByApprover)
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  updateAutoCalResult = debounce(async (checkDefaultValue = false) => {
    const {
      bus = new MessageCenter(),
      billSpecification = {},
      globalFieldsMap,
      submitterId,
      billType,
      feetype,
      formAllData
    } = this.props
    let { value, template } = this.state
    let specification = billType === 'requisition' ? feetype.requisitionSpecificationId : feetype.expenseSpecificationId
    let formData = {
      submitterId: submitterId,
      details: value
    }
    let allData = cloneDeep(formAllData)
    for (let key in allData) {
      formData[key] = allData[key]
    }
    let formValue = {
      feeTypeForm: value,
      feeTypeId: feetype.id,
      specificationId: specification
    }
    bus.emit('feetype:edit:loading:change', true)
    const needUpdateDefaultValue = checkDefaultValue
    const updateAutoCalResultAttribute = true
    await getAutoCalResultOnField(
      globalFieldsMap,
      bus,
      billSpecification,
      formData,
      'detail_',
      formValue,
      undefined,
      template,
      needUpdateDefaultValue,
      updateAutoCalResultAttribute
    )
    bus.emit('feetype:edit:loading:change', false)
  }, 400)

  updateCalDetailTemplate = value => {
    const calValue = value
    let { templateAll: template } = this.state
    const { plan } = this.props
    const { currentNodeShowFieldMap, isShowFileds } = fnFlowShowFields(plan)
    const fields = Object.keys(value)
    fields.forEach(field => {
      if (currentNodeShowFieldMap[field]) {
        return
      }
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        // template[index] = { ...template[index], ...calValue[field] }
        let attrs = calValue[field]
        let autoHide = attrs?.attributeHide
        const _hideVisibility = template[index]?.hideVisibility
        let hasStaffs = true
        const { departments, roles, staffs } = _hideVisibility || { departments: [], roles: [], staffs: [] }
        if (!departments?.length && !roles?.length && !staffs?.length) {
          hasStaffs = false
        }
        if ((autoHide && !hasStaffs) || (autoHide && !fnHideFieldsNote(_hideVisibility))) {
          template.splice(index, 1)
        } else {
          template[index] = { ...template[index], ...calValue[field] }
        }
      }
    })
    if (isShowFileds) {
      template = template.map(field => {
        if (currentNodeShowFieldMap[field.name]) {
          return { ...field, currentNodeShowField: true }
        }
        return field
      })
    }
    //是否设置了审批流字段隐藏
    const flowHiddenFields = fnFlowHideFields(plan)
    flowHiddenFields?.length > 0 &&
      flowHiddenFields.forEach(v => {
        let index = template.findIndex(el => el.name === v)
        if (index !== -1) {
          template.splice(index, 1)
        }
      })

    this.setState({ template: [...template] })
  }

  setFieldsExternal = (external, bus) => {
    bus.setFieldsExternalsData && bus.setFieldsExternalsData({ ...external })
  }

  handleAttachment = (value, index) => {
    fnPreviewAttachments({ value, index })
  }

  handleClickBillBar = () => {
    api.invokeService('@common:get:flow:detail:info', { id: this.props.flowId, isBack: true }).then(() => {
      api.open('@bill:BillDetailModal', {
        params: { id: this.props.flowId }
      })
    })
  }
  // 审批人点击禁用或标记发票
  handleDisableInvoiceByApprover = (id, isMark) => {
    const { onDisableInvoiceByApprover } = this.props
    let { disableInfo, markInfo } = this.state
    if (isMark) {
      // 标记发票
      markInfo[id].mark = true
      this.setState({ markInfo })
    } else {
      // 禁用发票
      disableInfo[id].disable = true
      this.setState({ disableInfo })
    }
    this.forceUpdate()
    onDisableInvoiceByApprover && onDisableInvoiceByApprover(id, isMark)
  }
  renderBillBar() {
    const form = this.props.billForm
    return (
      <div className="fee-type-info-bill-bar" onClick={this.handleClickBillBar}>
        <div className="label">{i18n.get('所属单据')}</div>
        <div className="title">
          <div className="name">{form.title}</div>
          <Money value={form.payMoney} showShorthand className="money" />
        </div>
        <div className="intro">
          <div className="tags">
            <span className="item code">{form.code}</span>
            <span className="item submitter">
              {form.submitterId.name}
              {i18n.get('提交')}
            </span>
          </div>
          <div className="detail">{i18n.get('查看详情')}</div>
        </div>
      </div>
    )
  }
  handleOldDiDiCardClick = thirdPartyOrder => {
    if (thirdPartyOrder.platform === 'DIDI') {
      api
        .invokeService('@bill:get:datalink:template:byId', { entityId: thirdPartyOrder?.id, type: 'CARD' })
        .then(resp => {
          api.open('@bill:DataLinkEntityTripOrderDetailModal', {
            field: get(resp, 'value.data.dataLink.entity.fields', []),
            value: resp?.value,
            title: i18n.get('订单详情')
          })
        })
    }
  }
  render() {
    let {
      bus,
      feetype,
      risk,
      calFields,
      flowId,
      billBus,
      closeAndReturnValue,
      notices,
      isHideWait,
      submitterId,
      showBillBar,
      ownerId,
      isEditTaxRate,
      showAllFeeType,
      apportionVisibleList = [],
      canEditNote,
      isMine,
      isTicketReview,
      invoiceRiskData,
      billType,
      billSpecification,
      notShowModalIfAllInvoiceSuccess = false,
      smartApproveCharge,
      ...others
    } = this.props
    let { template, value, disableInfo, markInfo, hiddenFields } = this.state
    template = presetFormulaValue(risk, template, [], true)
    let orderData = []
    if (value && value.ordersData && value.ordersData.length > 0) {
      orderData = orderData.concat(value.ordersData)
    }
    const detailNo = get(value, 'detailNo', '')

    return (
      <div className="ovr-y-a inertial-rolling" id="FeetypeInfoReadonly" style={{ paddingBottom: 50, height: '100%' }}>
        {showBillBar && this.renderBillBar()}
        <RiskNotice notices={notices} isFeeDetail={true} />
        <SelectSpecificationForFeeType
          smartApproveCharge={smartApproveCharge}
          detailId={value.detailId}
          dataSource={feetype}
          detailNo={detailNo}
        />
        <List>
          <Dynamic
            className="fee-type-info-dynamic"
            {...others}
            hiddenFields={hiddenFields}
            bus={bus}
            create={T => createForm()(T)}
            template={template}
            elements={readonly}
            submitter={submitterId}
            billType={billType}
            value={value}
            calFields={calFields}
            flowId={flowId}
            isMine={isMine}
            detailId={value.detailId}
            isDetail={true}
            billBus={billBus}
            closeAndReturnValue={closeAndReturnValue}
            isHideWait={isHideWait}
            isEditTaxRate={isEditTaxRate}
            ownerId={ownerId}
            showAllFeeType={showAllFeeType}
            apportionVisibleList={apportionVisibleList}
            invoiceRiskData={invoiceRiskData}
            canEditNote={canEditNote}
            isTicketReview={isTicketReview}
            disableInfo={disableInfo}
            markInfo={markInfo}
            feetypeId={feetype?.id}
            notShowModalIfAllInvoiceSuccess={notShowModalIfAllInvoiceSuccess}
            billSpecification={billSpecification}
            businessType={"DETAILS"}
          />
        </List>
        <ImportCards
          orderData={orderData}
          submitter={submitterId}
          isEdit={false}
          handleOldDiDiCardClick={this.handleOldDiDiCardClick}
        />
      </div>
    )
  }
}
