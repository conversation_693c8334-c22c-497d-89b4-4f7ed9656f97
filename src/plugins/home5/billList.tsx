import React, { PureComponent } from 'react'
import styles from './billList.module.less'
import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON>ook from '../../lib/EnhanceTitleHook'
import { app as api } from '@ekuaibao/whispered'
import { QuerySelect } from 'ekbc-query-builder'
const BillContent = api.require<any>('@bill/bill-content/bill-content')
import { orderBy } from 'lodash'
import { getFilterList } from './staticUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import { enableHidingFinishedBills } from '../../lib/featbit'

interface Props {
  specification_group_list?: any[]
  specification_name_map?: any
  specification_EN_map?: any
  KA_GLOBAL_SEARCH_2: boolean
}

interface State {
  current_index: string
  start: number
  filterBy: string
  billList: any[]
  billListCount: number
  showSkeleton: boolean,
  delegatedFilter: string,
  sortValue: {value: string, order: string}[]
}

interface SorRuleInterface {
  [key: string]: number
  rejected: number
  paid: number
  draft: number
  sending: number
  receiving: number
  approving: number
}

const sortRule: SorRuleInterface = {
  rejected: 1, //驳回
  paid: 2, //已支付
  draft: 3, //草稿
  sending: 4, //待寄送
  receiving: 5, //待收单
  approving: 6 //审批中
}


@EnhanceConnect((state: any) => {
  return {
    specification_group_list: state['@home'].specificationWithVersion.specification_group_list,
    specification_name_map: state['@home'].specificationWithVersion.specification_name_map,
    specification_EN_map: state['@home'].specificationWithVersion.specification_EN_map,
    KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2
  }
})
// @ts-ignore
@EnhanceTitleHook(i18n.get('我的单据'))
export default class BillList extends PureComponent<Props, State> {
  private initState: string
  private defaltState: string

  getInitialDelegatedFilter = () => {
    try {
      const userInfoId = api.getState()['@common'].me_info?.staff?.id + '-filter' || 'bill-select-filter'
      const preSelectFilter = JSON.parse(localStorage.getItem(userInfoId))
      let currentDelegatedFilter = 'DELEGATE_TO_ME'
      if (preSelectFilter) {
        const item = preSelectFilter.find((item: any) => item.type === 'entrust')
        const firstChildItemType = item?.children[0].type
        if (firstChildItemType) {
          currentDelegatedFilter = firstChildItemType
        }
      }
      return currentDelegatedFilter
    } catch (error) {
      return 'DELEGATE_TO_ME'
    }
  }

  getInitialFilter = () => {
    const staffId = api.getState()['@common'].me_info?.staff?.id
    if (enableHidingFinishedBills()) {
      const multiSelectStorageKey = staffId + '-multi-filter'
      try {
        return JSON.parse(localStorage.getItem(multiSelectStorageKey))
      } catch (error) {
        console.error(error)
        return null
      }
    }
    try {
      return JSON.parse(localStorage.getItem(staffId + '-filter'))
    } catch (error) {
      console.error(error)
      return null
    }
  }

  getQueryByFilterState = () => {
    const storageState = this.getInitialFilter()
    const _defaltState = this.defaltState
    if (storageState) {
      const query = new QuerySelect()
      const filterValue = storageState.map((el: any) => ({
        type: el.type,
        value: el.children.map((child: any) => child.type)
      }))
      filterValue.forEach((el: any) => {
        if (el.type === 'state') {
          if (!el?.value?.includes('all')) {
            if (enableHidingFinishedBills()) {
              query.filterBy(`state.in(${el.value.map((s: string) => `"${s}"`).join(',')})`)
            } else {
              query.filterBy(`state == "${el.value.join()}"`)
            }
          } else {
            query.filterBy(_defaltState)
          }
        } else if (el.type === 'formType') {
          if (!el?.value?.includes('all')) {
            if (enableHidingFinishedBills()) {
              query.filterBy(`formType.in(${el.value.map((s: string) => `"${s}"`).join(',')})`)
            } else {
              query.filterBy(`formType == "${el.value.join()}"`)
            }
          }
        }
      })
      return query.value().filterBy
    } else {
      return _defaltState
    }
  }

  getSortCacheKey = () => {
    return api.getState()['@common'].me_info?.staff?.id + '-sort-of-applet'
  }

  getInitialSortValue = () => {
    const defaultSortValue = [{ value: 'form.submitDate', order: 'desc' }]
    try {
      const currentSortValue = JSON.parse(localStorage.getItem(this.getSortCacheKey()))
      return currentSortValue || defaultSortValue
    } catch (error) {
      return defaultSortValue
    }
  }

  constructor(props: Props) {
    super(props)
    this.state = {
      current_index: '',
      start: 0,
      filterBy: '',
      billList: [],
      billListCount: 0,
      showSkeleton: true,
      delegatedFilter: this.getInitialDelegatedFilter(),
      sortValue: this.getInitialSortValue(),
    }
    this.defaltState = enableHidingFinishedBills()
        ? `state.in("rejected","draft","sending","receiving","receivingExcep","approving","pending","paying","paid")`
        : `state.in("rejected","draft","sending","receiving","receivingExcep","approving","pending","paying")`
    this.initState = this.getQueryByFilterState()
  }

  componentDidMount() {
    this.fnGetBillListByType('all')
  }

  handleItemClick = (flow: any) => {
    api.invokeService('@home:save:specification').then(() => {
      api.invokeService('@home:click:bill', flow, 'homePage')
    })
  }

  handleChangeBillType = (item: string, filterByStr: string) => {
    api.invokeService('@home:setSelectBillsType', item)
    this.fnGetBillListByType(item, filterByStr)
    this.setState({ current_index: item })
  }

  handleChangeBillFilter = ({ billGroupId, filterTypes, filterSearch }: any) => {
    api.invokeService('@home:setSelectBillsType', billGroupId)
    let filterBy =
      `(form.specificationId.originalId.specificationGroupId.containsIgnoreCase("${billGroupId}"))` + filterSearch
    if (billGroupId === 'all' || !billGroupId?.length) {
      filterBy = filterSearch.slice(2)
    }
    let isAllState = false

    let delegatedFilter: any = 'DELEGATE_TO_ME'
    if (!!filterTypes?.length) {
      const query = new QuerySelect().filterBy(filterBy)
      filterTypes.map((el: any) => {
        if (el?.key === 'state') {
          if (!el?.value?.includes('all')) {
            if (enableHidingFinishedBills()) {
              query.filterBy(`state.in(${el.value.map((s: string) => `"${s}"`).join(',')})`)
            } else {
              query.filterBy(`state == "${el.value.join()}"`)
            }
          } else {
            isAllState = true
          }
        } else if (el?.key === 'formType') {
          if (!el?.value?.includes('all')) {
            if (enableHidingFinishedBills()) {
              query.filterBy(`formType.in(${el.value.map((s: string) => `"${s}"`).join(',')})`)
            } else {
              query.filterBy(`formType == "${el.value.join()}"`)
            }
          }
        } else if (el?.key === 'entrust') {
          delegatedFilter = el.value.join()
        }
      })
      if (isAllState) query.filterBy(this.defaltState)
      filterBy = query.value().filterBy
    }
    this.setState({ filterBy, start: 0, billList: [], delegatedFilter }, () => {
      this.fnGetBillList(filterBy, delegatedFilter, this.state.sortValue)
    })
  }

  handleSortChange = (orderBy: any) => {
    this.setState({ sortValue: orderBy, start:0, billList: [] }, () => {
      localStorage.setItem(this.getSortCacheKey(), JSON.stringify(orderBy))
      this.fnGetBillList(this.state.filterBy, this.state.delegatedFilter, orderBy)
    })
  }

  fnGetBillListByType = (item: string, filterByStr?: string) => {
    const query = new QuerySelect().filterBy(filterByStr || this.state.filterBy || this.initState)
    if (item !== 'all') {
      query.filterBy(`form.specificationId.originalId.specificationGroupId.containsIgnoreCase("${item}")`)
    }
    const filterBy = query.value().filterBy
    this.setState({ filterBy, start: 0, billList: [] }, () => {
      this.fnGetBillList(filterBy)
    })
  }

  getLastAction = () => {
    try {
      const { lastBillOperation } = api.getState()['@bill']
      const lastAction = lastBillOperation?.actionType
      console.log(lastAction, 'lastAction')
      // 2秒内
      if(lastAction && Date.now() - lastBillOperation.timestamp < 1000 * 2){
        api.invokeService('@bill:record:last:operation', {})
        return lastAction
      }
    } catch(e) {}
  }

  fnGetBillList = (filterBy: string, delegatedFilter?: string, _orderBy?: {value: string, order: string}[]) => {
    const { start, billList, delegatedFilter: current_delegatedFilter, sortValue } = this.state
    this.setState({ showSkeleton: start === 0 })
    // 获取本地筛选配置

    api
      .invokeService('@common:search:bill', {
        filterSystemBill: true,
        filterBy: filterBy,
        hasArchived: true,
        orderBy: _orderBy || sortValue,
        limit: { start, count: 20 },
        delegatedFilter: delegatedFilter || current_delegatedFilter,
        lastAction: this.getLastAction()
      })
      .then((result: any) => {
        let arr = result.items || []
        // arr = arr.filter((el: any) => el.flow.state !== 'paid' && el.flow.state !== 'archived')
        // arr.forEach((o: any) => (o.sIndex = sortRule[o.flow.state]))
        // arr = orderBy(arr, ['sIndex'], ['asc'])
        this.setState({
          billList: billList.concat(arr),
          billListCount: result?.count,
          start: start + arr?.length,
          showSkeleton: false
        })
      })
  }

  handleLoadMore = () => {
    const { filterBy } = this.state
    this.fnGetBillList(filterBy)
  }

  formatEnSpecificationList = () => {
    const { specification_group_list } = this.props

    return specification_group_list.map(group => {
      return {
        ...group,
        label: i18n.currentLocale === 'en-US' && group.enName ? group.enName : group.label
      }
    })
  }

  render() {
    const { current_index, billList, billListCount, showSkeleton, sortValue } = this.state
    const { specification_name_map, specification_EN_map , KA_GLOBAL_SEARCH_2 } = this.props
    const nameMap = i18n.currentLocale === 'en-US' ? specification_EN_map : specification_name_map
    const menuList = [{ type: 'all', label: i18n.get('全部'), checkVisible: true }].concat(
      this.formatEnSpecificationList()
    )
    const activeText = current_index ? nameMap?.[current_index] : i18n.get('全部')
    return (
      <div className={styles['bill-list']}>
        <BillContent
          className={'bill-list5-content'}
          showSkeleton={showSkeleton}
          enableGlobalSearch={KA_GLOBAL_SEARCH_2}
          activeType={current_index}
          activeText={activeText}
          billType={current_index}
          menuList={menuList}
          dataSource={billList}
          showFilter={true}
          showSort={true}
          sortValue={sortValue}
          filterList={getFilterList()}
          searchFrom="home"
          applyNewFilter={true}
          useNewStyle
          onHandleClick={this.handleItemClick}
          onChangeBillType={this.handleChangeBillType}
          onChangeBillFilter={this.handleChangeBillFilter}
          onSortChange={this.handleSortChange}
          style={{ paddingBottom: 180, marginTop: 8 }}
          customLoadMore={this.handleLoadMore}
          dataSourceTotal={billListCount}
        />
      </div>
    )
  }
}
