import React from 'react'
import styles from './routeDetailInfo.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { TextareaItem, InputItem } from 'antd-mobile'
import { Button, Checkbox, Dialog } from '@hose/eui-mobile'
import PageContainer from '../page-container'
import { get, ceil, floor } from 'lodash'
import moment from 'moment'
import EnhanceTitleHook from '../../lib/EnhanceTitleHook'
import loadable from '@loadable/component'
import ARROWRIGHT from './images/arrow-right.svg'
import { Resource } from '@ekuaibao/fetch'
import { getAttachments } from './utils/utils'
import { defaultInvalidSuffixes, onInvalidFile } from '../../lib/invalidSuffixFile.tsx'
import { submitTrafficRecordLog } from '../../lib/dataflux/mapLogEvent'
import { toast } from '../../lib/util'
import { OutlinedTipsAdd, OutlinedTipsMaybe } from '@hose/eui-icons'
import { getModifyMilesLogs } from './mycarbusiness.action'
import { localStorageSet } from '../../lib/util'
const { getStrLastWord } = api.require('@components/utils/fnDataLinkUtil')
const FilesUploader = loadable(() => import('@ekuaibao/uploader/esm/FilesUploader'))
const HuaWeiUploader = loadable(() => import('@ekuaibao/uploader/esm/HuaWeiUploader'))
const EKBFormItem = api.require('@bill/elements/EKBFormItem')
const { buildData, fnClickAttachments, getUploadUrl, getToken, uploadDone } = api.require(
  '@components/utils/fnAttachment'
)
const UploadItem = api.require('@elements/puppet/Upload/UploadItem')
const maxSize = 64
const privateCarAction = new Resource('/api/v2/privateCar')

@EnhanceConnect(state => ({
  positionData: state['@mycarbusiness'].positionData,
  uploadServiceUrl: state['@common'].uploadServiceUrl,
  currentPositionData: state['@mycarbusiness'].currentPositionData
}))
@EnhanceTitleHook(i18n.get('行车记录'))
export default class routeDetailInfo extends PageContainer {
  inputRef = React.createRef();
  constructor(props) {
    super(props)
    let { positionData, params, currentPositionData } = this.props
    let { readonly, state } = params
    // readonly = false
    if (readonly) {
      positionData = this.getPositionData(currentPositionData)
    } else {
      currentPositionData = positionData.finished
      positionData = { ...positionData, ...this.getPositionData(currentPositionData) }
    }

    console.log('positionData:', positionData)
    this.state = {
      state,
      map: null, //地图对象
      readonly,
      data: positionData, //定位数据
      token: '',
      uploaderFileList: [],
      attachmentInfos: positionData['附件'] || [],
      attachmentArrs: [],
      modifyMilesConfig: {},
      showModifyMilesAlert: !localStorage.getItem('showModifyMilesAlert'),
      ignoreAlertCheck: false,
      showRemarkRequireAlert: false,
      showAttachmentsRequireAlert: false,
      modifyMilesLogData: {},
      showCauseRequiredAlert: false,
      causeRequiredConfig: false,
    }
    api.invokeService('@mycarbusiness:set:position:data', {})
  }

  getPositionData = currentPositionData => {
    let { form } = currentPositionData
    let obj = {}
    for (let key in form) {
      let ks = key.split('_'),
        ksl = ks.length
      let rkey = key.split('_')[ksl - 1]
      if (rkey == '目的地') {
        //@i18n-ignore
        rkey = 'endNode'
      } else if (rkey == '出发地') {
        //@i18n-ignore
        rkey = 'startNode'
      } else if (rkey == '途经地') rkey = 'wayPoints' //@i18n-ignore
      obj[rkey] = form[key]
    }

    return obj
  }

  async componentWillMount() {
    super.componentWillMount()
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
    
    getToken().then(token => {
      this.setState({ token })
    })

    const { data, attachmentInfos } = this.state
    const { currentPositionData } = this.props
    let { readonly } = this.props.params || {}
    if (!readonly) {
      /** 给实际里面默认填充数据 */
      this.setActualMiles(data['行驶总里程'])
    }
    const isAuto = data?.['记录方式'] === '自动打点'
    // 获取私车公用配置
    privateCarAction.GET('/platform').then(res => {
      if (
        res.value &&
        res.value.properties &&
        (res.value.properties.modifyMilesConfig || res.value.properties.autoModifyMilesConfig)
      ) {
        this.setState({
          modifyMilesConfig: isAuto
            ? res.value.properties.autoModifyMilesConfig || {}
            : res.value.properties.modifyMilesConfig,
          causeRequiredConfig: res.value.properties?.causeRequiredConfig || false,
        })
      }
    })

    // 获取附件信息
    const attachmentIds = attachmentInfos.map(el => el.fileId.id || el.fileId)
    if (attachmentIds && attachmentIds.length) {
      getAttachments(attachmentIds).then(res => {
        this.setState({ attachmentArrs: res })
      })
    }
    const params = {
      limit: {
        start: 0,
        count: 50
      },
      select: 'dataLinkId(id,name,code),operatorId(id,name,code,email,cellphone,note),entityId(fields),`...`'
    }

    // 获取日志信息
    const modifyMilesLogData = await getModifyMilesLogs(currentPositionData?.id, params)
    this.setState({ modifyMilesLogData: modifyMilesLogData })
  }

  /** 更新数据 */
  updateRouteDescription = () => {
    let {
      data,
      state,
      causeRequiredConfig,
      modifyMilesConfig,
      modifyMilesConfig: { modifyMilesRangeKm = 0, enabled = false, modifyMilesRangePercent = 0 }
    } = this.state
    const sjLC = Number(data['实际里程'])
    const zLC = Number(data['行驶总里程'])
    let hasFormError = false;

    if (enabled) {
      if (isNaN(sjLC)) {
        Dialog.alert({ title: i18n.get('提示'), content: i18n.get('必须填写实际里程') })
        return
      }

      // 实际里程不能
      if (modifyMilesRangeKm > 0 && (sjLC - modifyMilesRangeKm > zLC || zLC - modifyMilesRangeKm > sjLC)) {
        Dialog.alert({
          content: i18n.get(
            `根据企业要求，修正后的里程必须在
            ${ceil(zLC - modifyMilesRangeKm, 2) > 0 ? ceil(zLC - modifyMilesRangeKm, 2) : 0}Km
            -
            ${floor(zLC + modifyMilesRangeKm, 2)}Km
            范围内`
          )
        })
        return
      }

      const range = (zLC * modifyMilesRangePercent) / 100
      // 实际里程不能
      if (modifyMilesRangePercent > 0 && (sjLC - range > zLC || zLC - range > sjLC)) {
        Dialog.alert({
          content: i18n.get(
            `根据企业要求，修正后的里程必须在
            ${ceil(zLC - range, 2) > 0 ? ceil(zLC - range, 2) : 0}Km
            -
            ${floor(zLC + range, 2)}Km
            范围内`
          )
        })
        return
      }

      if (sjLC !== zLC && modifyMilesConfig.remark && !data.entity) {
        this.setState({ showRemarkRequireAlert: true });
        hasFormError = true;
      }
      
      if (sjLC !== zLC && modifyMilesConfig.attachments && !data.attachments) {
        this.setState({ showAttachmentsRequireAlert: true });
        hasFormError = true;
      }
    }

    if (!data.cause && causeRequiredConfig) {
      this.setState({ showCauseRequiredAlert: true });
      hasFormError = true;
    }

    if (hasFormError) return;

    let { id, entity = '', cause = '', attachments = [] } = data
    const attachmentsArr = (attachments || []).map(file => {
      const id = get(file, 'fileId.id')
      if (id) {
        const { key, name } = file
        return { key, fileId: id, fileName: name }
      }
      return file
    })
    const subsidiesAmount = {
      ...data['补助标准'],
      standard: new Big(data['实际里程'] * data['补助标准'].standard).round(3, 0).toFixed(2)
    }
    const saveData = {
      id,
      entity,
      cause,
      actualMiles: Number(data['实际里程']).toFixed(2),
      attachments: attachmentsArr,
      subsidiesAmount
    }

    if (sjLC !== zLC && this.state.showModifyMilesAlert) {
      Dialog.confirm({
        content: [
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <p style={{ marginBottom: '16px' }}>{i18n.get('修正里程提交后不可修改')}</p>
            <Checkbox onChange={e => this.setState({ ignoreAlertCheck: e })}>{i18n.get('不再提示')}</Checkbox>
          </div>
        ],
        confirmText: i18n.get('提交'),
        onConfirm: () => {
          this.submitRouteInfo(saveData)
          this.state.ignoreAlertCheck && localStorageSet('showModifyMilesAlert', 'N')
        }
      })
    } else {
      this.submitRouteInfo(saveData)
    }
  }

  submitRouteInfo = data => {
    submitTrafficRecordLog(data)
    api.invokeService('@mycarbusiness:set:position:updateRouteDescription', data).then(res => {
      console.log('触发标记')
      api.invokeService('@mycarbusiness:get:position:getRoute').then(_ => {
        api.go('/routelist', true)
        // if (state == 'RUNNING' || state == 'END') {
        //   api.go('/routelist', true)
        // } else {
        //   api.go(-1)
        // }
      })
    })
  }

  /** 修改备注 */
  setNote = value => {
    let { data } = this.state
    data['备注'] = value //@i18n-ignore
    data.entity = value
    this.setState({ data: { ...this.state.data } })
  }

  /** 事由修改 */
  setCause = value => {
    let { data } = this.state
    data['事由'] = value //@i18n-ignore
    data.cause = value
    this.setState({ data: { ...this.state.data } })
  }

  /** 实际里程修改 */
  setActualMiles = value => {
    let { data } = this.state
    value = value + ''
    if (value.split('.').length > 2 || value.indexOf('.') === 0) {
      value = value.replace(/.$/gi, '')
    }
    data['实际里程'] = value //@i18n-ignore
    data.actualMiles = value
    this.setState({ data: { ...this.state.data } })
  }

  getTotalTime = formatStr => {
    let m = Math.ceil((formatStr % 3600000) / 60000)
    let h = Math.floor(formatStr / 3600000)
    if (h > 0) {
      return i18n.get('{__k0}时{__k1}分', { __k0: h, __k1: m })
    }
    return i18n.get('{__k0}分', { __k0: m })
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  handleDone = uploaderFileList => {
    const { data, attachmentInfos } = this.state;
    const { attachments = [] } = data;

    // 不直接清空 uploaderFileList，保持 UI 的文件列表，避免闪烁
    const tempUploaderFileList = [...uploaderFileList];

    uploadDone.call(this, {
      list: uploaderFileList,
      value: attachments,
      onChange: list => {
        const updatedAttachmentInfos = [
          ...attachmentInfos,
          ...uploaderFileList.map(el => get(el, 'response.fileId')),
        ];

        const newWayPoint = { ...data, attachments: list };
        this.setState({
          data: newWayPoint,
          attachmentInfos: updatedAttachmentInfos,
          uploaderFileList: tempUploaderFileList,
        });
        this.setState({ uploaderFileList: [] });
      },
    });
  };

  handleOnStart = file => {
    if (file && file.length > 0) {
    } else {
      toast.info(i18n.get('attachment-max-msg', { __k0: maxSize - 4 }))
    }
  }

  handleDelete = (line, index) => {
    let { data, attachmentInfos } = this.state
    let { attachments } = data
    attachments = attachments?.slice(0) // Copy 一份, 不让我列表不会刷新
    attachmentInfos = attachmentInfos?.slice(0)
    attachments?.splice(index, 1)
    attachmentInfos?.splice(index, 1)
    this.setState({ data: { ...data, attachments } })
    this.setState({ attachmentInfos: attachmentInfos })
  }

  handleLineClick = async (line, index) => {
    const {
      data: { attachments },
      attachmentInfos
    } = this.state
    const value = attachments.map(el => {
      const { fileId, key, fileName } = el
      const { url = '', thumbUrl = '' } = attachmentInfos.find(el => el.id === fileId) || {}
      return {
        key,
        fileName,
        fileId: {
          url,
          thumbUrl,
          id: fileId,
          key
        }
      }
    })
    fnClickAttachments({ bus: null, value, line, index })
  }

  goMap = () => {
    const {
      data: { startNode, endNode, wayPoints, code }
    } = this.state
    const isAuto = this.state.data['记录方式'] === '自动打点'
    api.open('@mycarbusiness:MyCarBusinessDetailInfoMap', {
      from: startNode,
      to: endNode,
      wayPoints,
      isAuto: isAuto,
      id: code
    })
  }

  /**
   *
   * @param {File[]} invalidFiles // 错误文件列表
   * @param {'invilidaFileType' | 'otherInvalid'} type // 文件错误类型
   */
  handleInvalidFiles = (invalidFiles, type) => {
    let { invalidSuffixes = defaultInvalidSuffixes } = this.props
    onInvalidFile(invalidFiles, invalidSuffixes, type)
  }

  checkItemValue = (item) => {
    const { data, causeRequiredConfig } = this.state
    if (item === 'remark') {
      data.entity === '' ? this.setState({ showRemarkRequireAlert: true }) : this.setState({ showRemarkRequireAlert: false })
    }
    if(item === 'cause' && causeRequiredConfig){
      data.cause ? this.setState({ showCauseRequiredAlert: false }) : this.setState({ showCauseRequiredAlert: true })
    }
  }

  renderFileItem = (line, index, isEdit) => {
    let item = {}
    if (line.status) {
      item = line
    } else {
      const { key, fileName, fileId } = line
      const { url = '', thumbUrl = '' } = this.state.attachmentArrs.find(el => el.id === fileId) || {}
      item = {
        key,
        name: fileName || key,
        url: url || line.url,
        response: {
          fileId: {
            url: url || line.url,
            thumbUrl: thumbUrl || line.thumbUrl,
            id: fileId
          }
        }
      }
    }
    return (
      <UploadItem
        key={index}
        isEdit={isEdit}
        file={item}
        className="attachment-item"
        onRemoveItem={() => this.handleDelete(line, index)}
        onClickItem={() => this.handleLineClick(line, index)}
      />
    )
  }

  //@ts-ignore
  renderFilesUploader = () => {
    const { token } = this.state
    const { uploadServiceUrl, invalidSuffixes = defaultInvalidSuffixes } = this.props
    const uploadUrl = uploadServiceUrl && uploadServiceUrl.uploadUrl
    return (
      <div style={{ marginBottom: '16px' }}>
        {window.__PLANTFORM__ == 'HUAWEI' && window.isAndroid ? (
          <HuaWeiUploader
            action={IS_STANDALONE ? getMinioUploadUrl() : uploadUrl}
            onChange={this.handleChange}
            onDone={this.handleDone}
            onStart={this.handleOnStart}
            data={file => buildData(file, token, uploadServiceUrl)}
          >
            <Button block category="secondary" size="middle">
              <div className="bottom-bar-text">
                <OutlinedTipsAdd style={{ marginRight: '4px' }} />
                {i18n.get('添加附件')}
              </div>
            </Button>
          </HuaWeiUploader>
        ) : (
          <FilesUploader
            accept=""
            action={IS_STANDALONE ? getUploadUrl : uploadUrl}
            type={IS_STANDALONE}
            maxSize={maxSize}
            onChange={this.handleChange}
            onDone={this.handleDone}
            onStart={this.handleOnStart}
            onInvalidFile={this.handleInvalidFiles}
            invalidSuffixes={invalidSuffixes}
            data={file => buildData(file, token, uploadServiceUrl)}
          >
            <Button block category="secondary" size="middle">
              <div className="bottom-bar-text">
                <OutlinedTipsAdd style={{ marginRight: '4px' }} />
                {i18n.get('添加附件')}
              </div>
            </Button>
          </FilesUploader>
        )}
      </div>
    )
  }

  renderFormOther = step => {
    const { data, readonly, modifyMilesConfig, causeRequiredConfig, showCauseRequiredAlert } = this.state
    // @i18n-ignore
    let normal = []
    if (step === 1) {
      const bzjeObj = data['补助金额'] || data['补助金额元'] || {}
      normal = [
        {
          label: i18n.get('事由'),
          content: data['事由'] || '', //@i18n-ignore
          required: causeRequiredConfig && !readonly,
          error: showCauseRequiredAlert,
          errorText: i18n.get('请填写事由'),
          children:
            readonly === 'true' ? (
              ''
            ) : (
              <span className="item-content">
                <InputItem
                  className="textarea-input"
                  value={data['事由'] || ''} //@i18n-ignore
                  count={14}
                  maxLength={14}
                  extra={<img src={ARROWRIGHT} />}
                  onChange={(value) => {
                    this.setCause(value);
                    this.checkItemValue('cause');
                  }}
                  placeholder={i18n.get('请输入事由')}
                />
              </span>
            )
        },
        {
          label: i18n.get('补助金额'),
          content: bzjeObj.standardSymbol + new Big(data['实际里程'] * data['补助标准'].standard).round(3, 0).toFixed(2)
        },
        {
          label: i18n.get('补助标准（每公里）'),
          // @i18n-ignore
          content: data['补助标准'].standardSymbol + data['补助标准'].standard
        },
        { label: i18n.get('行驶日期'), content: moment(data['行驶日期']).format('YYYY-MM-DD') } // @i18n-ignore
      ]
    } else if (step === 2) {
      normal = [
        {
          label: i18n.get('里程计算方式'),
          content: data['里程计算方式']
        },
        {
          label: i18n.get('实际里程'),
          content: `${data['实际里程'] || data['行驶总里程']}km`, //@i18n-ignore
          children:
            readonly === 'true' || !modifyMilesConfig?.enabled ? (
              ''
            ) : (
              <span className="item-content" style={{ margin: '-5px 0' }}>
                <InputItem
                  type="money"
                  className="textarea-input"
                  value={data['实际里程'] || ''} //@i18n-ignore
                  min={0}
                  max={100}
                  extra={
                    <div style={{ color: '#2555FF', display: 'flex' }} onClick={() => this.inputRef.current?.focus()}>
                      <span>{i18n.get('修正')}</span>
                      <img src={ARROWRIGHT} />
                    </div>
                  }
                  ref={this.inputRef}
                  onChange={this.setActualMiles.bind(this)}
                  placeholder={i18n.get('请输入实际里程')}
                />
              </span>
            )
        },
        {
          label: i18n.get('参考里程'),
          // @i18n-ignore
          content: `${data['行驶总里程']}km`
        },
        // @i18n-ignore
        { label: i18n.get('行驶总时间'), content: this.getTotalTime(data['行驶总时间']) }
      ]
    }
    return (
      <div className="item-rows">
        {normal.map((item, index) => {
          return this.renderItem(item, index, normal.length, item?.required)
        })}
      </div>
    )
  }

  renderItem(item, index, length, required) {
    let { children } = item
    const hideBorder = index + 1 === length
    return (
      <EKBFormItem
        value={item}
        key={index}
        required={required}
        children={children}
        style={hideBorder ? { borderBottom: 'none' } : {}}
      />
    )
  }

  renderModifyMilesLog = () => {
    const displayLogs = this.state.modifyMilesLogData.items?.filter(item => item.logType === 'UPDATE')
    const hasActualMileage = displayLogs?.some(
      item => Array?.isArray(item?.logs) && item?.logs?.some(log => log.fieldLabel === '实际里程')
    )
    return (
      hasActualMileage && (
        <>
          <div className="line" />
          <div className="operation-log">
            <div className="log-title">{i18n.get('操作日志')}</div>
            {displayLogs?.slice(0, 5)?.map((item, index) => {
              const milesLog = item.logs?.find(log => log.fieldLabel === '实际里程')
              return (
                <div className="log-item" key={index}>
                  <span>{moment(item.createTime).format('YYYY.MM.DD')}&nbsp;&nbsp;</span>
                  <span>
                    {item.operator?.name}把【实际里程】由 {milesLog?.beforeContent}Km 修改为 {milesLog?.afterContent}Km
                  </span>
                </div>
              )
            })}
          </div>
        </>
      )
    )
  }

 // 返回按钮执行和提交一样的校验 
  backHookCallback = () => {
    this.updateRouteDescription()
  }

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
    api.invokeService('@mycarbusiness:set:position:data', {})
    api.invokeService('@layout:set:header:right', { show: false })
  }

  onNameClick = (e, name, address) => {
    e.stopPropagation()
    e.preventDefault()
    Dialog.alert({
      content: <><p>地址名称：{name}</p>
        <p>详细地址：{address}</p></>,
    })
  }

  render() {
    const {
      data,
      uploaderFileList,
      attachmentInfos,
      modifyMilesConfig,
      showRemarkRequireAlert,
      showAttachmentsRequireAlert,
      modifyMilesLogData
    } = this.state
    let { readonly } = this.state
    const remark = {
      label: i18n.get('备注'),
      content: data['备注'], //@i18n-ignore
      children:
        readonly === 'true' ? (
          ''
        ) : (
          <span style={{ marginLeft: '-15px' }}>
            <TextareaItem
              className="textarea-input"
              value={data['备注']} //@i18n-ignore
              autoHeight={true}
              onChange={this.setNote.bind(this)}
              count={200}
              maxLength={200}
              rows={3}
              placeholder={i18n.get('自定义备注信息')}
            />
          </span>
        )
    }

    let className = readonly ? 'map-container-info info_readonly' : 'map-container-info'

    return (
      <div className={styles['routeDetailInfo-view-wrapper']}>
        <div className={className}>
          <div className="line" />
          {this.renderFormOther(1)}
          <div className="line" />
          {this.renderFormOther(2)}
          <div className="line" />
          <div className="item-rows remark-row" onChange={() => this.checkItemValue('remark')}>
            {this.renderItem(remark, 0, 1, !readonly && modifyMilesConfig?.remark && data['行驶总里程'] !== data['实际里程'])}
            {showRemarkRequireAlert && (
              <div className="remark-require-alert" style={{ color: 'red' }}>
                {i18n.get('请添加备注')}
              </div>
            )}
          </div>
          <div className="line" />
          <div className="item-rows">
            <div className="ekb-form-item" style={{ borderBottom: 'none' }}>
              <span className="item-label">
                {i18n.get('附件')}
                {!readonly && modifyMilesConfig?.attachments && data['行驶总里程'] !== data['实际里程'] && (
                  <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
                )}
              </span>
            </div>
            <div className="positionNode-attachment">
              {attachmentInfos &&
                attachmentInfos.length > 0 &&
                attachmentInfos.map((line, index) => this.renderFileItem(line, index, !readonly))}
              {uploaderFileList &&
                uploaderFileList.length > 0 &&
                uploaderFileList.map((line, index) => this.renderFileItem(line, index, !readonly))}
              {attachmentInfos.length === 0 && uploaderFileList.length === 0 && (
                <div style={{ marginBottom: 12, color: '#1d2b3d' }}>{i18n.get('暂无附件')}</div>
              )}
            </div>
            {!readonly && this.renderFilesUploader()}
            {showAttachmentsRequireAlert && attachmentInfos.length === 0 && uploaderFileList.length === 0 && <div className="attachments-require-alert">{i18n.get('请上传附件')}</div>}
          </div>
          <div className="line" />
          <section className="sec-address" onClick={this.goMap}>
            <div className="row-title">
              <span>{i18n.get('行程信息')}</span>
              <span>{i18n.get('查看行驶路线')}</span>
            </div>
            <div className="fp-middle">
              <div className="fp-item">
                <div className="dot dotGre" />
                <span className="font">
                  {data?.startNode?.name || data?.startNode?.address}
                  {data?.startNode?.name && <OutlinedTipsMaybe onClick={(e) => this.onNameClick(e, data?.startNode?.name, data?.startNode?.address)} style={{ marginLeft: 4, marginTop: 3 }} />}
                </span>
              </div>
              <div className="fp-item">
                <div className="dot dotRed" />
                <span className="font">
                  {data?.endNode?.name || data?.endNode?.address}
                  {data?.endNode?.name && <OutlinedTipsMaybe onClick={(e) => this.onNameClick(e, data?.endNode?.name, data?.endNode?.address)} style={{ marginLeft: 4, marginTop: 3 }} />}</span>
              </div>
              {readonly && (
                <div className="fp-item">
                  <span className="font txt">{`${i18n.get('实际里程')}：${data['实际里程']}km`}</span>
                </div>
              )}
              <div className="fp-item" style={{ marginBottom: 0 }}>
                <span className="font txt time">{moment(data['起始时间']).format('YYYY-MM-DD HH:mm')}</span>
              </div>
            </div>
          </section>
          {readonly && modifyMilesLogData?.count > 1 && this.renderModifyMilesLog()}
        </div >
        {!readonly && (
          <div className="btns">
            <Button block category="primary" size="middle" onClick={this.updateRouteDescription}>
              {i18n.get('提交')}
            </Button>
          </div>
        )
        }
      </div>
    )
  }
}
