import React from 'react'
import styles from './addPayee.module.less'
import { app, app as api } from '@ekuaibao/whispered'
import { Button, Dialog, TextArea, Toast, Selector, Form, Input } from '@hose/eui-mobile'
import { EnhanceConnect } from '@ekuaibao/store'
import EnhanceTitleHook from './enhance-title-hook'
const BasicComponent = app.require('@elements/enhance/basic-component')
import { getFormItemsArrHSBC } from './basic-state-const'
import { getBankSortArr, accountChannelNameMap } from '../../lib/enums'
import { toast, hideKeyboard, getFormItemsCityCode } from '../../lib/util'
import { cloneDeep, compact } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'
import {
  OutlinedEditCopy,
  OutlinedTipsInfo,
  OutlinedDirectionRight,
  FilledGeneralGroup,
  FilledGeneralMember,
  OutlinedGeneralGroup,
  OutlinedGeneralMember
} from '@hose/eui-icons'
import FormItemAddPayeePicker from './components/FormItemAddPayeePicker'
import { getBoolVariation } from '../../lib/featbit'

const FORM_TYPE = {
  create: '1',
  edit: '2'
}

const PERSONAL = 'PERSONAL'

const titleMap = {
  PERSONAL: '您可以在对应银行APP的账户管理中查找银行网点，或咨询银行客户经理',
  PUBLIC: '您可以在对应的合同中查询此信息'
}

@EnhanceConnect(state => ({
  bankList: state['@common'].bankList,
  provinceList: state['@common'].provinceList,
  branchList: state['@common'].branchByCityIdList,
  cityList: state['@common'].cityList,
  certificateTypeList: state['@common'].CertificateTypeList,
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  ALIPAY: state['@common'].powers.ALIPAY,
  unionBankList: state['@common'].unionBankList,
  userInfo: state['@common'].me_info,
  payeeConfig: state['@common'].payeeConfig,
  payeeConfigCheck: state['@bill'].payeeConfigCheck,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT, // KA-外币账户
  searchCountries: state['@common'].searchCountries.items,
  searchCities: state['@common'].searchCities.items,
  globalFields: state['@common'].baseDataProperties.data
}))
@EnhanceTitleHook(props => props.title)
export default class AddPayee extends BasicComponent {
  constructor(props) {
    super(props)
    this.currentBank = undefined
    this.currentCity = undefined
    this.bankLinkNo = undefined
    this.branchId = undefined
    this.formRef = React.createRef()
    const { CHANGJIEPay, payeeConfig, payeeConfigCheck = {}, data } = props
    const type = data?.type ? data.type : payeeConfigCheck.personalAccount ? 'PERSONAL' : 'PUBLIC'
    const accountConfig =
      type === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig', {})
        : getV(payeeConfig, 'publicAccountConfig', {})
    const accountSort = getV(accountConfig, 'accountSort')
    this.bankSortArr = accountSort
      ? getBankSortArr().filter(item => accountSort.includes(item.value))
      : getBankSortArr()
    if (window.IS_SMG) {
      this.bankSortArr = this.bankSortArr && this.bankSortArr.filter(line => line.value !== 'ALIPAY')
    }
    const allowConcisionType = accountConfig.conciseInput && CHANGJIEPay
    const segmentActiveKey = data?.sort
      ? [data?.sort]
      : [(this.bankSortArr.length && this.bankSortArr[0].value) || 'BANK']
    const isForbiddenAdd = type === 'PERSONAL' && accountConfig.forbiddenAdd
    const isRequired = accountConfig?.networkNotRequired || false // 网点是否必填
    this.bankLinkNo = data?.bankLinkNo
    this.branchId = data?.branchId?.id
    let formData = {}
    if (data) {
      formData = {
        accountName: data.accountName,
        accountNo: data.accountNo,
        branch: data?.branchId?.name || data.branch,
        certificateNo: data.certificateNo,
        certificateType: [data.certificateType],
        remark: data.remark,
        nationCode: [data.nationCode],
        sort: [data.sort],
        ...data.customFields,
        extensions: data.extensions
      }
    }
    let extensionsMap = {}
    if (data?.extensions) {
      Object.keys(data.extensions).forEach(key => {
        extensionsMap[`extensions.${key}`] = data.extensions[key]
      })
    }
    this.formValue = data ? { ...data, ...formData } : { nationCode: ['090'] }
    this.state = {
      type: data ? FORM_TYPE.edit : FORM_TYPE.create,
      formData: { type, owner: 'INDIVIDUAL', ...formData },
      bankData: {},
      branchData: data ? data?.branchId?.name || data.branch : '',
      branchFlag: false,
      areaList: [],
      areaValue: [],
      cols: 1,
      // 添加收款信息模式
      isRegularType: true,
      segmentActiveKey,
      allowConcisionType,
      allowCreate: payeeConfig.allowCreate,
      isForbiddenAdd,
      isRequired,
      extraTextFields: [],
      showIntelligentFilling: false,
      intelligentFillingValue: '',
      ...extensionsMap
    }
    props.overrideGetResult(this.getResult)
  }

  componentWillMount() {
    this._$UUID = String(Math.random())
    api.backControl.create(this._$UUID, () => {
      this.backHookCallback()
    })
  }

  backHookCallback = () => {
    if (this.props.disabled) {
      api.backControl.remove(this._$UUID)
      api.backControl.invoke()
    } else {
      Dialog.confirm({
        content: i18n.get('确定退出此次编辑吗？'),
        closeOnAction: true,
        confirmText: i18n.get('退出'),
        onConfirm: () => {
          api.backControl.remove(this._$UUID)
          api.backControl.invoke()
        }
      })
    }
  }

  componentWillUnmount() {
    api.backControl.remove(this._$UUID)
  }

  componentDidMount() {
    const { KA_FOREIGN_ACCOUNT } = this.props
    api.invokeService(`@common:get:CertificateTypeList`)
    api.invokeService('@layout:set:header:title', this.props.title)
    api.invokeService('@common:get:banks', { start: 0, count: 50 })
    api.invokeService('@common:get:bank:provinces')
    api.invokeService('@common:get:unionBanks')
    api.invokeService('@bill:get:payee:config:check')
    if (KA_FOREIGN_ACCOUNT) {
      api.dataLoader('@common.searchCountries').load()
    }
    this.initPayeeExtraTextFields(this.state.formData.type)
  }

  componentWillReceiveProps(nextProps) {
    const { payeeConfigCheck = {} } = this.props
    const diff =
      nextProps.payeeConfigCheck?.personalAccount != payeeConfigCheck.personalAccount ||
      nextProps.payeeConfigCheck?.publicAccount != payeeConfigCheck.publicAccount
    if (diff) {
      const isPersonalHaveCreate = nextProps.payeeConfigCheck?.personalAccount
      const type = isPersonalHaveCreate ? 'PERSONAL' : 'PUBLIC'
      this.handleTypeChange(type)
    }
  }

  initPayeeExtraTextFields(type = PERSONAL) {
    const { payeeConfig = {}, globalFields } = this.props
    const accountConfig = type == PERSONAL ? payeeConfig.personalAccountConfig : payeeConfig.publicAccountConfig
    const customFields = accountConfig.customFields ?? []

    if (customFields) {
      // 额外字符串赋值
      this.setState({
        extraTextFields: compact(
          customFields.map(item => {
            return globalFields.find(v => v.name == item)
          })
        )
      })
    }
  }

  getResult = async () => {
    const { KA_FOREIGN_ACCOUNT, data: editTargetData } = this.props
    const form = this.formRef.current
    const { formData, segmentActiveKey, extraTextFields } = this.state
    let data = { ...formData, ...form.getFieldsValue() }
    const customFields = {}
    if (
      (segmentActiveKey.includes('OVERSEABANK') ||
        segmentActiveKey.includes('BANK') ||
        segmentActiveKey.includes('ACCEPTANCEBILL')) &&
      KA_FOREIGN_ACCOUNT
    ) {
      const extensions = {
        bankCountry: this.state['extensions.bankCountry'] || null,
        bankCountryStr: this.state['extensions.bankCountryStr'] || null,
        bankCountry_shortCode: this.state['extensions.bankCountry_shortCode'] || null,
        city: this.state['extensions.city'] || null,
        cityEn: this.state['extensions.cityEn'] || null,
        cityEnStr: this.state['extensions.cityEnStr'] || null,
        cityStr: this.state['extensions.cityStr'] || null,
        country: this.state['extensions.country'] || null,
        countryEn: this.state['extensions.countryEn'] || null,
        countryEnStr: this.state['extensions.countryEnStr'] || null,
        countryStr: this.state['extensions.countryStr'] || null,
        numCode: this.state['extensions.numCode'] || null,
        shortCode: this.state['extensions.shortCode'] || null
      }
      data.extensions = { ...data.extensions, ...extensions }
    }
    data = this.fnFormatData(data)
    data.accountNo = data.accountNo && data.accountNo.replace(/\s/g, '')
    extraTextFields.forEach(item => {
      customFields[item.name] = data[item.name]
      delete data[item.name]
    })
    data['customFields'] = customFields
    if (!editTargetData) {
      return api.invokeService('@common:add:payee', data)
    } else {
      data.id = editTargetData.id
      return api.invokeService('@common:edit:payee', data)
    }
  }

  fnFormatData(data) {
    const { payeeConfig, userInfo = {} } = this.props
    const { formData } = this.state
    let bankData = cloneDeep(data)
    if (bankData.certificateType) {
      bankData.certificateType = bankData.certificateType[0]
    }
    if (this.bankLinkNo) {
      bankData.bankLinkNo = this.bankLinkNo
    }
    if (this.branchId) {
      bankData.branchId = this.branchId
    }
    if (bankData?.nationCode) {
      bankData.nationCode = bankData?.nationCode?.[0]
    }
    const accountConfig =
      formData.type === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig')
        : getV(payeeConfig, 'publicAccountConfig')
    return {
      asPayee: true,
      visibility: {
        roles: null,
        staffs: [`${userInfo?.staff?.id}`],
        departments: null,
        fullVisible: getV(accountConfig, 'createAccount.visible', false),
        departmentsIncludeChildren: false
      },
      ...bankData,
      sort: bankData.sort[0]
    }
  }

  handleSubmit = () => {
    this.formRef.current
      .validateFields()
      .then(() => {
        const result = this.getResult().catch(err => toast.fail(err?.errorMessage || i18n.get('保存失败')))
        this.props.layer.emitOk(result)
        Toast.show({
          icon: 'success',
          content: '保存成功'
        })
      })
      .catch(err => {
        if (err.errorFields?.[0]) {
          const fieldName = err.errorFields[0].name.join('_')

          const errorElement = document.getElementById(fieldName)
          if (errorElement) {
            errorElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            })
          }
        }
        throw err
      })
  }

  handleTypeChange = type => {
    const { payeeConfig, CHANGJIEPay } = this.props
    let { formData } = this.state
    formData = { ...formData, type }
    const accountConfig =
      type === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig', {})
        : getV(payeeConfig, 'publicAccountConfig', {})
    this.bankSortArr = accountConfig.accountSort
      ? getBankSortArr().filter(item => accountConfig.accountSort.includes(item.value))
      : getBankSortArr()
    const segmentActiveKey = [(this.bankSortArr.length && this.bankSortArr[0].value) || 'BANK']
    const allowConcisionType = accountConfig.conciseInput && CHANGJIEPay
    const isForbiddenAdd = type === 'PERSONAL' && accountConfig.forbiddenAdd
    const isRequired = accountConfig?.networkNotRequired || false // 网点是否必填
    this.setState({ formData, allowConcisionType, segmentActiveKey, isForbiddenAdd, isRequired })
    if (this.formRef.current) {
      const oldValues = this.formRef.current.getFieldsValue()
      this.formRef.current.resetFields()
      this.formRef.current.setFieldsValue({
        ...oldValues,
        certificateType: '',
        certificateNo: '',
        sort: segmentActiveKey,
        accountName: ''
      })
    }
    this.initPayeeExtraTextFields(type)
  }

  handleFormChange = (updateValue, allValue) => {
    const { formData } = this.state
    this.setState({ formData: { ...formData, ...updateValue } })
  }

  handleSelectBankBranch = () => {
    // 有空指针异常的风险
    hideKeyboard()
    api
      .open('@basic:SelectBankBranch', {
        data: {
          branchData: this.state.branchData,
          currentCity: this.currentCity
        }
      })
      .then(result => {
        let cBankData = this.state.bankData
        this.formRef?.current?.setFieldsValue({
          branch: result.name
        })
        if (result) {
          this.bankLinkNo = result?.code
          this.branchId = result?.id
          this.formRef?.current?.setFieldsValue({
            bank: result.bankName
          })
          cBankData = { ...cBankData, name: result.bankName, icon: result.bankIcon }
        }
        this.currentBank = cBankData
        this.setState({ bankData: cBankData, branchData: result.name, branchFlag: result.name === i18n.get('其他') })
      })
  }

  checkNumber(rule, value, callback) {
    const { segmentActiveKey } = this.state
    if (segmentActiveKey[0] === 'ALIPAY' || segmentActiveKey[0] === 'OTHER' || segmentActiveKey[0] === 'WEIXIN') {
      // if (value) {
      //   if (!/^(?:1[3-9]\d{9}|[a-zA-Z\d._-]*\@[a-zA-Z\d.-]{1,20}\.[a-zA-Z\d]{1,20})$/.test(value.replace(/\s/g, ''))) {
      //     return callback(i18n.get('支付宝账号格式不正确'))
      //   }
      // }
    } else {
      if (value) {
        if (!/^[0-9a-zA-Z-]*$/.test(value.replace(/\s/g, ''))) return callback(i18n.get('银行账号格式不正确'))
      }
    }
    callback()
  }

  checkcertificateNo = (_rule, value, callback) => {
    const cer = this.formRef?.current?.getFieldValue('certificateType')
    const certificateType = cer && cer[0]
    if (certificateType === '01' && value) {
      if (!value.length) return callback(i18n.get('身份证号不能为空'))
      if (value.length > 18) return callback(i18n.get('身份证号不能超过18位'))
      if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        return callback(i18n.get('身份证号格式不正确'))
      }
    }
    callback()
  }

  checkRoutingNumber = (rule, value, callback) => {
    if (!!value && !/^[0-9]+$/.test(value)) {
      return callback(i18n.get('汇款路线号码必须是数字'))
    }
    callback()
  }

  checkRemark = (_rule, value, callback) => {
    if (!!value && /\n/.test(value)) {
      const newValue = value.replace(/\n/g, '')
      this.formRef?.current?.setFieldsValue({ remark: newValue })
    }
    callback()
  }

  handleSelectNewBank = () => {
    hideKeyboard()
    api
      .open('@basic:SelectNewBank', {
        bankList: this.props.unionBankList,
        value: this.formRef?.current?.getFieldValue('unionBank')
      })
      .then(res => {
        this.formRef?.current?.setFieldsValue({ unionBank: res.name })
        this.setState({ canClear: true })
      })
  }

  handleClearUnionBank = e => {
    if (e.cancelable) {
      e.preventDefault && e.preventDefault()
    }
    e.stopPropagation && e.stopPropagation()
    e.stopImmediatePropagation && e.stopImmediatePropagation()
    this.formRef?.current?.setFieldsValue({ unionBank: '' })
    this.setState({ canClear: false })
  }

  renderUnionBanks = () => {
    return (
      <Form.Item
        name="unionBank"
        label={i18n.get('开户行')}
        rules={[{ required: true, message: i18n.get('开户行不能为空') }]}
      >
        <Input
          onClear={this.handleClearUnionBank}
          suffix={<OutlinedDirectionRight />}
          onClick={this.handleSelectNewBank}
          placeholder={i18n.get('请选择')}
        ></Input>
      </Form.Item>
    )
  }

  handleAccountChange = value => {
    this.formRef.current.setFieldsValue({ accountNo: '', sort: value })
    this.setState({ segmentActiveKey: value })
  }

  handleIconClick = () => {
    const { formData } = this.state
    const title = titleMap?.[formData?.type]
    return i18n.get(title)
  }

  renderBankBranch() {
    const { isRequired } = this.state
    return (
      <Form.Item
        name="branch"
        label={
          <div
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              Dialog.alert({
                content: this.handleIconClick()
              })
            }}
          >
            {i18n.get('银行网点')}
            <OutlinedTipsInfo fontSize={14} style={{ marginLeft: 2 }} />
          </div>
        }
        rules={[{ required: !isRequired, message: i18n.get('银行网点不能为空') }]}
      >
        <Input
          suffix={<OutlinedDirectionRight />}
          onClick={this.handleSelectBankBranch}
          value={this.state.branchData}
          placeholder={i18n.get('请选择')}
        ></Input>
      </Form.Item>
    )
  }

  renderHeader = () => {
    const { payeeConfigCheck = {} } = this.props
    const { formData } = this.state
    const isPersonalHaveCreate = payeeConfigCheck.personalAccount
    const isPublicHaveCreate = payeeConfigCheck.publicAccount
    const isDisabledPersonal = !isPersonalHaveCreate
    const isDisabledPublic = !isPublicHaveCreate
    const options = [
      {
        label: (
          <span
            style={
              formData.type === 'PERSONAL' ? { font: 'var(--eui-font-body-b1)' } : { font: 'var(--eui-font-body-r1)' }
            }
          >
            {formData.type === 'PERSONAL' ? (
              <FilledGeneralMember fontSize={16} style={{ marginRight: 4 }} />
            ) : (
              <OutlinedGeneralMember fontSize={16} style={{ marginRight: 4 }} />
            )}
            {i18n.get('个人账户')}
          </span>
        ),
        value: 'PERSONAL',
        disabled: isDisabledPersonal
      },
      {
        label: (
          <span
            style={
              formData.type === 'PUBLIC' ? { font: 'var(--eui-font-body-b1)' } : { font: 'var(--eui-font-body-r1)' }
            }
          >
            {formData.type === 'PUBLIC' ? (
              <FilledGeneralGroup fontSize={16} style={{ marginRight: 4 }} />
            ) : (
              <OutlinedGeneralGroup style={{ marginRight: 4 }} fontSize={16} />
            )}
            {i18n.get('对公账户')}
          </span>
        ),
        value: 'PUBLIC',
        disabled: isDisabledPublic
      }
    ]
    return (
      <div className="header-wrapper">
        <Selector
          style={{ width: '100%', '--border-radius': '8px', '--padding': '12px' }}
          columns={2}
          disabled={this.props.disabled}
          options={options}
          defaultValue={[formData.type]}
          onChange={value => {
            if (value?.length) {
              this.handleTypeChange(value[0])
            }
          }}
        />
      </div>
    )
  }
  handleCertificateTypeChange = value => {
    // 证件类型为空，将证件号重置
    if (value.toString() === '') {
      this.formRef.current.resetFields(['certificateNo'])
    }
    this.formRef.current.setFieldsValue({
      certificateType: value,
      certificateNo: undefined
    })
  }
  renderCertify = () => {
    const { certificateTypeList } = this.props
    const { formData } = this.state
    const isCertificateTypeRequired = getBoolVariation('customized_filling_of_collection_account', false)
    let list = certificateTypeList.map(v => ({
      value: v.code,
      label: i18n.get(v.name)
    }))
    if (formData.type === 'PUBLIC') {
      list = list.filter(item => item.value === '11' || item.value === '54')
      if (window.IS_SMG) {
        list = list && list.filter(item => item.value === '54')
      }
    }
    if (!isCertificateTypeRequired) {
      list = [{ value: '', label: i18n.get('无') }, ...list]
    }
    const isSmg = window.IS_SMG && formData.type === 'PUBLIC'
    return (
      <>
        <Form.Item
          name="certificateType"
          label={i18n.get('证件类型')}
          rules={[{ required: isSmg || isCertificateTypeRequired, message: i18n.get('请选择证件类型') }]}
        >
          <FormItemAddPayeePicker
            onChange={this.handleCertificateTypeChange}
            title={i18n.get('证件类型')}
            data={list}
          />
        </Form.Item>
        <Form.Item
          name="certificateNo"
          label={i18n.get('证件号码')}
          disabled={!formData.certificateType?.[0] && !isCertificateTypeRequired}
          rules={[
            { required: isSmg || isCertificateTypeRequired, message: i18n.get('请输入证件号码') },
            { validator: this.checkcertificateNo },
            { max: 100, message: i18n.get('账号不能超过100个字') }
          ]}
        >
          <Input placeholder={i18n.get('请输入证件号码')}></Input>
        </Form.Item>
      </>
    )
  }

  fnHandleAlipayWarnningClick = text => {
    const { segmentActiveKey } = this.state
    if (text && segmentActiveKey[0] === 'ALIPAY') {
      toast.info(text)
    }
  }

  fnGetAlipayAccountNoWarnningText = value => {
    const { segmentActiveKey } = this.state
    if (
      value &&
      segmentActiveKey[0] === 'ALIPAY' &&
      !/^(?:1[3-9]\d{9}|[a-zA-Z\d._-]*\@[a-zA-Z\d.-]{1,20}\.[a-zA-Z\d]{1,20})$/.test(value.replace(/\s/g, ''))
    ) {
      return i18n.get('支付宝账户可能存在错误')
    }
    return ''
  }

  handleValidator = (rule, value, callback) => {
    const { userInfo } = this.props
    if (!value) {
      callback()
    }
    if (value != userInfo.staff.name) {
      callback(i18n.get('开户名与当前用户姓名不一致！'))
    }
    callback()
  }

  checkEn = (_rule, value, callback) => {
    if (value) {
      if (value.match(/[\u4e00-\u9fa5]/)) return callback(i18n.get('Please enter in English'))
    }
    callback()
  }

  checkEmail = (_rule, value, callback) => {
    let reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    if (value && !reg.test(value)) {
      return callback(i18n.get('请输入正确格式的邮箱地址'))
    }
    callback()
  }

  inputValidator = (el, rule, value, callback) => {
    if (el?.onlyEn) {
      this.checkEn(rule, value, callback)
    }
    if (el?.checkEmail) {
      this.checkEmail(rule, value, callback)
    }
    callback()
  }

  handleSelectCountryorCityBranch = (name, placeholder, label) => {
    // 有空指针异常的风险
    hideKeyboard()
    const { searchCountries, searchCities } = this.props
    const fieldNameMap = {
      'extensions.country': 'fullCname',
      'extensions.city': 'cnName',
      'extensions.countryEn': 'fullEname',
      'extensions.cityEn': 'name',
      'extensions.bankCountry': 'fullCname'
    }
    const fieldName = fieldNameMap[name]
    let data = {
      fieldName,
      placeholder,
      label
    }
    if (name.indexOf('city') >= 0) {
      data.searchList = searchCities
    } else {
      data.searchList = searchCountries
    }
    api
      .open('@basic:SelectCountryorCityBranch', {
        data,
        modalTitle: label,
        value: this.state[name]
      })
      .then(result => {
        if (result) {
          const obj = {}
          if (name !== 'extensions.bankCountry') {
            const formItemMap = {
              'extensions.country': 'extensions.countryEn',
              'extensions.city': 'extensions.cityEn',
              'extensions.countryEn': 'extensions.country',
              'extensions.cityEn': 'extensions.city'
            }
            const labelname = formItemMap[name]
            //国家、城市 中英文联动
            if (labelname.includes('country')) {
              obj[labelname] = searchCountries.filter(item => item.id === result.id)[0]?.id
              obj['extensions.countryStr'] = searchCountries.filter(item => item.id === result.id)[0]?.fullCname
              obj['extensions.countryEnStr'] = searchCountries.filter(item => item.id === result.id)[0]?.fullEname
              obj['extensions.shortCode'] = searchCountries.filter(item => item.id === result.id)[0]?.shortCode
              obj['extensions.numCode'] = searchCountries.filter(item => item.id === result.id)[0]?.numCode
            } else {
              const selectedCity = searchCities.filter(item => item.id === result.id)
              obj[labelname] = selectedCity[0]?.id
              if (selectedCity[0]) {
                obj['extensions.cityStr'] = selectedCity[0]?.cnState
                  ? `${selectedCity[0].cnState}-${selectedCity[0].cnName}`
                  : selectedCity[0].cnName
                obj['extensions.cityEnStr'] = selectedCity[0]?.state
                  ? `${selectedCity[0].state}-${selectedCity[0].name}`
                  : selectedCity[0].name
              }
            }
            obj[name] = result.id
            this.formRef?.current?.setFieldsValue(obj)
            this.setState({
              ...obj
            })
            // 切换国家 清空城市
            if (name.includes('country')) {
              api.invokeService('@common:get:CityList', result.id)
              this.formRef?.current?.resetFields(['extensions.city', 'extensions.cityEn'])
              this.setState({
                'extensions.city': undefined,
                'extensions.cityEn': undefined
              })
            }
          } else {
            obj[name] = result.id
            obj['extensions.bankCountryStr'] = searchCountries.filter(item => item.id === result.id)[0]?.fullCname
            obj['extensions.bankCountry_shortCode'] = searchCountries.filter(
              item => item.id === result.id
            )[0]?.shortCode
            this.formRef?.current?.setFieldsValue(obj)
            this.setState({
              ...obj
            })
          }
        }
      })
  }

  handleAccountNoBlur = async () => {
    const { setFieldsValue, getFieldsValue } = this.formRef.current
    const values = getFieldsValue()
    const { accountNo, sort } = values || {}
    const { formData } = this.state
    if (sort?.[0] === 'BANK' && formData?.type === 'PERSONAL' && accountNo?.length > 2) {
      const res = await Fetch.GET('/api/pay/v1/banks/branchByCardNo', { cardNo: accountNo })
      if (res?.value?.id) {
        let cBankData = this.state.bankData
        const data = res?.value || {}
        setFieldsValue({ branch: data?.name })
        this.bankLinkNo = data?.code
        this.branchId = data?.id
        cBankData = { ...cBankData, name: data?.bankName, icon: data?.bankIcon }
        this.currentBank = cBankData
        this.setState({ bankData: cBankData, branchData: data?.name, branchFlag: data?.name === i18n.get('其他') })
      }
    }
  }

  handleTextAreaChange = intelligentFillingValue => {
    this.setState({ intelligentFillingValue })
  }

  handleIntelligentFillingClick = showIntelligentFilling => {
    this.setState({ showIntelligentFilling })
  }

  handleShowTipClick = () => {
    Dialog.alert({
      content: (
        <div className={styles['dialog-wrap']}>
          <span>{i18n.get('户名：合小思')}</span>
          <span>{i18n.get('卡号：****************')}</span>
          <span>{i18n.get('开户行：招商银行双榆树支行')}</span>
        </div>
      )
    })
  }

  handleRecognitionClcik = () => {
    const { intelligentFillingValue } = this.state
    const { setFieldsValue } = this.formRef.current
    const _value = intelligentFillingValue.trim()
    const data = JSON.stringify(_value)
    const text = data.replace(/^"|"$/g, '')
    return Fetch.POST('/api/pay/v2/accounts/parsing', {}, { body: { text } })
      .then(res => {
        const { accountName, accountNo, branches } = res?.value || {}
        const data = branches?.[0] || {}
        setFieldsValue({ accountName, accountNo, branch: data?.name })
        toast.info('已填入可识别信息，请确认')
        if (data?.id) {
          let cBankData = this.state.bankData
          this.bankLinkNo = data?.code
          this.branchId = data?.id
          cBankData = { ...cBankData, name: data?.bankName, icon: data?.bankIcon }
          this.currentBank = cBankData
          this.setState({ bankData: cBankData, branchData: data?.name, branchFlag: data?.name === i18n.get('其他') })
        }
      })
      .catch(err => {
        toast.error(err?.errorMessage)
      })
  }

  renderOverseaFieldForHSBC = segmentActiveKey => {
    const { KA_FOREIGN_ACCOUNT } = this.props
    const formItemsArrHSBC = segmentActiveKey.includes('OVERSEABANK')
      ? getFormItemsArrHSBC()
      : getFormItemsArrHSBC().filter(item => item.name !== 'extensions.bankCountry')
    if (
      (segmentActiveKey.includes('OVERSEABANK') ||
        segmentActiveKey.includes('BANK') ||
        segmentActiveKey.includes('ACCEPTANCEBILL')) &&
      KA_FOREIGN_ACCOUNT
    ) {
      return (
        <>
          {formItemsArrHSBC.map(el => {
            const label = el.payee ? `${el.payee}-${el.label}` : el.label
            const __k0 = el.payee ? `${i18n.get(el.payee)}-${i18n.get(el.label)}` : i18n.get(el.label)
            const placeholder = el.notChineseOREnglish
              ? i18n.get('请输入{__k0}', { __k0: __k0 })
              : el.onlyEn
              ? i18n.get(`请输入{__k0}_English`, { __k0: __k0 })
              : i18n.get(`请输入{__k0}_Chinese`, { __k0: __k0 })
            if (el.type === 'select') {
              let selectNameMap = {
                'extensions.country': 'extensions.countryStr',
                'extensions.city': 'extensions.cityStr',
                'extensions.countryEn': 'extensions.countryEnStr',
                'extensions.cityEn': 'extensions.cityEnStr',
                'extensions.bankCountry': 'extensions.bankCountryStr'
              }
              return (
                <>
                  <Form.Item
                    key={el.name}
                    name={el.name.split('.')}
                    label={label}
                    rules={[
                      {
                        max: el.max,
                        message: i18n.get(el.label) + i18n.get('不能超过{__k0}个文字', { __k0: el.max })
                      },
                      { validator: this.inputValidator.bind(this, el) }
                    ]}
                  >
                    <div>
                      <Input
                        value={this.state[selectNameMap[el.name]]}
                        suffix={<OutlinedDirectionRight />}
                        onClick={() => this.handleSelectCountryorCityBranch(el.name, placeholder, el.label)}
                        placeholder={placeholder}
                      ></Input>
                    </div>
                  </Form.Item>
                </>
              )
            } else {
              return (
                <Form.Item
                  key={el.name}
                  name={el.name.split('.')}
                  label={label}
                  rules={[
                    {
                      max: el.max,
                      message: i18n.get(el.label) + i18n.get('不能超过{__k0}个文字', { __k0: el.max })
                    },
                    { validator: this.inputValidator.bind(this, el) }
                  ]}
                >
                  <Input placeholder={placeholder}></Input>
                </Form.Item>
              )
            }
          })}
        </>
      )
    }
    return null
  }

  renderIntelligentFillingContent = () => {
    const { showIntelligentFilling, intelligentFillingValue } = this.state
    return showIntelligentFilling ? (
      <div className="intelligent-filling-content">
        <div className="text-area-content">
          <TextArea
            className="text-area"
            placeholder={i18n.get('在银行APP账户管理中查询卡片信息，复制粘贴，自动识别开户名/银行卡号/银行网点')}
            value={intelligentFillingValue}
            onChange={this.handleTextAreaChange}
            maxLength={100}
            border
          />
          <div className="btns">
            <Button
              shape="rounded"
              category="secondary"
              style={{ marginRight: 8 }}
              onClick={() => this.handleIntelligentFillingClick(false)}
            >
              {i18n.get('取消')}
            </Button>
            <Button
              onClick={this.handleRecognitionClcik}
              disabled={!intelligentFillingValue}
              shape="rounded"
              category="secondary"
              theme="highlight"
            >
              {i18n.get('识别')}
            </Button>
          </div>
        </div>
        <div className="tips">
          {i18n.get('标准格式更快识别')}
          <span className="blue" onClick={this.handleShowTipClick}>
            {i18n.get('查看示例')}
          </span>
        </div>
      </div>
    ) : (
      <Button
        block
        disabled={this.props.disabled}
        size="small"
        category="secondary"
        className="intelligent-filling-btn"
        icon={<OutlinedEditCopy />}
        onClick={() => this.handleIntelligentFillingClick(true)}
      >
        {i18n.get('智能填写')}
      </Button>
    )
  }

  renderList = showButton => {
    const { disabled } = this.props
    const { segmentActiveKey, allowConcisionType, extraTextFields } = this.state
    const isOther = segmentActiveKey[0] === 'OTHER' || segmentActiveKey[0] === 'WEIXIN'
    const accountChannelNameMapEnum = accountChannelNameMap()
    const isNewTips = getBoolVariation('account_name_tips_new') && segmentActiveKey[0] === 'OVERSEABANK'
    const accountNoPlaceHolder =
      segmentActiveKey[0] === 'ALIPAY'
        ? i18n.get('请输入与开户名相匹配的支付宝账号')
        : i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[segmentActiveKey[0]] })
    return (
      <div className="list-wrapper">
        {this.renderHeader()}
        <Form
          disabled={disabled}
          scrollToFirstError
          ref={this.formRef}
          onValuesChange={this.handleFormChange}
          initialValues={this.formValue}
        >
          <Form.Item
            name="sort"
            label={i18n.get('账户类别')}
            initialValue={segmentActiveKey}
            rules={[{ required: true }]}
          >
            <FormItemAddPayeePicker
              onChange={this.handleAccountChange}
              title={i18n.get('账户类别')}
              data={this.bankSortArr}
            />
          </Form.Item>
          {segmentActiveKey[0] === 'BANK' && this.renderIntelligentFillingContent()}
          <Form.Item
            name="accountName"
            label={
              <div>
                {i18n.get('开户名')}
                <OutlinedTipsInfo
                  fontSize={14}
                  style={{ marginLeft: 2 }}
                  onClick={() => {
                    Dialog.alert({
                      content: isNewTips
                        ? 'Please enter in english'
                        : i18n.get('您在银行开立账户时输入的姓名、公司名或组织机构名称')
                    })
                  }}
                />
              </div>
            }
            rules={[
              { required: true, message: i18n.get('请输入真实姓名、公司名或组织机构名称') },
              { max: 100, message: i18n.get('户名不能超过100个字') },
              { validator: this.state.isForbiddenAdd ? this.handleValidator.bind(this) : null }
            ]}
          >
            <Input placeholder={i18n.get('请输入真实姓名、公司名或组织机构名称')}></Input>
          </Form.Item>
          <Form.Item
            name="accountNo"
            label={accountChannelNameMapEnum[segmentActiveKey[0]]}
            rules={[
              {
                required: !isOther,
                message: i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[segmentActiveKey[0]] })
              },
              { max: 100, message: i18n.get('账号不能超过100个字') },
              { validator: this.checkNumber.bind(this) }
            ]}
          >
            <Input
              onBlur={this.handleAccountNoBlur}
              onChange={value => {
                this.formRef.current?.setFieldsValue({
                  accountNo: value
                })
              }}
              suffix={
                this.fnGetAlipayAccountNoWarnningText(this.formRef?.current?.getFieldValue('accountNo')) ? (
                  <OutlinedTipsInfo
                    color="var(--eui-function-warning-500"
                    onClick={() => {
                      Dialog.alert({
                        content: this.fnGetAlipayAccountNoWarnningText(
                          this.formRef?.current?.getFieldValue('accountNo')
                        )
                      })
                    }}
                  />
                ) : (
                  undefined
                )
              }
              placeholder={accountNoPlaceHolder}
            ></Input>
          </Form.Item>
          {this.renderSortItems(allowConcisionType)}
          {this.renderCertify()}
          {this.renderOverseaFieldForHSBC(segmentActiveKey)}
          <Form.Item
            name="remark"
            label={
              <div>
                {i18n.get('备注信息')}
                <OutlinedTipsInfo
                  fontSize={14}
                  style={{ marginLeft: 2 }}
                  onClick={() => {
                    Dialog.alert({
                      content: i18n.get('如您的账户做特殊用途，区别其他账户，可添加账户备注')
                    })
                  }}
                />
              </div>
            }
            rules={[
              {
                required: false,
                message: i18n.get('请输入{__k0}', { __k0: i18n.get('备注信息') })
              },
              { validator: this.checkRemark },
              { max: 140, message: i18n.get('最多输入{__k0}个字符', { __k0: 140 }) }
            ]}
          >
            <TextArea showCount maxLength={140} placeholder={i18n.get('请输入')}></TextArea>
          </Form.Item>
          {extraTextFields.map(field => (
            <Form.Item
              key={field.name}
              name={field.name}
              label={i18n.get(field.label)}
              rules={[
                { required: false, message: i18n.get('请输入{__k0}', { __k0: i18n.get(field.name) }) },
                { max: 50, message: i18n.get(`${field.label}不能超过50个字`) }
              ]}
            >
              <Input placeholder={i18n.get('请输入')}></Input>
            </Form.Item>
          ))}
        </Form>
        {showButton && <div className="bottom-line" />}
      </div>
    )
  }

  renderOverseaBank = () => {
    const { KA_FOREIGN_ACCOUNT } = this.props
    const cityCodeList = getFormItemsCityCode()
    const isNewTips = getBoolVariation('account_name_tips_new')
    return (
      <>
        <Form.Item
          name="bankName"
          label={
            isNewTips ? (
              <div>
                {i18n.get('银行名称(Bank Name)')}
                <OutlinedTipsInfo
                  fontSize={14}
                  style={{ marginLeft: 2 }}
                  onClick={() => {
                    Dialog.alert({
                      content: 'Please enter in english'
                    })
                  }}
                />
              </div>
            ) : (
              i18n.get('银行名称(Bank Name)')
            )
          }
          rules={[
            { max: 140, message: i18n.get('最多输入{__k0}个字符', { __k0: 140 }) },
            {
              required: KA_FOREIGN_ACCOUNT,
              whitespace: true,
              message: i18n.get('请输入银行名称(Bank Name)')
            }
          ]}
        >
          <Input placeholder={i18n.get('请输入')}></Input>
        </Form.Item>
        <Form.Item
          name="swiftCode"
          label={i18n.get('银行国际代码(Swift Code)')}
          rules={[
            { required: true, whitespace: true, message: i18n.get('请输入银行国际代码(Swift Code)') },
            {
              validator: (_, value, callback) => {
                if (!value || value.length === 8 || value.length === 11) {
                  return callback()
                }
                return callback('只能输入8位或11位')
              }
            }
          ]}
        >
          <Input placeholder={i18n.get('请输入')}></Input>
        </Form.Item>
        <Form.Item
          name="nationCode"
          label={i18n.get('银行所在地区代码(Nation Code)')}
          rules={[{ required: true, message: i18n.get('银行所在地区代码(Nation Code)') }]}
        >
          <FormItemAddPayeePicker title={i18n.get('银行所在地区代码')} data={cityCodeList} />
        </Form.Item>
        <Form.Item name="receiverAddress" label={i18n.get('收款人地址(Receiver Address)')}>
          <Input maxLength={35} placeholder={i18n.get('请输入收款人地址，不超过35个汉字')}></Input>
        </Form.Item>
        <Form.Item
          name="routingNumber"
          label={i18n.get('汇款路线号码(Routing No.)')}
          rules={[{ max: 9, message: i18n.get('汇款路线号码不能超过9个字符') }, { validator: this.checkRoutingNumber }]}
        >
          <Input placeholder={i18n.get('请输入汇款路线号码')}></Input>
        </Form.Item>
        <Form.Item
          name="bankCode"
          label={i18n.get('联行号')}
          rules={[{ max: 140, message: i18n.get('最多输入{__k0}个字符', { __k0: 140 }) }]}
        >
          <Input placeholder={i18n.get('请输入联行号')}></Input>
        </Form.Item>
        <Form.Item
          name="branchCode"
          label={i18n.get('支行号')}
          rules={[{ max: 140, message: i18n.get('最多输入{__k0}个字符', { __k0: 140 }) }]}
        >
          <Input placeholder={i18n.get('请输入支行号')}></Input>
        </Form.Item>
      </>
    )
  }

  renderSortItems = allowConcisionType => {
    const { segmentActiveKey } = this.state
    switch (segmentActiveKey[0]) {
      case 'BANK':
      case 'ACCEPTANCEBILL':
        if (allowConcisionType) {
          return this.renderUnionBanks()
        } else {
          return this.renderBankBranch()
        }
      case 'ALIPAY':
        return null
      case 'OVERSEABANK':
        return this.renderOverseaBank()
      default:
        return null
    }
  }

  render() {
    const { payeeConfigCheck = {}, disabled } = this.props
    const isPersonalHaveCreate = payeeConfigCheck.personalAccount
    const isPublicHaveCreate = payeeConfigCheck.publicAccount
    const showButton = (isPersonalHaveCreate || isPublicHaveCreate) && !disabled
    return (
      <div className={styles['add-payee-view-wrapper']}>
        {this.renderList(showButton)}
        {showButton && (
          <div className="action-wrapper">
            <Button block category="primary" size="large" onClick={this.handleSubmit}>
              {i18n.get('保存')}
            </Button>
          </div>
        )}
      </div>
    )
  }
}
