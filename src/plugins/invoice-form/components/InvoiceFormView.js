import { app, app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from './InvoiceFormView.module.less'
import classNames from 'classnames'
import createDOMForm from 'rc-form/lib/createDOMForm'
const FakeInput = app.require('@elements/puppet/FakeInput')
import { Button } from '@hose/eui-mobile'
import { Dynamic } from '@ekuaibao/template'
import MessageCenter from '@ekuaibao/messagecenter'
import editableViews from '../invoice-dynamic/index.editable'
import { show } from './popup'
import { invoiceOptions, invoiceFormTemplate, filterByType } from '../utils/config'
import InvoiceMenuView from './popup/InvoiceMenuView'
import { get, isObject } from 'lodash'
import { OutlinedDirectionDown } from '@hose/eui-icons'
import InvoiceMappingValue from '../../../lib/InvoiceMappingValue'

function create(T) {
  return createDOMForm()(T)
}

export default class InvoiceFormView extends PureComponent {
  bus = new MessageCenter()

  constructor(props) {
    super(props)
    this.templates = invoiceFormTemplate()
    let {
      field: { invoiceType, importMode },
      isEdit,
      value,
      isOwnerBill
    } = props
    let type = !value ? this.initData(props).type : value.type
    if (type === 'exist' && value?.invoiceConfirm === 'false') {
      type = 'wait'
    }
    let temp = this.templates[type]
    this.state = {
      importMode,
      template: (temp && temp(invoiceType, isEdit, isOwnerBill)) || []
    }
  }

  componentDidMount() {
    const {
      field: { invoiceType, defaultValue, editable },
      onChange,
      value,
      currentFlowNode
    } = this.props
    const { importMode } = this.state
    if (importMode?.setVisible) {
      api.invokeService('@invoice-form:get:feetype:invoice:importMode', importMode).then(res => {
        this.setState({ importMode: res.items })
      })
    }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    if (!value || (!!value && value.type === 'noWrite')) {
      const _value = value || {}
      const fnvalue = editable
        ? { ..._value, type: invoiceType?.defaultInvoiceType || fnInvoiceType[0].type }
        : { ..._value, type: defaultValue.value }
      onChange && onChange(fnvalue)
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value) {
      let {
        field: { invoiceType },
        isEdit,
        value,
        onResult
      } = nextProps
      if (!nextProps.value) {
        let { type } = this.initData(nextProps)
        let temp = this.templates[type]
        this.setState({
          template: (temp && temp(invoiceType, isEdit)) || []
        })
      } else {
        let type = value.type
        if (type === 'exist' && value?.invoiceConfirm === 'false') {
          type = 'wait'
        }
        let temp = this.templates[type]
        this.setState(
          {
            template: temp && temp(invoiceType, isEdit)
          },
          () => {
            onResult && onResult(nextProps.value)
          }
        )
      }
    }
  }

  initData(props) {
    let {
      value,
      field: { invoiceType },
      onResult,
      currentFlowNode
    } = props
    const fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode) || []
    let { type, label } = fnInvoiceType.find(el => el.type === invoiceType?.defaultInvoiceType) || fnInvoiceType[0]
    let initType = value && value.type ? value.type : type
    !value && onResult && onResult({ type, name: label })
    return { type: initType, label }
  }

  handleSelectValue = async line => {
    const {
      field: { invoiceType },
      isEdit,
      bus,
      calFields,
      template: components,
      value
    } = this.props
    // 如果不是已有发票并且之前上传过发票，清空之前的联动赋值数据
    if (value?.invoices?.length > 0 && line.type !== 'exit') {
      this.invoiceMappingValue = new InvoiceMappingValue()
      const summaryFields = components.filter(line => get(line, 'defaultValue.type') === 'invoiceSum')
      const invoiceSumField = {}
      summaryFields.forEach(({ field }) => {
        invoiceSumField[field] = undefined
      })
      const formValue = await this.invoiceMappingValue.invoice2FeeTypeForm([], components, calFields)
      const v = { ...invoiceSumField, ...formValue }
      bus.setFieldsValue(v)
    }

    const temp = this.templates[line.type]
    const template = temp && temp(invoiceType, isEdit)
    this.setState({ template })

    const { onResult } = this.props
    onResult && onResult(line)
  }

  handleClick = () => {
    let {
      field: { invoiceType },
      currentFlowNode
    } = this.props
    let data = invoiceOptions(invoiceType, currentFlowNode)
    let params = { data, selectValue: this.handleSelectValue, Component: InvoiceMenuView, ...this.props }
    show(params)
  }

  parseAsShowValue() {
    let {
      value,
      field: { invoiceType },
      currentFlowNode
    } = this.props
    if (JSON.stringify(value) === '{}') {
      const fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode) || []
      let { type, label } = fnInvoiceType.find(el => el.type === invoiceType?.defaultInvoiceType) || fnInvoiceType[0]
      this.handleSelectValue({ type, label })
      return { parentValue: { id: type, name: label } }
    }
    if (!value) {
      const fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode) || []
      let { type, label } = fnInvoiceType.find(el => el.type === invoiceType?.defaultInvoiceType) || fnInvoiceType[0]
      return { parentValue: { id: type, name: label } }
    }
    let name = value.name ? value.name : filterByType(value.type, invoiceType)
    let { attachments, invoiceCorporationId } = value
    let unifyData = isObject(invoiceCorporationId) ? { ...invoiceCorporationId } : undefined
    attachments = attachments ? attachments.map(line => this.parseAsAttactmentsShowValue(line)) : []
    const addInvocieData = attachments.concat(value.invoices || [])

    let childrenValue = { unifySelect: unifyData, addInvoice: addInvocieData }
    return { parentValue: { id: value.type, name }, childrenValue }
  }

  parseAsAttactmentsShowValue(value) {
    if (!value) return
    let { name, status, response, fileName, key, fileId } = value
    name = name || fileName
    status = status || 'done'
    response = response || {
      key,
      hash: String(Math.random()),
      ['x:originalname']: name,
      fileId
    }
    return { name, status, response, type: 'invoicePhoto' }
  }

  handlePreviewBatchInvoice = () => {
    const { value } = this.props
    api.open('@invoice:BatchInvoiceDetail', { batchId: value?.supplementInvoiceBatchId })
  }

  renderSupplementInvoiceBatch = ({ label, placeholder, showValue }) => {
    return (
      <div className={styles['invoice-form-view']}>
        <div className="add-action">
          <Button size="small" category="secondary" theme="highlight" inline onClick={this.handlePreviewBatchInvoice}>
            {i18n.get('查看批量补充发票')}
          </Button>
        </div>
      </div>
    )
  }

  renderInvoiceInput = ({ showValue, editable, isEdit, isFrom, placeholder, label }) => {
    if (isFrom === 'invoiceFormDynamic') {
      const onClick = e => {
        e.stopPropagation()
        editable && this.handleClick()
      }

      const disabled = isEdit ? !editable : !isEdit

      return (
        <div className="invoice-input-wrapper">
          <Button
            className="invoice-input-button"
            size="mini"
            category="text"
            theme="highlight"
            onClick={isEdit ? onClick : undefined}
            icon={
              <OutlinedDirectionDown
                fontSize={12}
                color={disabled ? 'var(--eui-text-disabled)' : 'var(--eui-primary-pri-500)'}
              />
            }
            iconPosition="end"
            disabled={disabled}
            data-testid="invoice-input-button"
          >
            {showValue && showValue?.parentValue?.name}
          </Button>
        </div>
      )
    }

    return (
      <FakeInput
        value={showValue && showValue.parentValue}
        label={label}
        editable={isEdit && isFrom !== 'importDetails' ? editable : isEdit}
        hiddenCursor={true}
        placeholder={placeholder}
        onClick={isEdit && isFrom !== 'importDetails' ? this.handleClick : undefined}
        isFrom={isFrom}
      />
    )
  }

  render() {
    let {
      value = {},
      field,
      editable,
      bus,
      onResult,
      isEdit = true,
      isFrom,
      isHideWait,
      submitter,
      invoiceImages,
      closeAndReturnValue,
      onDelete,
      isReadOnlyPage,
      isEditTaxRate = true,
      template: components,
      isInvoiceManagePermissions,
      onTaxAmountChange,
      onRetryCheckerInvoiceClick,
      isEditAuthenticity,
      calFields,
      onApporeIssueChange,
      isTicketReview,
      isModifyBill,
      onSortInvoice,
      invoiceRiskData,
      isSort,
      sortType,
      feetypeId,
      showPriceAndRate,
      disableInfo,
      markInfo,
      isExistConfirmInvoice,
      onInvoiceStatusChange,
      sourcePage,
      isQuickExpends,
      notShowModalIfAllInvoiceSuccess = false,
      flowId,
      detailId,
      isDetail,
      billSpecification,
      timeField
    } = this.props

    let { template, importMode } = this.state
    let { label, placeholder } = field
    let showValue = this.parseAsShowValue(value)
    if (value?.supplementInvoiceBatchId) {
      return this.renderSupplementInvoiceBatch({ label, placeholder, showValue })
    }
    const didi =
      !editable &&
      value &&
      value.type === 'unify' &&
      value.invoiceCorporationId &&
      value.invoiceCorporationId.channel === 'DIDI'
    const addInvoiceList = get(showValue, 'childrenValue.addInvoice', [])

    return (
      <div className={classNames(styles['invoice-form-view'], { 'invoice-form-view-disabled': !isEdit })}>
        {isReadOnlyPage && value.type === 'exist' && addInvoiceList.length > 0
          ? null
          : this.renderInvoiceInput({ showValue, editable, isEdit, isFrom, placeholder, label })}
        <Dynamic
          className="invoice-form-view"
          flowId={flowId}
          detailId={detailId}
          isDetail={isDetail}
          isEdit={didi ? false : isEdit}
          isFrom={isFrom}
          isReadOnlyPage={isReadOnlyPage}
          onDelete={onDelete}
          closeAndReturnValue={closeAndReturnValue}
          parentValue={showValue && showValue.parentValue}
          value={showValue && showValue.childrenValue}
          parentBus={bus}
          billSpecification={billSpecification}
          submitter={submitter}
          onResult={onResult}
          bus={this.bus}
          template={template}
          create={create}
          originField={field}
          importMode={importMode}
          isHideWait={isHideWait}
          elements={editableViews}
          invoiceImages={invoiceImages}
          parentTemplate={components}
          onTaxAmountChange={onTaxAmountChange}
          isInvoiceManagePermissions={isInvoiceManagePermissions}
          onRetryCheckerInvoiceClick={onRetryCheckerInvoiceClick}
          isEditAuthenticity={isEditAuthenticity}
          isEditTaxRate={isInvoiceManagePermissions && isDetail ? isEditTaxRate : false}
          calFields={calFields}
          isSort={isSort}
          onApporeIssueChange={onApporeIssueChange}
          onInvoiceStatusChange={onInvoiceStatusChange} //点击禁用发票
          disableInfo={disableInfo} //发票禁用展示状态
          markInfo={markInfo} //发票标记展示状态
          isTicketReview={isTicketReview}
          isModifyBill={isModifyBill}
          sortType={sortType}
          onSortInvoice={onSortInvoice}
          invoiceRiskData={invoiceRiskData}
          feetypeId={feetypeId}
          showPriceAndRate={showPriceAndRate}
          isExistConfirmInvoice={isExistConfirmInvoice}
          sourcePage={sourcePage}
          isQuickExpends={isQuickExpends}
          notShowModalIfAllInvoiceSuccess={notShowModalIfAllInvoiceSuccess}
          timeField={timeField}
        />
      </div>
    )
  }
}
