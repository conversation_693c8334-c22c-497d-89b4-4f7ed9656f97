import { app, app as api, UIContainer as Container } from '@ekuaibao/whispered'
import './approve.less'
import React, { Fragment } from 'react'
import { Modal } from 'antd-mobile'
import { Dialog, Input, Checkbox, SpinLoading } from '@hose/eui-mobile'
const { thousandBitSeparator } = app.require('@components/utils/fnThousandBitSeparator')
import { EnhanceConnect } from '@ekuaibao/store'
import { BillAdditionalMessageApi } from '@ekuaibao/ekuaibao_types'
import MessageCenter from '@ekuaibao/messagecenter'
const LogsCardView = app.require('@bill/panels/LogsCardView')
const History = app.require('@detail/history')
const FlowNodeView = app.require('@detail/flow-node-view')
const ActionsPanel = app.require('@elements/ActionsPanel')
const PrintPreview = app.require('@elements/puppet/PrintPreview')
const BillExpress = app.require('@elements/puppet/Express/BillExpress')
import { get, cloneDeep, sortBy } from 'lodash'
import { Fetch } from '@ekuaibao/fetch'
import { getBillHistoryVersionDetail, getBillHistoryVersionList, getApproverRepeatConfig } from '../bill/bill.action'
import {
  showSensitiveContent,
  STATE_LIST,
  logInfo,
  checkIsRemuneration,
  fixRemunerationSpecification,
  getDiffsBetweenVersions,
  getRiskReasonDataForVersionDiffModal,
  fnShareBillInfo,
  getSpecificationName
} from '../bill/utils/billUtils'
import questionnaireConfig from '../../lib/questionnaireConfig'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import {
  hideLoading,
  showLoading,
  toast,
  printRemindAlert,
  printReceiveExceptionAlert,
  printCancelReceiveExceptionAlert,
  getUrlParamString
} from '../../lib/util'
import { billActions } from '../../lib/util/billActions'
import PageContainer from '../page-container'
import * as actions from './approve.action'
import qs from 'qs'
import Big from 'big.js'
import RiskAlert from '../../elements/puppet/RiskNotice/RiskAlert'

import { isPrintShow } from '../../plugins/approve/util'
const {
  ACTIONS_STATE,
  addExpressInfo,
  comment,
  modifyFlow,
  print,
  printInvoice,
  printRemind,
  printReceive,
  receiveExpress,
  receiveExceptionExpress,
  cancelReceiveExceptionExpress,
  shift,
  signShift,
  skipExpress,
  cancelAddNote,
  addNote,
  modifyNullify,
  exportAttachment,
  billVersionBtn,
  hangUp
} = api.require('@basic-elements/basic-state-const')
const ReportListView = app.require('@basic-elements/budget/report-list-view')
const ETabs = app.require('@elements/EUITabs')
import {
  receiveExpresses,
  receiveExceptionExpresses,
  cancelReceiveExceptionExpresses,
  printReceiveAction,
  hangUpAction
} from './approveFetchUtil'
import WarningSensitive from '../../elements/puppet/WarningSensitive'
const RiskNotice = app.require('@elements/puppet/RiskNotice')
const formatRiskNotice = app.require('@bill/utils/formatRiskData.formatRiskNotice')
const UrgentView = app.require('@bill/panels/UrgentView')
const NoPermissionViewBillDetail = app.require('@bill/elements/NoPermissionViewBillDetail')
import { checkFlowAllRequiredFields, dealPrintData, doPrint } from './service'
import { getApproveFlowConfig, approveListFilter } from './approve.action'
import { Questionnaire } from '@hose/eui-mobile'
import { formatRiskWarningsV2 } from '../bill/utils/formatRiskData.formatRiskWarnings'
import SkeletonComponent from '../bill/panels/SkeletonModal'
import BillInfoButtons from '../../elements/FlowActionButton/BillInfoButtons'
import { FlowAction } from '../bill/panels/FlowAction'
import {startOpenFlowPerformanceStatistics, startOpenFlowThirdPartyPerformanceStatistics } from '../../lib/flowPerformanceStatistics';
import { getBoolVariation } from '../../lib/featbit'
import { actionsMap } from '../bill/panels/bill-actions/actions'
const EkbIcon = api.require('@elements/ekbIcon')

export function formatNodes(data, id, node) {
  const plan = data.plan || get(data, 'flow.plan') || get(data, 'flowId.plan')
  plan.nodes = plan.nodes.map(line => {
    if (line.id === id) {
      line.staff = line.staff || {}
      line.staff.avatar = node.avatar
      line.staff.name = node.name
      line.approverId = node
    }
    return line
  })
}

@EnhanceConnect(state => ({
  me_info: state['@common'].me_info,
  backlog: state['@approve'].current_backlog,
  reportChargePower: state['@common'].powers.Budget,
  expressPower: state['@common'].powers.Express,
  KA_PREVIEW_PRINT_IN_MODAL: state['@common'].powers.KA_PREVIEW_PRINT_IN_MODAL,
  submitterLoans: state['@common'].submitter_loans,
  standardCurrency: state['@common'].standardCurrency,
  isWhiteList: state['@common'].isWhiteList,
  globalFieldsMap: state['@common'].baseDataProperties.baseDataPropertiesMap,
  riskwarning: state['@common'].riskwarning,
  batchFlow_config: state['@approve'].batchFlow_config,
  remunerationSetting: state['@home'].remunerationSetting,
  RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization,
  KA_DETAILS_LAYOUT_POWER: state['@common'].powers.KA_DETAILS_LAYOUT,
  printPreviewUrl: state['@bill'].printPreviewUrl,
  continuousApproval: state['@approve'].continuousApproval,
  approveListFilter: state['@approve'].approveListFilter,
  billAdditionalInfo: state['@bill'].billAdditionalInfo
}))
export default class ApproveApproving extends PageContainer {
  constructor(props) {
    super(props)
    // 开通charge，展示小组件单据信息按钮
    const powerCodeMap = app.getState('@common').powers?.powerCodeMap || []
    const showWidget = powerCodeMap?.includes('160013')

    this.initializeFnBindContext(this, 'handleGoToHistory', '_handlePrint', 'handleFlowPlanEditableValueChange')
    this.taskNameRef = React.createRef()
    this._BACKLOG_ID_ = props.params.id
    this._FLOW_ID_ = props.params.flowId
    startOpenFlowThirdPartyPerformanceStatistics()
    this.state = {
      data: null,
      activeKey: '1',
      current_backlog: props.current_backlog,
      isPaymentModalShow: false,
      signChecked: true,
      hasExpressNode: false,
      noticeList: [],
      flowRiskList: [], //整理好的费用风险结构下发
      existBudgetOccupy: false,
      isAutographTips: false,
      isLoadingNext: false,
      isApprovePermissionSuccess: false,
      arrangeLayout: [],
      approverRepeatStatus: false, //单据当前审批人与前序节点重复时无权审批是否重复
      approverRepeatMessage: '', //单据当前审批人与前序节点重复时无权审批重复提示信息
      widgetConfig: {
        // 小组件配置
        config: undefined, // 小组件配置数据
        loading: showWidget, // 是否正在加载配置
        visible: showWidget // 是否需要展示小组件
      },
      showPrintBtn: false
    }
    this.defaultArrangeLayout = [
      {
        label: '风险提示',
        key: 'risks',
        type: 'default'
      },
      {
        label: '单据状态',
        key: 'status',
        type: 'default'
      },
      {
        label: '差旅订购',
        key: 'travel_order',
        type: 'default'
      },
      {
        label: '单据详情',
        key: 'details',
        type: 'default'
      },
      {
        label: '支付计划',
        key: 'payPlan',
        type: 'default'
      },
      {
        label: '核销组件',
        key: 'writtenOff',
        type: 'default'
      }
    ]

    api.dataLoader('@common.me_info').load()
  }

  bus = new MessageCenter()

  componentWillMount() {
    super.componentWillMount()
    this.initData()
    api.invokeService('@common:list:tripTypes')
    this.bus.on('element:click:message:panel', this.handleMessagePanel)
    this.bus.on('element:expense:link:click', this.handleExpenseLinkClick)
    this.bus.watch('invoice:updata:bills', this.handleUpDataBills)
    api.on('invoice:updata:bills', this.handleUpDataBills)
    if (this.props.continuousApproval === undefined && this.props.params?.sourcePage === 'approving') {
      api.invokeService('@approve:get:flow:approval:config')
    }
  }

  getPrintInvoice() {
    this.state?.flowId &&
      api.invokeService('@bill:get:show:print:invoice', { flowId: this.state?.flowId }).then(data => {
        this.setState({ showPrintBtn: data?.value || false })
      })
  }

  initData() {
    const { widgetConfig } = this.state
    let properties = api.getState()['@common'].baseDataProperties.data
    const fetchWidgetConfig = spec => {
      // 获取小组件配置
      if (widgetConfig.visible) {
        BillAdditionalMessageApi.fetchUserConfig(spec.id).then(res => {
          let widgetConfig = {
            config: undefined,
            loading: false,
            visible: false
          }
          if (!!res) {
            widgetConfig = {
              ...(this.state?.widgetConfig ?? {}),
              config: res,
              loading: false,
              visible: true
            }
          }
          this.setState({ widgetConfig })
        })
      }
    }
    if (!properties?.length) {
      api.invokeService('@common:get:basedata:properties').then(() => {
        api.dispatch(actions.getBackLog(this._BACKLOG_ID_, this._FLOW_ID_)).then(res => {
          this.backlog = res.value
          this.handleRiskWarning(res.value)
          const specificationCurrent = get(res.value, 'flowId.form.specificationId')
          const isRemuneration = checkIsRemuneration(specificationCurrent?.id || specificationCurrent)
          isRemuneration && fixRemunerationSpecification(specificationCurrent)
          fetchWidgetConfig(specificationCurrent.originalId)
          api.invokeService('@home:save:specification', res.value.flowId.form.specificationId)
          this.setState({ isLoadingNext: false, data: res.value })
        }).catch(error => {
          if (error?.errorCode === 403) {
            this.setState({ noPermission: true })
          }
        })
      })
    } else {
      api.dispatch(actions.getBackLog(this._BACKLOG_ID_, this._FLOW_ID_)).then(res => {
        this.handleRiskWarning(res.value)
        const specificationCurrent = get(res.value, 'flowId.form.specificationId')
        const isRemuneration = checkIsRemuneration(specificationCurrent?.id || specificationCurrent)
        isRemuneration && fixRemunerationSpecification(specificationCurrent)
        fetchWidgetConfig(specificationCurrent.originalId)

        api.invokeService('@home:save:specification', res.value.flowId.form.specificationId)
        this.setState({ isLoadingNext: false })
      }).catch(error => {
        if (error?.errorCode === 403) {
          this.setState({ noPermission: true })
        }
      })
    }
  }

  componentWillReceiveProps(nextProps) {
    let fn = this.fnCompareProps(this.props, nextProps)
    fn('backlog', () => {
      this.backlog = nextProps.backlog
      const { flowId } = this.backlog

      if (nextProps.expressPower) {
        // 判断是否有寄送节点
        const { nodes } = flowId.plan
        nodes.forEach(node => {
          if (node.expressConfig && node.expressConfig.type === 'send') this.hasExpressNode = true
        })
        if (this.hasExpressNode) {
          this.setState({ hasExpressNode: true })
        }
      }

      if (this.judegeBillStatus(flowId)) {
        const flow = this.backlog.flowId
        const name = getSpecificationName(flow?.form?.specificationId)
        api.invokeService('@layout:change:header:title', name)
        if(flow?.form?.submitterId) {
          api.invokeService('@common:getSubmitterLoanList', {
            submitterId: flow.form.submitterId,
            flowId: flow.id,
            state: 'REPAID',
            start: 0,
            count: 2999
          })
        }

        this.setState({ data: nextProps.backlog, flowId: flow.id })
        this.handleGetBudgetStatus(flow.id)
      }

      //研究所定制化需求
      if (window.isZhongdian) {
        this.getApproverRepeated(flowId?.id)
      }
    })
    const flowId = get(this.props, 'backlog.flowId.id')
    if (flowId !== get(nextProps, 'backlog.flowId.id')) {
      this.props.reportChargePower && this.getBudgetStatus(flowId)
    }

    let specificationIdNext = get(nextProps, 'backlog.flowId.form.specificationId')
    specificationIdNext = specificationIdNext?.id || specificationIdNext
    if (nextProps.KA_DETAILS_LAYOUT_POWER && specificationIdNext) {
      api.dispatch(actions.getLayoutConfig({ type: 'ARRANGE', specificationId: specificationIdNext })).then(resp => {
        const { arrangeLayout: arrangeLayoutResp } = resp.value?.configDetail || {}
        if (arrangeLayoutResp) {
          this.setState({
            arrangeLayout: arrangeLayoutResp
          })
        }
      })
    }
    this.initPrintPreviewData()
    this.getPrintInvoice()
  }

  // 准备审批预览页签需要的数据
  initPrintPreviewData = async () => {
    const { KA_PREVIEW_PRINT_IN_MODAL } = this.props
    if (!KA_PREVIEW_PRINT_IN_MODAL) return
    if (this.backlog && this.backlog.flowId) {
      let select = {}
      select[this.backlog.id] = this.backlog
      let data = dealPrintData([this.backlog.id], select)
      data.isTablePreview = true
      doPrint(data)
    }
  }

  //获取单据当前审批人与前序节点重复时是否无权审批
  getApproverRepeated = async id => {
    showLoading()
    try {
      const res = await getApproverRepeatConfig(id)
      if (res?.value) {
        const { isRepeated, message } = res.value
        this.setState({
          approverRepeatStatus: isRepeated,
          approverRepeatMessage: message
        })
      }
    } finally {
      hideLoading()
    }
  }

  componentWillUnmount() {
    api.invokeService('@bill:change:add:note:mode', false)
    this.bus.un('element:click:message:panel', this.handleMessagePanel)
    this.bus.un('element:expense:link:click', this.handleExpenseLinkClick)
    this.bus.un('invoice:updata:bills', this.handleUpDataBills)
    api.un('invoice:updata:bills', this.handleUpDataBills)
    api.dispatch(approveListFilter(undefined)) //清空filter
    this.clearPrintPreviewUrl()
    api.invokeService('@bill:update:flow:append:info', {})
  }

  clearPrintPreviewUrl = () => {
    if (!this.props.KA_PREVIEW_PRINT_IN_MODAL) return
    api.invokeService('@bill:set:print:preview:url', '')
  }

  getBudgetStatus = id => {
    id &&
      api.invokeService('@common:get:budget:status', { id }).then(res => {
        this.setState({ existBudgetOccupy: res.value })
      })
  }

  handleRiskWarning = value => {
    let {
      flowId: { form, id }
    } = value
    let { globalFieldsMap, RiskPromptOptimization, backlog } = this.props
    api.invokeService('@common:get:riskwarningById', { flowId: id }).then(risks => {
      if (!risks) return
      const components = get(value, 'flowId.form.specificationId.components')
      const details = get(backlog, 'flowId.form.details', [])
      let flowRiskList = formatRiskWarningsV2(cloneDeep(risks), details)
      let noticeList = formatRiskNotice({
        riskData: flowRiskList,
        components,
        globalFieldsMap,
        bus: this.bus,
        submitter: form.submitterId,
        RiskPromptOptimization
      })
      this.setState({ noticeList, flowRiskList, invoiceRiskData: risks })
    })
  }

  handleUpDataBills = () => {
    api.dispatch(actions.getBackLog(this._BACKLOG_ID_, this._FLOW_ID_)).then(result => {
      let { value } = result
      this.handleRiskWarning(value)
      value && this.setState({ data: value })
    }).catch(error => {
      if (error?.errorCode === 403) {
        this.setState({ noPermission: true })
      }
    })
  }

  handleMessagePanel = type => {
    if (type === 'budget') {
      if (this.state.existBudgetOccupy) {
        this.setState({ activeKey: '3' })
      } else {
        Dialog.alert({
          content: i18n.get('您不是该预算的负责人，无法查看详情。如需查看请联系预算管理员。'),
          confirmText: i18n.get('确定')
        })
      }
    } else if (type === 'loan') {
      let {
        flowId,
        data: {
          flowId: { form }
        }
      } = this.state
      api.go('/submitter/' + form.submitterId.id + '/' + flowId)
    }
  }

  handleExpenseLinkClick = value => {
    const { flowId } = this.state
    value.flowId = flowId
    api.invokeService('@requisition:save:current:requisition', value).then(() => {
      api.go('/requisitionDetail/' + false)
    })
  }

  handleGetBudgetStatus(flowId) {
    if (this.props.reportChargePower) {
      flowId &&
        api.invokeService('@common:get:flow:budget:list', { id: flowId }).then(data => {
          if (get(data, 'items', []).length) {
            this.setState({ isBudgetOn: true })
          }
        })
      api.invokeService('@common:get:budget:status', { id: flowId }).then(data => {
        this.setState({ existBudgetOccupy: data.value })
      })
    }
  }

  handleFlowPlanEditableValueChange = (id, node) => {
    const data = cloneDeep(this.state.data)
    formatNodes(data, id, node)
    this.setState({ data })
  }

  handleGoToHistory = () => {
    this.setState({ activeKey: '2' }, _ => {
      this.bus.emit('history:type:change', 'comment')
    })
  }

  _handleComment() {
    let id = this.backlog.flowId.id
    api.open('@bill:BillComment').then(params => {
      api.invokeService('@bill:comment:bill', { id, params }).then(_ => {
        logInfo(`评论${this.backlog.flowId.form.title}单据`)
        this.handleUpDataBills()
      })
    })
  }

  _handleShift = async transferType => {
    const { params, continuousApproval } = this.props
    const { data: backlog } = this.state
    const { sourcePage } = params || {}
    const isPaying = backlog.state === 'PAYING'
    const { value: rejectedAddNode } = await api.invokeService('@common:get:toggle:switch:by:name', {
      key: 'tg_fix_rejected_addNode'
    })
    const data = await api.open(!transferType && rejectedAddNode ? '@basic:RejectedAddNodeModal' : '@basic:PlanShift', {
      isPaying,
      flowDatas: [backlog],
      isSignShift: !transferType
    })
    // 后加签需要校验表单是否有必填字段
    if (data.type == 'AFT_ADD_NODE') {
      const allFieldCheckIn = await checkFlowAllRequiredFields([backlog.flowId])
      if (!allFieldCheckIn) return
    }
    await api.dispatch(actions.doAction({ id: this._BACKLOG_ID_ }, { ...data, name: 'freeflow.addnode' }))
    if (continuousApproval && sourcePage === 'approving' && !isPaying) {
      this.approveNext()
    } else {
      api.go(-1)
    }
  }

  approveNext = () => {
    api
      .invokeService('@approve:get:approving:list', {
        filterBy: this.props.approveListFilter?.filterBy,
        waitInvoice: this.props.approveListFilter?.waitInvoice,
        page: { start: 0, count: 1 }
      })
      .then(res => {
        if (res?.items?.length) {
          this.setState({ isLoadingNext: true, activeKey: '1' })
          const data = res.items[0]
          this._BACKLOG_ID_ = data.id
          this.initData()
        } else {
          api.go(-1)
        }
      })
  }

  _handleAddExpressInfo = () => {
    api.open('@basic:ExpressAdd', { backlog: this.props.backlog, label: i18n.get('添加寄送信息') })
  }

  _handleSkipExpress = () => {
    api.open('@basic:ExpressAdd', { backlog: this.props.backlog, label: i18n.get('跳过寄送'), type: 'skipExpress' })
  }

  _handleReceiveExpress = () => {
    const { backlog } = this.props
    api.open('@basic:PlanResolve', { data: { backlog } }).then(value => {
      showLoading()
      receiveExpresses({ backlogIds: [backlog.id], ...value }).then(data => {
        hideLoading()
        const list = (data.value && data.value.errors) || []
        const errorList = list.filter(item => item.resultCode !== 'OK')
        if (errorList.length) {
          return Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
        }
        api.go(-1)
      })
    })
  }

  _handleExceptionReceiveExpress = () => {
    const { backlog } = this.props
    return printReceiveExceptionAlert(() => {
      showLoading()
      receiveExceptionExpresses({ backlogIds: [backlog.id], comment: '收单异常' }).then(data => {
        hideLoading()
        const list = (data.value && data.value.errors) || []
        const errorList = list.filter(item => item.resultCode !== 'OK')
        if (errorList.length) {
          return Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
        }
        api.go(-1)
      })
    })
  }

  _handleCancelExceptionReceiveExpress = () => {
    const { backlog } = this.props
    return printCancelReceiveExceptionAlert(() => {
      showLoading()
      cancelReceiveExceptionExpresses({ backlogIds: [backlog.id], comment: '取消收单异常' }).then(data => {
        hideLoading()
        const list = (data.value && data.value.errors) || []
        const errorList = list.filter(item => item.resultCode !== 'OK')
        if (errorList.length) {
          return Dialog.alert({ title: i18n.get('操作失败'), content: data.value.errors[0].message })
        }
        api.go(-1)
      })
    })
  }

  // 检测单子是否包含附件
  checkAttachment = () => {
    const backlog = this.state.data
    const form = get(backlog, 'flowId.form', {})
    const formComponents = get(backlog, 'flowId.form.specificationId.components', [])
    const hasAttachment = this.checkAttachmentFn(form, formComponents)
    return hasAttachment || this.checkDetailsAttachment()
  }
  // 检测 明细是否包含附件
  checkDetailsAttachment = () => {
    const backlog = this.state.data
    const details = get(backlog, 'flowId.form.details', [])
    let hasAttachment = false
    for (let i = 0; i < details.length; i++) {
      const el = details[i]
      const form = get(el, 'feeTypeForm', {})
      const components = get(el, 'specificationId.components', [])
      const hasAttachment2 = this.checkAttachmentFn(form, components)
      if (hasAttachment2) {
        hasAttachment = true
        break
      }
    }
    return hasAttachment
  }
  checkAttachmentFn = (form, components) => {
    const attachmentsArr = components.filter(it => it?.type === 'attachments')
    let hasAttachment = false
    if (attachmentsArr.length > 0) {
      for (let i = 0; i < attachmentsArr.length; i++) {
        const el = attachmentsArr[i]
        const attachments = form?.[el?.field]
        if (attachments?.length > 0) {
          for (let j = 0; j < attachments.length; j++) {
            const item = attachments[j]
            // 必须要有非钉盘的文件才能导出,都是钉盘文件不到处
            if (!item?.key?.startsWith('DP:{')) {
              hasAttachment = true
              break
            }
          }
          if (hasAttachment) {
            break
          }
        }
      }
    }
    return hasAttachment
  }

  _export = async flowId => {
    const hasAttachment = this.checkAttachment()
    if (!hasAttachment) {
      toast.info(i18n.get('无可导出附件或附件是钉盘上传'))
      return
    }

    Dialog.confirm({
      title: i18n.get('导出全部附件'),
      content: (
        <>
          <div style={{ marginBottom: '20px' }}>
            {i18n.get('进程将在后台处理，请输入任务名称并在「导出管理」查看任务结果。')}
          </div>
          <Input
            ref={this.taskNameRef}
            placeholder={i18n.get('请输入任务名称')}
            onlyShowClearWhenFocus={false}
            clearable
            border
          />
        </>
      ),
      confirmText: i18n.get('导出'),
      onConfirm: async () => {
        const taskName = this.taskNameRef?.current?.nativeElement?.value
        if (!taskName || taskName.length > 12) {
          toast.info(i18n.get('任务名不能为空或者不超过12个字符'))
          throw new Error()
        }
        Fetch.GET(`/api/v2/easyexcel/annex/export/async?taskName=${taskName}&flowId=${flowId}`)
        toast.info(i18n.get('附件异步导出中,请稍后到导出管理中查看!'), 3000)
      }
    })
  }

  fnGetActionMap = () => {
    return {
      [FlowAction.Print]: { onClick: () => this._handlePrint() },
      [FlowAction.PrintInvoice]: { onClick: () => this._handlePrintAndInvoice() },
      [FlowAction.PrintRemind]: { onClick: () => this._handlePrintRemind() },
      [FlowAction.Printed]: { onClick: () => this._handlePrintReceive() },
      [FlowAction.Modify]: { onClick: () => this._handleModifyFlow() },
      [FlowAction.Comment]: { onClick: () => this._handleComment() },
      [FlowAction.Send]: { onClick: () => this._handleAddExpressInfo() },
      [FlowAction.SkipSend]: { onClick: () => this._handleSkipExpress() },
      [FlowAction.Receive]: { onClick: () => this._handleReceiveExpress() },
      [FlowAction.ReceiveException]: { onClick: () => this._handleExceptionReceiveExpress() },
      [FlowAction.CancelReceiveException]: { onClick: () => this._handleCancelExceptionReceiveExpress() },
      [FlowAction.AddAnnotation]: {
        onClick: button => {
          const { inAddNoteMode } = this.state
          inAddNoteMode ? this.handleCancelAddNote() : this.handleAddNote()
          return {
            id: button.id,
            changeButton: true,
            name: inAddNoteMode ? i18n.get('添加批注') : i18n.get('退出批注模式'),
            showCurrentButton: !inAddNoteMode,
            currencyCategory: 'primary'
          }
        }
      },
      [FlowAction.Nullify]: { onClick: () => this._handleNullifyFlow() },
      [FlowAction.Suspend]: { onClick: () => this._handleHangUp() },
      [FlowAction.AuxiliaryInformation]: { onClick: () => this.handleViewDiff() },
      [FlowAction.AddNode]: { onClick: () => this._handleShift() },
      [FlowAction.ShiftNode]: { onClick: () => this._handleShift('SHIFT_NODE') },
      [FlowAction.Agree]: {
        onClick: () => {
          this._handleAction(FlowAction.Agree, 'PlanResolve')
        }
      },
      [FlowAction.Reject]: {
        onClick: () => {
          this._handleAction(FlowAction.Reject, 'PlanReject')
        }
      },
      [FlowAction.Pay]: {
        onClick: () => {
          this._handleAction(FlowAction.Pay, 'PlanPay')
        }
      },
      [FlowAction.ExportAllAttachment]: {
        onClick: () => {
          let backlog = this.state.data
          this._export(backlog?.flowId?.id)
        }
      },
      [FlowAction.Share]: {
        onClick: () => {
          const backlog = this.state.data
          fnShareBillInfo(backlog?.flowId?.id)
        }
      }
    }
  }

  lazyGetActionMapByButtons = (buttons) => {
    const commonOptions = {
      params: this.props.params,
      backlog: this.state.data,
      refreshData: () => this.handleUpDataBills(),
      onApproveNext: () => this.approveNext(),
    }
    const getPropsForActionMap = (button) => {
      const map = {
        [FlowAction.Agree]: {
          onApprovePermissionSuccess: () => {
            this.setState({ isApprovePermissionSuccess: true })
          }
        },
        [FlowAction.Reject]: {
          onApprovePermissionSuccess: () => {
            this.setState({ isApprovePermissionSuccess: true })
          }
        },
        [FlowAction.AddAnnotation]: {
          inAddNoteMode: this.state.inAddNoteMode,
          onModeChange: (inAddNoteMode) => {
            this.setState({ inAddNoteMode })
            return {
              id: button.id,
              changeButton: true,
              name: inAddNoteMode ? i18n.get('退出批注模式') : i18n.get('添加批注'),
              showCurrentButton: inAddNoteMode,
              currencyCategory: 'primary'
            }
          }
        }
      }
      return map[button.action]
    }
    return buttons.reduce((acc, button) => {
      const Action = actionsMap[button.action]
      if (Action) {
        acc[button.action] = new Action({
          ...commonOptions,
          actionName: button.action,
          propsForAction: getPropsForActionMap(button)
        })
      }
      return acc
    }, {})
  }

  _buttonsClick = params => {
    const { name = '', page = '' } = params
    let backlog = this.state.data
    let Bill_approval_result
    switch (name) {
      case 'print':
        return this._handlePrint()
      case 'printInvoice':
        return this._handlePrintAndInvoice()
      case 'printRemind':
        return this._handlePrintRemind()
      case 'printReceive':
        return this._handlePrintReceive()
      case 'modify_flow':
        startOpenFlowPerformanceStatistics()
        return this._handleModifyFlow()
      case 'comment':
        return this._handleComment()
      case 'addExpressInfo':
        return this._handleAddExpressInfo()
      case 'skipExpress':
        return this._handleSkipExpress()
      case 'receiveExpress':
        return this._handleReceiveExpress()
      case 'receiveExceptionExpress':
        return this._handleExceptionReceiveExpress()
      case 'cancelReceiveExceptionExpress':
        return this._handleCancelExceptionReceiveExpress()
      case 'addNote':
        return this.handleAddNote()
      case 'cancelAddNote':
        return this.handleCancelAddNote()
      case 'modify_nullify':
        return this._handleNullifyFlow()
      case 'freeflow.agree': //同意
        Bill_approval_result = '同意'
        break
      case 'freeflow.reject': //驳回
        if (backlog.state === 'APPROVING') {
          Bill_approval_result = '驳回'
        }
        break
      case 'exportAttachment':
        return this._export(backlog?.flowId?.id)
      case 'billVersionBtn':
        return this.handleViewDiff()
      case 'hangUp':
        return this._handleHangUp()
      case 'share':
        return fnShareBillInfo(backlog?.flowId?.id)
      default:
        break
    }
    Bill_approval_result &&
      this.newTrack('Bill_approval', {
        actionName: '单据处理',
        Bill_approval_result,
        Approval_page_display_source: '非邮件',
        flowID: backlog.flowId.id
      })
    return this._handleAction(name, page)
  }
  newTrack(key, options) {
    //单据处理神策
    api.track(key, {
      ...options,
      decice: 'applet',
      source: window.__PLANTFORM__,
      userId: api.getState()['@common'].me_info?.staff?.userId,
      corName: api.getState()['@common'].me_info?.staff?.corporationId?.name
    })
  }

  // 获取历史版本数据
  getHistoryVersions = async data => {
    const res = await getBillHistoryVersionList(data?.id, '')
    return res
  }

  // 获取两个版本间的数据diff
  getDiffs = async (type, curId, prevId) => {
    const privilegeId = ''
    const curVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    const prevVersion = await getBillHistoryVersionDetail(prevId, privilegeId)
    const diffs = await getDiffsBetweenVersions(type, curVersion, prevVersion)
    return diffs
  }

  // 打开小组件
  handleViewDiff = async () => {
    const { invoiceRiskData, data, widgetConfig } = this.state
    const historyVersions = await this.getHistoryVersions({ id: data?.flowId?.id })
    app.open('@bill:BillVersionDiff', {
      versions: historyVersions?.items,
      getDiffs: this.getDiffs,
      riskData: getRiskReasonDataForVersionDiffModal(invoiceRiskData),
      dataSource: data?.flowId?.form,
      config: widgetConfig.config
    })
  }

  handleCancelAddNote = () => {
    api.invokeService('@bill:change:add:note:mode', false)
    this.setState({ inAddNoteMode: false })
  }

  handleAddNote = () => {
    api.invokeService('@bill:change:add:note:mode', true)
    this.setState({ inAddNoteMode: true })
  }

  _handlePrint = () => {
    if (this.backlog && this.backlog.flowId) {
      let select = {}
      select[this.backlog.id] = this.backlog
      let data = dealPrintData([this.backlog.id], select)
      doPrint(data, null, this.handleUpDataBills, '0')
    }
  }

  _handlePrintAndInvoice = () => {
    if (this.backlog && this.backlog.flowId) {
      let select = {}
      select[this.backlog.id] = this.backlog
      let data = dealPrintData([this.backlog.id], select)
      doPrint(data, null, this.handleUpDataBills, '1')
    }
  }

  _handleModifyFlow() {
    const { data } = this.state
    const {
      flowId: { id, formType }
    } = data
    const ownerId = this.backlog && this.backlog.ownerId
    api.go(`/modify/${formType}/${id}/${ownerId}?source=Modify`) // source 为了区分立即修改和修改
  }

  _handleNullifyFlow = () => {
    let { flowId } = this.backlog
    const nodes = get(flowId, 'plan.nodes', [])
    const taskId = get(flowId, 'plan.taskId', '')
    const planNode = nodes.find(v => v.id === taskId) || {}
    const lockInvoiceWhenNullify = planNode?.config?.lockInvoiceWhenNullify
    api.open('@basic:NullifyFlowModal', { lockInvoiceWhenNullify }).then(result => {
      const nullifyType = result.checkedValue ? 'lockInvoice' : 'releaseInvoice'
      const data = {
        resubmitMethod: 'FROM_START',
        comment: '',
        name: 'freeflow.nullify',
        params: { nullifyType: nullifyType }
      }
      api.dispatch(actions.setFlowNullify({ id: this._BACKLOG_ID_, data })).then(data => {
        api.go(-1)
      })
    })
  }

  _handlePrintReceive() {
    const { data } = this.state
    const {
      flowId: { id, form }
    } = data
    printReceiveAction([id]).then(_ => {
      logInfo(`收到打印${form.title}单据`)
      toast.success(i18n.get('操作成功'))
      this.handleUpDataBills()
    })
  }

  _handleHangUp() {
    const { data } = this.state
    const {
      id,
      flowId: { form }
    } = data
    hangUpAction([id]).then(_ => {
      logInfo(`暂挂审批${form.title}单据`)
      toast.success(i18n.get('操作成功'))
      this.handleUpDataBills()
    })
  }

  _handlePrintRemind() {
    const { data } = this.state
    const {
      flowId: { id, form }
    } = data
    printRemindAlert(() => {
      api
        .dispatch(actions.printRemindAction([id]))
        .then(res => {
          const { errors } = res
          if (errors.length > 0) {
            const err = errors[0]
            return Dialog.alert({
              title: i18n.get('操作失败'),
              content: err.message,
              confirmText: i18n.get('确定'),
              onConfirm: () => api.go(-1)
            })
          }
          logInfo(`提醒打印${form.title}单据`)
          toast.success(i18n.get('提醒成功'))
        })
        .then(_ => this.handleUpDataBills())
    })
  }

  _initSurvey = () => {
    const { backlog } = this.props
    let type = backlog?.flowId?.form?.specificationId?.type
    type = billTypeMap()[type || 'expense']
    Questionnaire.initSurvey({
      sid: questionnaireConfig?.approve?.sid,
      channelId: questionnaireConfig?.approve?.channelId,
      externalUserId: api.getState()['@common'].me_info?.staff?.userId,
      externalCompanyId: api.getState()['@common'].me_info?.staff?.corporationId?.id,
      parameters: {
        name: backlog?.flowId?.form?.specificationId?.name,
        type
      }
    })
  }

  _handleAction = async (name, page) => {
    const { params, riskwarning, continuousApproval } = this.props
    const { data: backlog, approverRepeatStatus, approverRepeatMessage } = this.state
    const { sourcePage, fromThirdParty: defaultFromThirdParty } = params || {}
    // 如果第三方地址栏携带backLastPage=true，则表示从第三方页面返回
    const urlParams = qs.parse(window.location.search.slice(1))
    let fromThirdParty = defaultFromThirdParty
    if (urlParams?.backLastPage === 'true') {
      fromThirdParty = window.isPC ? defaultFromThirdParty : undefined
    }

    if (name === 'freeflow.agree' || name === 'freeflow.pay') {
      const allFieldCheckIn = await checkFlowAllRequiredFields([backlog.flowId])
      if (!allFieldCheckIn) return
    }

    let overrideProps = {}
    if (name === 'freeflow.agree') {
      if (approverRepeatStatus) {
        return Dialog.alert({ title: i18n.get('温馨提示'), content: approverRepeatMessage })
      }
      overrideProps = {
        onChange: this.handleFlowPlanEditableValueChange
      }
    }

    if (name === 'freeflow.pay') {
      if (backlog.flowId.form.payMoney.standard * 1 === 0) {
        api.dispatch(getApproveFlowConfig(backlog.flowId.id)).then(() => {
          logInfo(`支付${backlog.flowId.form.title}单据`)
          this.setState({ isPaymentModalShow: true })
        })
      } else {
        api
          .open('@basic:PlanPay', {
            data: {
              backlog: [backlog],
              onPay: data => api.dispatch(actions.payBackLog(data)),
              onConfirm: data => {
                logInfo(`支付了单据${backlog.flowId.form.title}`)
                return api.dispatch(actions.confirmPayment(data))
              }
            }
          })
          .then(_ => {
            if (fromThirdParty) {
              window.location.reload()
            } else {
              api.go(-1)
            }
          })
      }
    } else if (name === 'shift') {
      this._handleShift('SHIFT_NODE')
    } else if (name === 'signShift') {
      this._handleShift()
    } else {
      let isBudgetExcited = []
      if (name !== 'freeflow.reject' && !!riskwarning) {
        isBudgetExcited = riskwarning.riskWarning
      }
      //判断是否存在设置了单个节点的驳回配置，如果设置了，弹窗提示，走单个节点的驳回配置
      let rejectData
      if (name === 'freeflow.reject') {
        let nodes = backlog?.flowId?.plan?.nodes || []
        let taskId = backlog?.flowId?.plan?.taskId
        rejectData = this.getRejectData(taskId, nodes) || {}
      }
      let data = await api.open(`@basic:${page}`, {
        data: { backlog },
        riskWarningCount: riskwarning?.moneyCount,
        isBudgetExcited,
        isShowRiskNotice: true,
        ...overrideProps,
        spcSetting: rejectData?.spcSetting,
        spcResubmitMethod: rejectData?.spcResubmitMethod,
        spcRejectTo: rejectData?.id,
        selectData: [backlog]
      })
      data = { ...data, name }
      try {
        showLoading()
        const isApproveNext = continuousApproval && sourcePage === 'approving'
        const isApprovePermission = location.pathname?.includes('approvePermission')
        const result = await api.invokeService('@bill:get:current:backLog', this.state.data.flowId.id)
        const id = isApproveNext ? this._BACKLOG_ID_ : result.id || this._BACKLOG_ID_
        if (name === 'freeflow.reject') {
          const subData = [{ id, ...data, name: 'freeflow.reject' }]
          const res = await api.dispatch(actions.spAgreeBackLog({ data: subData }))
          if (res?.success === 0 && res?.errors?.length) {
            Dialog.alert({
              title: i18n.get('操作失败'),
              content: res.errors[0].message || i18n.get('驳回失败'),
              confirmText: i18n.get('确定')
            })
            hideLoading()
            return
          }
        } else {
          //连续审批的场景，有时候审批成功后，调用@bill:get:current:backLog接口返回的还是老的backlogId，所以直接取this._BACKLOG_ID_的值，这个值是通过另一个接口取的是正确的
          await api.dispatch(actions.doAction({ id }, data))
        }
        if (name === 'freeflow.reject' || name === 'freeflow.agree') {
          logInfo(`${name === 'freeflow.reject' ? '驳回' : '同意'}${this.state.data.flowId.form.title}单据`)
          // 临时授权接口增加【审批完成后跳转的地址】参数，仅pageType=form/backlogDetail时有效。
          const urlState = qs.parse(window.location.search.slice(1))
          if (urlState && ['form', 'backlogDetail'].includes(urlState.pageType) && urlState.approvalUrl) {
            return api.invokeService('@layout:open:link', urlState.approvalUrl)
          }
        }
        hideLoading()
        if (fromThirdParty) {
          window.location.reload()
        } else if (isApprovePermission) {
          setTimeout(() => {
            if (window.isPC) {
              Dialog.alert({
                content: i18n.get('审批成功, 点击确认关闭本窗口'),
                confirmText: i18n.get('确认'),
                onConfirm: () => {
                  window.opener = null
                  window.open('', '_self')
                  window.close()
                }
              })
            } else {
              this.setState({ isApprovePermissionSuccess: true })
              toast.success(i18n.get('审批成功'))
            }
          }, 1.8)
        } else {
          if (isApproveNext) {
            this.approveNext()
          } else {
            if(getBoolVariation('cyxq-75554')&& urlParams?.messagePurpose === 'sharing' && window.isDingtalk) {
              api.invokeService('@layout:close:window')
            } else {
              api.go(-1)
            }
          }
          this._initSurvey()
        }
      } catch (err) {
        console.log(err)
        hideLoading()
      }
    }
  }

  getRejectData(taskId, nodes) {
    // 根据 taskId 到nodes里面找数据
    let spcSetting = false,
      spcResubmitMethod,
      id,
      rejectTo,
      rejectToNode,
      rejectToNodeIndex

    for (var i = 0; i < nodes.length; i++) {
      if (nodes[i].id === taskId) {
        rejectToNode = nodes[i]
        rejectToNodeIndex = i
        break
      }
    }

    // 在nodes里面找到数据后， 在查看 rejectSetting 数据
    if (rejectToNode.hasOwnProperty('rejectSetting') && rejectToNode.rejectSetting?.rejectMethod?.methods?.length > 0) {
      //存在设置了单个节点的驳回配置，弹窗提示，走单个节点的驳回配置
      spcSetting = true
      rejectTo = rejectToNode?.rejectSetting?.rejectTo

      // 如果 rejectSetting 中 rejectTo 有值， 根据 rejectTo 值到整个nodes里面找 configNodeId 相同数据 （type不能为ebot）， 找到之后获取id
      if (rejectTo === null) {
        id = null
      }

      if (!id && id !== null) {
        for (var i = 0; i < nodes.length; i++) {
          if (
            rejectTo === nodes[i].configNodeId &&
            nodes[i].type != 'ebot' &&
            nodes[i].type != 'invoicingApplication'
          ) {
            id = nodes[i].id
            break
          }
        }
      }

      // 根据 rejectTo 没有找到数据， 则根据当前node索引往上找最近 非 ebot数据， 找到后返回id， 没有到话 返回null
      if (!id && id !== null) {
        for (var i = rejectToNodeIndex - 1; i >= 0; i--) {
          if (nodes[i].type != 'ebot' && nodes[i].type != 'invoicingApplication') {
            id = nodes[i].id
            break
          }
        }
      }

      if (!id && id !== null) {
        id = null
      }

      spcResubmitMethod = rejectToNode?.rejectSetting?.rejectMethod?.methods[0]
      return { spcResubmitMethod, id, spcSetting }
    }
  }

  handleTabClick = key => {
    this.setState({
      activeKey: key
    })
    key === '3' && this.bus.emit('budget:tab:click')
  }

  handleGotoDetail = line => {
    api.go(
      '/flow-report-detail/' +
        this.state.flowId +
        '/' +
        line.budgetId +
        '/' +
        line.fromNodeId +
        '/' +
        line.nodeId +
        '/' +
        line.periodTime
    )
  }

  handleZeroPay = () => {
    const { me_info, batchFlow_config } = this.props
    let backlog = this.state.data
    let params = { flowIds: [backlog.flowId.id] }
    let autograph = me_info?.staff?.autograph
    const mustBeUsedSignature = get(batchFlow_config, 'mustBeUsedSignature')
    if (mustBeUsedSignature && !autograph) {
      this.setState({ isAutographTips: true })
    } else {
      if (this.state.signChecked && autograph) {
        params.autographImageId = autograph.key
      }
      api.dispatch(actions.zeroPay(params)).then(_ => {
        api.go(-1)
      })
    }
  }

  handleCancelZeroPay = () => {
    this.setState({ isPaymentModalShow: false })
  }

  handleSignChange = signChecked => {
    this.setState({ signChecked })
  }

  renderFlowState() {
    let value = this.props.backlog && this.props.backlog.flowId
    let { data } = this.state
    if (!data) {
      return null
    }
    return (
      <div className="plan-content-wrapper">
        <div className="scrollable plan-page inertial-rolling h-100-percent w-100p h-100p">
          <FlowNodeView value={data} isReadOnly={false} />
          <div className="approve-history">
            <History value={value} bus={this.bus} />
          </div>
        </div>
      </div>
    )
  }

  pushShareBtn = btns => {
    const urlParams = qs.parse(window.location.search.slice(1))
    if (urlParams?.hiddenShareBtn) {
      return
    }
    if (window.isDingtalk || window.isFeishu || window.isWxWork) {
      const shareBtn = {
        name: 'share',
        label: i18n.get('转发'),
        weight: 150
      }
      btns.push(shareBtn)
    }
  }

  renderDetailActions() {
    const { me_info, params, billAdditionalInfo = {} } = this.props
    const { data: backlog, inAddNoteMode, isApprovePermissionSuccess, widgetConfig, showPrintBtn } = this.state
    const isShowActionPanel =
      !isApprovePermissionSuccess && (!widgetConfig.visible || (widgetConfig.visible && !widgetConfig.loading))

    if (!isShowActionPanel || !backlog || !me_info?.staff?.id) return null

    const billState = backlog.state
    const meId = me_info.staff.id
    let { flowId, type } = this.backlog
    let flowActions = flowId.actions
    let arr = []
    const nodes = get(flowId, 'plan.nodes', [])
    const taskId = get(flowId, 'plan.taskId', '')
    const planNode = nodes.find(v => v.id === taskId) || {}

    if (params?.messageSource === 'workrecord') {
      arr = billActions(me_info, flowActions)
      if (!['reject', 'draf'].includes(flowId.state)) {
        arr.push(comment())
      }
    } else {
      flowActions = flowActions[meId] || []
      let showShift = false
      flowActions = flowActions
        .map(name => {
          const action = ACTIONS_STATE[name]
          let tmp = action && action()

          if (name === 'freeflow.select.approver') {
            return null
          }
          if (name === 'freeflow.addnode') {
            showShift = true
          }
          return tmp
        })
        .filter(item => !!item)

      // 是否是审批人
      const isApprover = !!flowId.actions[meId]?.length
      // 是否在分享的消息中
      const messagePurpose = getUrlParamString(location.search, 'messagePurpose')
      const inSharingMessage = messagePurpose === 'sharing'
      // 非审批人在分享的消息中浏览单据
      const nonApproverInSharingMessage = !isApprover && inSharingMessage

      let leftButtons = nonApproverInSharingMessage
        ? [comment(), print(), printInvoice()]
        : [exportAttachment(), comment(), print(), printInvoice(), printRemind(), printReceive(), hangUp()]

      // 审批节点禁止驳回
      if (planNode.forbidRejectNode) {
        const isForbidReject = planNode.forbidRejectNode ? true : false
        flowActions = flowActions.map(item => {
          if (item.name === 'freeflow.reject') {
            item.disabled = isForbidReject
            item.bgColor = ''
            item.buttonType = ''
            item.dangerous = ''
          }
          return item
        })
      }

      // 根据节点配置展示的操作按钮，不提供展示给在钉钉会话消息中查看的非审批人
      if (!nonApproverInSharingMessage) {
        if (showShift) {
          if (!planNode.forbidShiftNode) {
            leftButtons.push(shift())
          }
          if (!planNode.forbidBeforeAddNode || !planNode.forbidAftAddNode) {
            leftButtons.push(signShift())
          }
        }
        if (planNode.allowModify && billState !== 'PROCESSING' && billState !== 'CANCELED') {
          leftButtons.push(modifyFlow())
        }
        if (planNode.config?.allowApproverNullify) {
          leftButtons.push(modifyNullify())
        }
        if (planNode.expressConfig?.type === 'send') {
          flowActions = flowActions.concat([addExpressInfo(), skipExpress()])
          leftButtons = leftButtons.filter(line => line.name !== 'shift')
          flowActions = flowActions.filter(line => line.name !== 'freeflow.reject')
        } else if (planNode.expressConfig?.type === 'receive') {
          flowActions = flowActions.concat([receiveExpress()])
          flowActions = flowActions.filter(line => line.name !== 'freeflow.reject')
          let ExpressPower = api.getState()['@common'].powers.Express
          if (ExpressPower) {
            leftButtons.push(receiveExceptionExpress())
          }
          leftButtons = leftButtons.filter(line => line.name !== 'shift')
        }
      }

      if (!showPrintBtn) {
        leftButtons = leftButtons.filter(item => {
          return item.name !== 'printInvoice'
        })
      }

      arr = flowActions.concat(leftButtons)
    }

    //加入添加批注按钮
    arr.push(addNote())
    //判断有无打印按钮权限
    arr = isPrintShow({ selectData: this.backlog })
      ? arr
      : arr.filter(line => line.name !== 'print' && line.name !== 'printRemind')

    let rightButtons = []

    //批注模式判断
    if (inAddNoteMode) {
      arr = []
      rightButtons = [cancelAddNote()]
    } else {
      arr = sortBy(arr, item => item.weight)
      if (planNode.expressConfig?.type === 'send') {
        rightButtons = arr.splice(-2)
      } else {
        rightButtons = arr.splice(-3)
      }
    }
    if (billState === 'RECEIVING_EXCEP') {
      rightButtons = [receiveExpress(), cancelReceiveExceptionExpress()]
      arr = []
    }

    // 小组件配置辅助信息按钮
    if (widgetConfig.visible) {
      const showMobileShortcut = widgetConfig.config?.otherConfig?.showMobileShortcut
      if (showMobileShortcut) {
        if (rightButtons.length >= 4) {
          arr.push(rightButtons.shift())
        }
        const btn = billVersionBtn()
        btn.label = (
          <div style={{ display: 'block', lineHeight: 'normal' }}>
            <EkbIcon name="#danjuxinxi" style={{ fontSize: '24px', display: 'block', margin: '0 auto' }} />
            <div style={{ fontSize: '12px' }}>{i18n.get('辅助信息')}</div>
          </div>
        )
        rightButtons.unshift(btn)
      }
      if (arr.length > 0) {
        arr.push(billVersionBtn())
      }
    }

    // 根据条件判断是否添加分享按钮
    this.pushShareBtn(arr)

    return (
      <BillInfoButtons
        flowId={flowId?.id}
        scene={'APPROVER'}
        needConfigButton={billAdditionalInfo?.needConfigButton}
        flowActionMap={this.fnGetActionMap()}
        getFlowActionMap={this.lazyGetActionMapByButtons}
      >
        <ActionsPanel
          hiddenLeftButton={type === 'permit'}
          rightButtons={rightButtons}
          leftButtons={arr}
          buttonAction={this._buttonsClick}
        />
      </BillInfoButtons>
    )
  }

  judegeBillStatus(flowId) {
    const { logs = [], active } = flowId

    const fnModal = () => {
      Dialog.alert({
        title: i18n.get('操作失败'),
        content: active ? i18n.get('该单据已被撤回，无法执行此操作') : i18n.get('该单据已被删除，无法执行此操作'),
        confirmText: i18n.get('确定'),
        onConfirm: () => api.go(-1)
      })
    }
    if (!active) {
      // 单据已经被删除
      fnModal()
      return false
    }
    if (logs.length > 0) {
      const lastLog = logs[logs.length - 1]
      if (lastLog.action === 'freeflow.retract') {
        //撤回
        fnModal()
        return false
      }
    }
    return true
  }

  renderDetail() {
    let { me_info, isModifyBill, backlog, submitterLoans } = this.props
    let { data, noticeList, flowRiskList, invoiceRiskData, arrangeLayout } = this.state
    if (!data) {
      return <SkeletonComponent bodyLength={2} length={4} showHead={true} />
    }
    let {
      type,
      flowId: { logs, form, id, writtenOff, plan, ownerId, flowRulePerformLogs = {}, state, loanManualRepayment = [] }
    } = data
    let errorMessages = (flowRulePerformLogs?.results || []).filter(e => e.type !== 'calculate')
    let tags = { submitterId: { ownerId } }
    //加急
    let submitNodes = logs?.filter(v => v.action === 'freeflow.submit').map(v => v.attributes) || []
    let { isUrgent, urgentReason, sensitiveContent, sensitiveAttachment } = submitNodes[submitNodes.length - 1] || {}
    //费用标准notice 暂时不能删 兼容老标准
    let notices = (errorMessages || []).map(o => ({ content: o.errorMsg })).concat(noticeList)
    data.flowId.countDownDuration = data.countDownDuration
    data.flowId.autoApproveType = data.autoApproveType
    const showSensitive = showSensitiveContent(data.flowId, me_info?.staff?.id) && STATE_LIST.indexOf(state) < 0
    const arrangeLayoutList = arrangeLayout.length ? arrangeLayout : this.defaultArrangeLayout
    let isHideWait = false
    if (data.flowId?.form?.specificationId?.appId === '320-corporateReceipt') {
      // 收款管理下的发票组件要补充发票按钮要隐藏
      isHideWait = true
    }

    return (
      <div className="detail-content-wrapper scrollable inertial-rolling h-100-percent h-100p w-100p">
        <Container
          name="@bill:BillInfoReadonly"
          errorMessages={errorMessages}
          bus={this.bus}
          type={type}
          value={form}
          plan={plan}
          loanManualRepayment={loanManualRepayment}
          submitterLoans={submitterLoans}
          flowId={id}
          ownerId={ownerId}
          tags={tags}
          writtenOff={writtenOff}
          flowRulePerformLogs={flowRulePerformLogs}
          showLoan={true}
          external={flowRiskList}
          billState={state}
          invoiceRiskData={invoiceRiskData}
          auto={data?.flowId?.form?.systemGeneration}
          remunerationData={backlog.flowId}
          isModifyBill={isModifyBill}
          arrangeLayout={arrangeLayoutList}
          isHideWait={isHideWait}
          renderRiskNotice={() => (
            <Fragment key="RiskNotice">
              {showSensitive && <WarningSensitive content={sensitiveContent} attachments={sensitiveAttachment} />}
              {/* <RiskNotice notices={notices} plan={plan} /> */}
              <RiskAlert
                bus={this.bus}
                billDetails={data}
                riskWarningV2={invoiceRiskData?.riskWarningV2}
                flowAllRiskType={invoiceRiskData || {}}
              />
              {isUrgent && <UrgentView urgentReason={urgentReason} />}
            </Fragment>
          )}
          renderLogsCardView={() => (
            <LogsCardView
              key="LogsCardView"
              dataSource={data.flowId}
              onLogMsgClick={this.handleGoToHistory}
              userInfo={me_info}
              isFromApproving
            />
          )}
          renderFlowNodeView={() => (
            <FlowNodeView key="FlowNodeView" value={data} isReadOnly={false} isSimpleMode={true} />
          )}
        />
      </div>
    )
  }

  renderBudgetTakeUp() {
    let {
      standardCurrency,
      backlog: {
        flowId: { id, form = {} }
      }
    } = this.props
    return (
      <div className="detail-budget-wrapper-new-home">
        <ReportListView
          bus={this.bus}
          flowId={id}
          form={form}
          standardCurrency={standardCurrency}
          handleGotoDetail={this.handleGotoDetail}
        />
      </div>
    )
  }

  renderExpress() {
    const { expressNums, backlog, params = {} } = this.props
    const canEditExpress = params.canEditExpress === 'null' ? false : params.canEditExpress
    return <BillExpress expressNums={expressNums} flow={backlog.flowId} canEditExpress={canEditExpress} />
  }

  render() {
    const {
      me_info,
      standardCurrency,
      expressPower,
      batchFlow_config,
      printPreviewUrl,
      KA_PREVIEW_PRINT_IN_MODAL
    } = this.props
    const {
      hasExpressNode,
      existBudgetOccupy,
      activeKey,
      isPaymentModalShow,
      isAutographTips,
      signChecked,
      isLoadingNext,
      noPermission
    } = this.state

    if (noPermission) {
      return <NoPermissionViewBillDetail />
    }

    if (isLoadingNext) {
      return (
        <div className="approve_next_loading">
          <SpinLoading text="加载中..." size="large" direction="vertical" />
        </div>
      )
    }

    const { symbol = '', scale = 2 } = standardCurrency
    const zeroMoney = thousandBitSeparator(new Big(0).toFixed(scale))
    const tabPanes = [{ tab: i18n.get('单据详情'), key: '1', children: this.renderDetail() }]

    // 审批预览页面，展示打印预览结果
    if (KA_PREVIEW_PRINT_IN_MODAL) {
      tabPanes.splice(1, 0, {
        tab: i18n.get('审批预览'),
        key: 'printPreview',
        children: <PrintPreview url={printPreviewUrl} />
      })
    }

    tabPanes.push({ tab: i18n.get('审批流程'), key: '2', children: this.renderFlowState() })

    if (existBudgetOccupy) {
      tabPanes.push({ tab: i18n.get('预算占用'), key: '3', children: this.renderBudgetTakeUp() })
    }

    if (expressPower && hasExpressNode) {
      tabPanes.push({ tab: i18n.get('寄送信息'), key: '4', children: this.renderExpress() })
    }

    return (
      <div className="approve-view-wrapper">
        <div className="layout-content-wrapper">
          <ETabs
            defaultActiveKey="1"
            animated={false}
            dataSource={tabPanes}
            activeKey={activeKey}
            pageSize={3}
            onChange={this.handleTabClick}
          />
          {this.renderDetailActions()}
        </div>
        <Modal
          transparent
          maskClosable={false}
          visible={isPaymentModalShow}
          footer={[
            { text: i18n.get('取消'), onPress: this.handleCancelZeroPay },
            { text: i18n.get('确定'), onPress: this.handleZeroPay }
          ]}
        >
          <span style={{ color: '#54595b', fontSize: 15, fontWeight: 500 }}>
            {i18n.get('支付金额：{__k0}{__k1}', { __k0: symbol, __k1: zeroMoney })}
          </span>
          <br />
          <span style={{ color: '#b1b9bd', fontSize: 14, marginTop: 5 }}>{i18n.get('无需支付，确定即完成')}</span>
          <br />
          {isAutographTips && (
            <span style={{ color: '#FC3842', fontSize: 12, marginTop: 4 }}>
              {i18n.get('您还没有上传您的签名影像，请到我的-我的签名中进行维护')}
            </span>
          )}
          {me_info?.staff?.autograph && (
            <div style={{ display: 'inline-block' }}>
              <Checkbox
                checked={signChecked}
                disabled={batchFlow_config?.mustBeUsedSignature}
                onChange={this.handleSignChange}
              >
                <span>{i18n.get('使用签名影像')}</span>
              </Checkbox>
            </div>
          )}
        </Modal>
      </div>
    )
  }
}
