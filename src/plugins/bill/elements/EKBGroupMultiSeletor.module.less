@import '~@ekuaibao/eui-styles/less/token-mobile.less';

.filter-multi-select-wrapper {
  z-index: 2;
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;

  :global {
    .ekb-drop-down-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: -1;
      opacity: 0;
      transition: opacity ease .2s;
      background: rgba(20, 34, 52, 0.24);
      box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);

      &.show {
        opacity: 1;
      }
    }

    .ekb-drop-down {
      position: absolute;
      top: -100%;
      left: 0;
      width: 100%;
      min-height: 240px;
      background: white;
      transition: all ease .2s;

      .ekb-drop-down-content {
        overflow: scroll;
        max-height: 750px;
        padding-left: @space-6;
        padding-right: @space-6;

        .ekb-drop-down-group {
          margin-top: 32px;
          &:first-child {
            margin-top: 0;
          }
        }

        .ekb-drop-down-groupName {
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: var(--eui-text-title);
          font: var(--eui-font-head-b1);
        }

        .group-select-all {
          display: flex;
          align-items: center;
          color: var(--eui-text-title);
          font: var(--eui-font-head-r1);
          gap: 16px;
          margin-bottom: 16px;
        }

        .group-select-items {
          display: flex;
          flex-wrap: wrap;
          row-gap: 24px;
          column-gap: 16px;
        }

        .ekb-drop-down-item {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 68px;
          border-radius: 8px;
          background: var(--eui-bg-body-overlay);
          color: var(--eui-text-title);
          font: var(--eui-font-body-r1);
          border: 2px solid transparent;
          flex: 0 0 calc(50% - 8px);

          &.all-status-item {
            background: rgba(37, 85, 255, 0.06);
            border: 1px solid rgba(37, 85, 255, 0.2);
          }

          .checkbox-container {
            margin-right: 12px;
            display: flex;
            align-items: center;

            .option-checkbox {
              width: 16px;
              height: 16px;
              margin: 0;
              cursor: pointer;
              accent-color: var(--eui-primary-pri-500);
            }
          }

          &.selected {
            background: #ffffff;
            border: var(--eui-primary-pri-500) 2px solid;

            &.all-status-item {
              background: var(--eui-primary-pri-500);
              color: white;
              border: var(--eui-primary-pri-500) 2px solid;
            }
          }

          .item-selected-tip {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 32px;
            height: 32px;
            border-bottom-right-radius: @radius-2;
            border-width: 16px;
            border-style: solid;
            border-color: #ffffff var(--eui-primary-pri-500) var(--eui-primary-pri-500) #ffffff;
            font-size: @icon-size-3;

            .icon {
              position: absolute;
              color: #ffffff;
            }
          }
        }
      }

      &.show {
        top: 0;
      }
    }

    .ekb-drop-down-footer {
      display: flex;
      justify-content: space-between;
      margin: 32px;
      font: var(--eui-font-head-r1);

      .btn {
        width: 160px;
        margin-right: 16px;
        color: var(--eui-text-title);
      }

      .primary-btn {
        flex: 1;
        color: var(--eui-static-white);
      }
    }
  }
}
