import React, { useState, useEffect } from 'react'
import { app } from '@ekuaibao/whispered'
import { Button, Checkbox } from '@hose/eui-mobile'
import { cloneDeep } from 'lodash'

import styles from './EKBGroupMultiSeletor.module.less'

const EkbIcon: React.FC<{name: string}> = app.require('@elements/ekbIcon')

interface MenuProps {
  type: string
  label: string
  children?: MenuProps[]
}

interface Props {
  menuList: MenuProps[]
  isOpen: boolean
  fnCancel: () => void
  activeTypes: MenuProps[]
  defaultActiveTypes: MenuProps[]
  fnChangeType: (param: MenuProps[]) => void
  downContentClassName?: string
  style: any
}

const EKBGroupMultiSeletor: React.FC<Props> = (props: Props) => {
  const {
    isOpen,
    fnCancel,
    menuList,
    defaultActiveTypes,
    activeTypes,
    fnChangeType,
    downContentClassName = '',
    style
  } = props

  const [selectTypes, setSelectTypes] = useState<MenuProps[]>(activeTypes || defaultActiveTypes)
  const [isShowMask, setIsShowMask] = useState(false)
  const [isShowContent, setIsShowContent] = useState(false)

  const userInfoId = app.getState()['@common'].me_info?.staff?.id + '-filter' || 'bill-select-filter'

  useEffect(() => {
    if (isOpen) {
      setIsShowMask(isOpen)
      setTimeout(() => setIsShowContent(isOpen), 300)
    } else {
      setIsShowContent(isOpen)
      setTimeout(() => setIsShowMask(isOpen), 300)
    }
  }, [isOpen])

  const handleReset = () => {
    localStorage.removeItem(userInfoId)
    setSelectTypes(defaultActiveTypes)
  }

  const handleOK = () => {
    fnChangeType(selectTypes)
  }

  // 处理单个选项的点击
  const handleClickMenu = (menuType: string, subMenuType: string) => {
    const newSelectTypes = cloneDeep(selectTypes)
    let selectMenu = newSelectTypes.find(el => el.type === menuType)
    const menu = menuList.find(el => el.type === menuType)
    const existingIndex = selectMenu.children.findIndex(el => el.type === subMenuType)

    if (existingIndex > -1) {
      // 已选中，则移除
      selectMenu.children.splice(existingIndex, 1)
    } else {
      // 未选中，有两种情况
      // 1. 全选，则先将选项选中，在移除当前项
      // 2. 未选中，选中当前项
      const isGroupAllSelected = selectMenu.children.some((item: MenuProps) => (item.type === menu.children[0].type))
      if (isGroupAllSelected) {
        selectMenu.children = menu.children.slice(1).filter((item) => item.type !== subMenuType)
      } else {
        const subMenu = menu.children.find(el => el.type === subMenuType)
        if (subMenu) {
          selectMenu.children.push(subMenu)
          // 如果是全选，则换成全选项
          if (selectMenu.children.length === menu.children.length - 1) {
            selectMenu.children = [menu.children[0]]
          }
        }
      }
    }

    setSelectTypes(newSelectTypes)
  }

    // 处理分组全选/取消全选
  const handleSelectGroupAll = (menuType: string) => {
    const newSelectTypes = cloneDeep(selectTypes)
    const selectMenu = newSelectTypes.find(el => el.type === menuType)
    const menu = menuList.find(el => el.type === menuType)

    const allTypeItem = menu.children[0]
    const isGroupAllSelected = selectMenu?.children?.some((item: MenuProps) => (item.type === allTypeItem.type))

    if (isGroupAllSelected) {
      selectMenu.children = []
    } else {
      selectMenu.children = [menu.children[0]]
    }

    setSelectTypes(newSelectTypes)
  }


  const existMenuSelectNoDimension = selectTypes.some((menu: MenuProps) => (menu.children?.length === 0))
  const maskClassName = isShowContent ? 'ekb-drop-down-mask show' : 'ekb-drop-down-mask'
  const contentClassName = isShowContent ? 'ekb-drop-down show' : 'ekb-drop-down'

  if (!isShowMask && !isShowContent) {
    return null
  }

  return (
    <div className={styles['filter-multi-select-wrapper']} style={style}>
      <div className={maskClassName} onClick={() => fnCancel && fnCancel()} />
      <div className={contentClassName}>
        <div className={`ekb-drop-down-content inertial-rolling ${downContentClassName}`}>
          {/* 分组选项 */}
          {menuList.map((menu) => {
            if (!menu?.children?.length) {
              return null
            }

            const selectItems = selectTypes.find(el => el.type === menu.type)?.children || []
            const _allTypeItem = menu.children[0]
            const isGroupAllSelected = selectItems.some((item: MenuProps) => (item.type === _allTypeItem.type))
            const subMenu = menu.children.slice(1).map((el) => {
              const isSelected = selectItems.some((item: MenuProps) => (item.type === el.type || item.type === _allTypeItem.type))
              const itemClassName = isSelected ? 'ekb-drop-down-item selected' : 'ekb-drop-down-item'

              return (
                <div key={el.type} className={itemClassName} onClick={() => handleClickMenu(menu.type, el.type)}>
                  <span>{el.label}</span>
                  {isSelected && (
                    <div className="item-selected-tip">
                      <EkbIcon name="#EDico-check-default"/>
                    </div>
                  )}
                </div>
              )
            })

            return (
              <div key={menu.type} className='ekb-drop-down-group'>
                <div className='ekb-drop-down-groupName'>
                  <span>{menu.label}</span>
                </div>
                <div className="group-select-all" onClick={() => handleSelectGroupAll(menu.type)}>
                  <Checkbox
                    checked={isGroupAllSelected}
                  />
                  <span>{_allTypeItem.label}</span>
                </div>
                <div className='group-select-items'>{subMenu}</div>
              </div>
            )
          })}
        </div>
        <div className='ekb-drop-down-footer'>
          <Button className="btn" size='large' category='secondary' onClick={handleReset}>{i18n.get('重置')}</Button>
          <Button className="primary-btn" category='primary' size='large' disabled={existMenuSelectNoDimension} onClick={handleOK}>{i18n.get('确定')}</Button>
        </div>
      </div>
    </div>
  )
}

export default EKBGroupMultiSeletor
