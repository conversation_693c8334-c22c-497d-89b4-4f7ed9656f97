/**************************************************
 * Created by nanyuantingfeng on 11/07/2017 12:08.
 **************************************************/
import { Resource } from '@ekuaibao/fetch'
import key from './key'
import { QuerySelect } from 'ekbc-query-builder'
import { app as api } from '@ekuaibao/whispered'
import * as util from '../../lib/util'
import { delThirdOrders } from './utils/formatUtil'
import { get } from 'lodash'
import { mutiPayTrack } from './utils/billUtils'
import { fetchAttachment, fetchInvoice } from '../../lib/attachment-fetch'
import { saveErrorMessageLog } from '../../lib/dataflux/billLogEvent'
import { isString } from '@ekuaibao/helpers'
import { getBoolVariation } from '../../lib/featbit'
const specifications = new Resource('/api/form/v1/specificationVersions')
const flows = new Resource('/api/flow/v1/flows')
const flowInfo = new Resource('/api/flow/v1/resource')
const favProjects = new Resource('/api/v1/basedata/projects')
const plans = new Resource('/api/v1/flow/plans')
const loanpackageAction = new Resource('/api/v1/loan/loanInfo')
const apply = new Resource('/api/form/v2/requisition')
const requisitionInfo = new Resource('/api/form/v3/requisition/info')
const validation = new Resource('/api/v2/invoice/validation')
const rule = new Resource('/api/rpc/v1/rule')
const customizeQuery = new Resource('/api/v2/datalink/customizeQueryRule')
const express = new Resource('/api/expresses/v2')
const feeTypeImport = new Resource('/api/flow/v2/feeTypeImportRule')
const calculateField = new Resource('/api/form/v2')
const dimensionItems = new Resource('/api/v1/basedata/dimensionItems')
const department = new Resource('/api/v1/organization/departments')
const enumItem = new Resource('/api/v1/basedata/enumItems')
const dataLink = new Resource('/api/v2/datalink')
const fieldMapping = new Resource('/api/v1/mapping/fieldMapping')
const VPhotoDetail = new Resource('/api/v2/vphoto/flowid')
const VPhotoVerify = new Resource('/api/v2/vphoto')
const isEbot = new Resource('/api/flow/v2/flows/isEbot')
const repay = new Resource('/api/v1/loan/repayment')
const current = new Resource('/api/flow/v2/flows/current')
const backlogs = new Resource('/api/flow/v2/backlogs')
const v2flows = new Resource('/api/flow/v2/flows')
const print = new Resource('/api/v1/print')
const dataLinkV2 = new Resource('/api/v1/datalink')
const searchDataLink = new Resource('/api/v1/datalink/searchDataLink')
const getDataLinkV2 = new Resource('/api/v2/datalink/searchDataLinks')
const searchDataLinkV2 = new Resource('/api/v2/datalink/detail')
const dependence = new Resource('/api/v1/record/recordLink/search')
const dependenceAction = new Resource('/api/v1/record/recordLink')
const supplementIdentity = new Resource('/api/v2/pay/payeeInfo/check')
const yeeGoOrder = new Resource('/api/flow/v2/check/yeeGoOrder')
const credit = new Resource('/api/credit/v2')
const travelManagement = new Resource('/api/tpp/v2/travelManagement')
const withNotes = new Resource('/api/withNotes/v2/batch/update/withNotes')
const QuickExpense = new Resource('/api/quickexpense/v1/batch/update/detail')
const meta = new Resource('/api/v1/flow')
const budgetList = new Resource('/api/v1/budget/report/flow')
const configCheck = new Resource('/api/pay/v2/payeeinfoconfigs/check')
const riskwarning = new Resource('/api/flow/v2/riskwarning/coalition')
const singleInvoiceRiskWarning = new Resource('/api/flow/v3/riskwarning/invoice/riskWarning') // get为获取风险，post为记录风险
const autoAssociate = new Resource('/api/v1/requisition/autoAssociate/detailcr')
const originalOrderNo = new Resource('/api/tpp/v2/travelManagement/order/all')
const autoGenerationFeeDetail = new Resource('/api/flow/v1/autoGenerationFeeDetail')
const invoiceReason = new Resource('/api/v1/invoiceRiskExplain')
const extensionCenterConfigRes = new Resource('/api/v1/flow/extensionCenterConfig')
const corporationList = new Resource('/api/v2/invoice/unify/corporation/list')

const pageSize = 100
const billVersions = new Resource('/api/flow/v2/flowVersioneds')
const widgetConfig = new Resource('/api/v1/flow/smallComponentConfig')
const unAuthorizedApprove = new Resource(' /api/flow/v2/check/approverRepeatedByUnauthorized')
const payee = new Resource('/api/pay/v2/accounts')
const delegate = new Resource('/api/v1/organization/delegate')
const flowLink = new Resource('/api/flow/v1/flowLink')
const calculateRecordLink = new Resource('/api/v2/privateCar/calculateRecordLink')
const nullifyRule = new Resource('/api/flow/v1/nullifyRule')
const detailFLowRelation = new Resource('/api/v1/detailFLowRelation')
const orderConfirm = new Resource('/api/tpp/v2/travelOrderConfirm')
const baiwangPreviewUrl = new Resource('/api/v2/datalink/baiwang/url/encryption') // 百旺预览
const repaymentAccountList = new Resource('/api/v2/pay/accounts/manual/repayment/account/list')
const backlogsApprove = new Resource('/api/backlogs/v2/approve')
const flowButtonAction = new Resource('/api/flow/v2/button')
const feishuChat = new Resource('/api/extend/feishu')
const aiLink = new Resource('/api/tpp/v2/ai')
const mallLink = new Resource('/api/tpp/v2/hoseMall')
const payV3Action = new Resource('/api/pay/v3')
const aiAgent = new Resource('/api/engine/hab/agent')

export function addOrCancelCollect(param = {}) {
  const { id, enable } = param
  return {
    type: key.ADDORCANCELCOLLECT,
    payload: payee.PUT(`/$${id}/$${enable}`)
  }
}

export function judgeShowPrintInvoice(params = {}) {
  return flows.GET('/showPrintInvoice', params)
}

export function getTravelDateRange(params = {}) {
  return travelManagement.POST('/getTravelDateRange', params)
}

export function setSyncTripManually(flowId) {
  return {
    type: key.SET_SYNCTRIPMANUALLY,
    payload: travelManagement.POST('/syncTrip/manually', flowId)
  }
}

export function getSyncTripResult(flowId) {
  return {
    type: key.GET_SYNCTRIP_RESULT,
    payload: travelManagement.POST('/syncTrip/result', flowId)
  }
}

export function getSyncTripValid(param) {
  return {
    type: key.GET_SYNC_VALID,
    payload: travelManagement.POST('/syncTrip/valid', param)
  }
}

//票据连号风险需要填写原因
export function getInvoiceReason(param) {
  return {
    type: key.GET_INVOICE_REASON,
    payload: invoiceReason.GET('/$id', param)
  }
}

export function saveInvoiceReasonBatch(params) {
  return invoiceReason.POST('/saveBatch', params)
}

//票据连号风险需要填写原因
export function getInvoiceIdAndReason(param) {
  return {
    type: key.GET_INVOICE_REASON,
    payload: invoiceReason.GET('/ticket/$id', param)
  }
}

export function saveInvoiceReason(params) {
  return invoiceReason.POST('/save', params)
}

export function getTravelManagementDetail(params) {
  return travelManagement.GET('/selectTravelDetail', params)
}
/**
 * 判断借款是否被取消共享
 * @param params : {id: 借款id}
 */
export function shareIsCancel(params) {
  return loanpackageAction.POST('/isCancel/$id', params)
}
// 订单详情退改签allCard
export function getOriginalOrderNo(params) {
  return originalOrderNo.GET('/$dataLinkId', params)
}

// 申请事项/行程规划list
export function getTripPlanningList(param) {
  return apply.GET('/$id/travels', param)
}

//获取实体模板
export function getDataLinkEditTemplate(params) {
  let joinReferenceData = `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
  return {
    type: key.GET_DATA_LINK_EDIT,
    payload: dataLink.GET('/entityEdit/entityForm/$id/$type', { ...params, join: joinReferenceData })
  }
}

// 单据表格审批，储存预览url
export function setPrintPreviewUrl(url) {
  return {
    type: key.SET_PRINT_PREVIEW_URL,
    url
  }
}

/**
 * 获取实体数据及配置
 * @template {T}
 *
 * @param id
 * @param {TemplateKind} type
 * @return {Promise<TemplateDataResponse<T>>}
 */
export function getEntityWithTemplate(id, type) {
  return searchDataLinkV2.GET('/$id/$type', { id, type })
}

export function getChildrenPrimary(datalinkId, ledgerConfigId) {
  return dataLinkV2.GET('/childrenPrimary/$datalinkId', { datalinkId, ledgerConfigId })
}

export function printJob(data, done) {
  const { flowId, callback } = data
  return {
    type: key.POST_PRINTJOB,
    flowId,
    callback,
    payload: print.GET('/async/pdf/[flowId]', data),
    done
  }
}

export function getSpecifications(type = 'expense') {
  let data = { type }
  return {
    type: key.GET_SPECIFICATION_LIST,
    specificationType: type,
    payload: specifications.GET('/actives', data)
  }
}

export function getApplyByExpense(data = {}, done) {
  let params = {
    ...data,
    join$1: `expenseInfoList.id,flow,/flow/v1/flows`,
    join$2: 'specificationId,specification,/form/v1/specificationVersions',
    join$3: `changeLogs.operatorId,operatorId,/v1/organization/staffs`,
    join$4: `changeLogs.fromStaff,fromStaff,/v1/organization/staffs`,
    join$5: `changeLogs.toStaffs,toStaffs,/v1/organization/staffs`,
    join$6: `ownerId,ownerId,/v1/organization/staffs`
  }
  return {
    type: key.GET_APPLY_BY_EXPENSE,
    payload: apply.GET('/byExpense/$id', params),
    done
  }
}

export function getApplyByExpenseWithOrder(data = {}, done) {
  const { loadMore = false, fromSort = false, filterStr, query, ...othersData } = data
  if (loadMore) {
    return {
      type: key.LOAD_MORE_APPLY_BY_EXPENSE,
      payload: { filterStr },
      done
    }
  }
  let params = {
    ...othersData
  }
  return {
    type: key.GET_APPLY_BY_EXPENSE,
    payload: apply.POST('/byExpenseRelated/$id', query || {}, params).then(resp => ({
      data: resp,
      filterStr
    })),
    done
  }
}

export function getApplyEventById(id, done) {
  const applyEventJoin = {
    join: `specificationId,specificationId,/form/v1/specificationVersions`,
    join$1: `changeLogs.operatorId,operatorId,/v1/organization/staffs?select=id,name,enName,avatar,code,email,cellphone,note`,
    join$2: `changeLogs.fromStaff,fromStaff,/v1/organization/staffs?select=id,name,enName,code,email,cellphone,note`,
    join$3: `changeLogs.toStaffs,toStaffs,/v1/organization/staffs?select=id,name,enName,code,email,cellphone,note`,
    join$4: `ownerId,ownerId,/v1/organization/staffs?select=id,name,enName,code,email,cellphone,note`
  }
  let params = {
    id: id,
    ...applyEventJoin
  }
  return {
    type: key.GET_APPLY_EVENT,
    payload: apply.GET('/$id', params),
    done
  }
}

export function getCurrentRequisitionInfo(visibleSpecId) {
  return {
    type: key.GET_CURRENT_REQUISITION_INFO,
    visibleSpecId
  }
}

export function getExpenseLink(data, done) {
  let params = {
    ...data,
    join: `id,specificationVersion,/form/v1/specificationVersions`
  }
  return {
    type: key.GET_EXPENSE_LINK,
    payload: apply.GET('/expenseLink/$id', params),
    done
  }
}

export function editFlow(id, data, type) {
  !type && util.showLoading()
  data = { name: 'freeflow.edit', ...data }
  if (data.form && data.form.details && data.form.details.length > 0) {
    data.form.details = delThirdOrders(data.form.details, data?.form?.alterFlag)
  }
  return {
    type: type ? key.SAVE_AND_SUBMIT : key.EDIT_FLOW,
    payload: flows.POST(id ? '/$id' : '', data, { id }).then(data => {
      util.hideLoading()

      return data
    }),
    done(_, action) {
      const { done, ...others } = action
      saveErrorMessageLog({ flowId: id, flowValue: data, actionMessage: others })
    }
  }
}

export function submitFlow({ id, data, isBillCopiedValue, isModifyBill }) {
  data = { name: 'freeflow.submit', ...data }
  const details = get(data, 'form.details', [])
  if (details.length > 0) {
    data.form.details = delThirdOrders(details)
  }
  const expenseLink = data.form && data.form.expenseLink
  const linkRequisitionInfo = data.form && data.form.linkRequisitionInfo
  return {
    type: key.SUBMIT_FLOW,
    isModifyBill: data.name === 'freeflow.editApproving',
    billType: data.formType,
    expenseLink,
    linkRequisitionInfo,
    isBillCopiedValue,
    payload: getMessageCode()
      .then(pushMessageCode => flows.POST(id ? '/$id' : '', data, { id, pushMessageCode }))
      .catch(e => Promise.reject(e)),
    done(_, action) {
      if (!action.error) {
        if (isModifyBill) return
        //多收款人埋点
        mutiPayTrack(data)
        window.IS_KINGOA && dealKingOA()
      }
    }
  }
}
function dealKingOA() {
  try {
    if (window.location.href.indexOf('pageType=new') > -1) {
      return api.invokeService('@layout:close:window')
    }
  } catch (e) {
    console.log('KING OA : close window...' + e)
  }
}
export function deleteFlow(id) {
  util.showLoading()
  const data = { name: 'freeflow.delete', id }
  return {
    type: key.DELETE_FLOW,
    payload: flows.POST('/$id', data).then(data => {
      util.hideLoading()
      return data
    }),
    done(_, action) {
      if (!action.error) {
      }
    }
  }
}

export function retractFlow(id) {
  util.showLoading()
  const data = { name: 'freeflow.retract', id }
  return {
    type: key.RETRACT_FLOW,
    payload: flows.POST('/$id', data).then(data => {
      util.hideLoading()
      return data
    })
  }
}

/**
 * 撤回审批
 * */
export function approveWithdraw(id, rejectTo, nextId) {
  util.showLoading()
  const data = { name: 'freeflow.withdraw', id, rejectTo, nextId }
  return {
    type: key.WITHDRAWN_FLOW,
    payload: getMessageCode().then(messageCode => {
      return backlogs.POST('/withdraw/$id', data, { messageCode }).then(data => {
        util.hideLoading()
        return data
      })
    })
  }
}
export function getFlowDetailInfo(id) {
  const selectQuery = new QuerySelect().filterBy(`id=="${decodeURIComponent(id)}"`).value()
  const data = { id, join: 'form.specificationId,specificationId,/form/v1/specificationVersions' }
  return {
    type: key.GET_FLOW,
    payload: flows.POST('/flowId/$id', selectQuery, data).then(data => {
      return data
    })
  }
}
export function getFlowDetailInfoLite(id) {
  const { selectEntityLite, joinParamsLite } = api.require('@common/join/bill.join')
  let query = new QuerySelect()
    .filterBy(`id=="${decodeURIComponent(id)}"`)
    .select(`${selectEntityLite()}`)
    .value()
  let params = {
    id,
    ...joinParamsLite()
  }
  return {
    type: key.GET_FLOW_LITE,
    payload: flows.POST('/flowId/$id', query, params).then(data => {
      return data
    })
  }
}
export function getFlowInfoTripPlatform(id) {
  const data = [
    {
      type: 'ability',
      abilityType: 'tripPlatform'
    }
  ]
  return {
    type: key.GET_FLOW_TRIPPLATFORM,
    payload: flowInfo.POST(`/$${id}`, data).then(data => {
      return data
    })
  }
}

export function commentFlow(param) {
  util.showLoading()
  const data = { name: 'freeflow.comment', ...param }
  return {
    type: key.COMMENT_FLOW,
    payload: getMessageCode()
      .then(messageCode => flows.POST('/comment/$id', data, { messageCode }))
      .then(data => {
        util.hideLoading()
        return data
      })
      .catch(e => Promise.reject(e))
  }
}

export function getFavProjects(originalId, done) {
  return {
    type: key.FAV_PROJECTS,
    payload: favProjects.GET('/recent', { originalId: originalId }),
    done
  }
}

export function computedFlowPlan(data, alterFlag = false) {
  util.showLoading()
  let params = {
    join: 'nodes.approverId,staff,/v1/organization/staffs',
    join$1: 'nodes.counterSigners.signerId,signerId,/v1/organization/staffs',
    join$2: 'nodes.counterSignersCandidate.signerId,signerId,/v1/organization/staffs'
  }
  if (alterFlag) {
    data.form.alterFlag = alterFlag
  }
  return {
    type: key.COMPUTED_FLOW_PLAN,
    payload: plans.POST('/computed', data, params).then(data => {
      util.hideLoading()
      return data
    })
  }
}

export function listLoanPackage(param, done) {
  return {
    type: key.LOANPACKAGE_LIST,
    payload: loanpackageAction.GET('/mine/byState', param),
    done
  }
}

export function checkLoanPackageExist(loanIds, submitterId, done) {
  const ids = loanIds.join(',')
  return {
    type: key.CHECK_LOAN_PACKAGE_EXIST,
    payload: loanpackageAction.GET(`/checkLoanInfo/[ids]?submitterId=${submitterId}`, { ids }),
    done
  }
}

export function listLoanPackageBySubmitter(param) {
  let departments = 'departments,departmentArr,/v1/organization/departments?select=id,name'
  let { state = 'REPAID', start = 0, count = 999, filters = '', flowId, id, method = 'get' } = param || {}
  const joins = {
    join: `repaymentRecords.casherId,staff,/v1/organization/staffs?join=${departments}`,
    join$1: `repaymentRecords.accountId,accountCompany,/pay/v1/accounts`,
    join$2: `repaymentRecords.ownerId,ownerId,/v1/organization/staffs`,
    join$3: `repaymentRecords.shiftStaffId,shiftStaffId,/v1/organization/staffs`
  }
  if (method === 'get') {
    return loanpackageAction.GET('/byOwnerIdAndState/$id', { ...param, ...joins })
  }

  let query = new QuerySelect()
    .limit(start, count)
    .filterBy(`state.in("${state}")`)
    .filterBy(filters)
    .value()

  flowId = flowId || undefined

  return loanpackageAction.POST('/byOwnerIdAndState/$id/search', { id, flowId, ...query }, joins)
}

export function setRequisitionInfo(visibleIds, expenseLink) {
  return {
    type: key.SET_REQUISITION_INFO,
    visibleIds,
    expenseLink
  }
}

export function getInvoiceStateById(masterId, staffId) {
  return {
    type: key.GET_INVOICE_STATE_BYID,
    payload: validation.GET('/visibility', { masterId, staffId })
  }
}

function getMessageCode() {
  return api.invokeService('@layout:dd:message:code')
}

//单据催办
export function billRemind(flowId, taskId) {
  return {
    type: key.BILL_REMIND,
    payload: getMessageCode().then(messageCode => {
      return flows.PUT('/remind/$id', { id: flowId }, { messageCode, taskId: taskId })
    })
  }
}

export function getBatchAutoCalResult(params) {
  return {
    type: key.GET_BATCH_AUTO_CAL_RESULT,
    payload: rule.POST('/calculate', params)
  }
}
export function getCustomizeCalResult(params) {
  return {
    type: key.GET_CUSTOMIZE_CAL_RESULT,
    payload: customizeQuery.POST('/customizeCalculate', params)
  }
}

export function getAutoCalFields(params) {
  return {
    type: key.GET_AUTO_CAL_FIELDS,
    payload: calculateField.GET('/calculateFields/staffId/$submitterId/specId/$specificationId', { ...params })
  }
}

export function getDimensionItemsById(ids) {
  return {
    type: key.GET_DIMENSION_BY_ID,
    payload: dimensionItems.GET('/[ids]', { ids })
  }
}

export function getDepartmentById(id) {
  return {
    type: key.GET_DEPARTMENT_BY_ID,
    payload: department.GET('/$id', { id })
  }
}

export function getEnumItemById(ids) {
  return {
    type: key.GET_ENUMITEM_BY_ID,
    payload: enumItem.GET('/[ids]', { ids })
  }
}

export function getEntityDetailsById(entityId) {
  return {
    type: key.GET_ENTITY_VALUE_BY_ID,
    payload: dataLink.GET('/entity/$entityId', { entityId })
  }
}

export function getEntityFormById(entityId, behaviour) {
  let isInsert = false
  if (behaviour === 'INSERT' || behaviour === 'MORE') {
    isInsert = true
  }
  let joinReferenceData = `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
  return {
    type: key.GET_ENTITY_FORM_BY_ID,
    payload: dataLink.GET('/entityEdit/entityForm/$entityId/$isInsert', { entityId, isInsert, join: joinReferenceData })
  }
}

export function getObjectFormById(params) {
  return {
    type: key.GET_OBJECT_FORM_BY_ID,
    payload: dataLink.GET('/entityEdit/getEntityFormDetail/$flowId/$type', { ...params })
  }
}

export function getEntityValueListById(params) {
  let { count, start, isPrivateCar, submitterId } = params
  const flowId = decodeURIComponent(params?.flowId)
  let query
  if (count) {
    query = new QuerySelect()
      .filterBy((params && params.filterBy) || '')
      .filterBy(flowId ? `active==true || flowCounts.containsIgnoreCase("${flowId}")` : 'active==true')
      .select(`ownerId(...),...`)
      .limit(start, count)
  } else {
    query = new QuerySelect()
      .filterBy((params && params.filterBy) || '')
      .filterBy(flowId ? `active==true || flowCounts.containsIgnoreCase("${flowId}")` : 'active==true')
      .select(`ownerId(...),...`)
  }
  if (isPrivateCar) {
    query.orderBy('createTime', 'DESC')
  }
  query = query.value()
  let { entityId } = params
  return {
    type: key.GET_ENTITY_VALUE_LIST_BY_ID,
    payload: searchDataLink.POST('/$entityId', query, { entityId, flowId, submitterId })
  }
}

export function getEntityValueListV2(params) {
  // 私车公用 使用 web 端的接口 v2
  return {
    type: key.GET_ENTITY_VALUE_LIST_V2,
    payload: getDataLinkV2.POST('', params)
  }
}

export function getEntityDetailsList(params) {
  return {
    type: key.GET_ENTITY_LIST_BY_PLATFORM,
    payload: dataLink.POST('/entity/platformId/$id', params)
  }
}

export function getFieldMappingByRuleId(id) {
  return {
    type: key.GET_FIELD_MAPPING_BY_ID,
    payload: fieldMapping.GET('/$id', { id })
  }
}

export function getVphotoDetail(flowId) {
  return VPhotoDetail.GET('/$flowId', { flowId })
}

export function verifyVPhotoOrders(value) {
  return VPhotoVerify.GET('/$value', { value })
}

export function saveCurrentFlow(flow) {
  return {
    type: key.SAVE_CURRENT_FLOW,
    payload: flow
  }
}

//单据只读状态下，将当前单据相关的收款信息存在状态树里
export function saveCurrentPayees(payees) {
  return {
    type: key.SAVE_CURRENT_PAYEES,
    payload: payees
  }
}

export function updateExpressNums(data) {
  const { flowId, expressId } = data
  return {
    expressId,
    type: key.UPDATE_EXPRESS_NUMS,
    payload: express.GET(`/byflowid/$flowId`, { flowId })
  }
}

export function getExpressNums(flowId) {
  return {
    type: key.GET_EXPRESS_NUMS,
    payload: express.GET(`/byflowid/$flowId`, { flowId })
  }
}

export function getExpress(data) {
  return {
    type: key.GET_EXPRESS,
    expressId: data.expressId,
    payload: express.GET(`/track/$id/byflowid/$byflowid`, { id: data.expressId, byflowid: data.flowId })
  }
}

export function getFeetypeImportRuleById(id, type) {
  let join = {
    deviceType: 'MOBILE',
    formType: type,
    join:
      'list.dataLinkEntity.platformId,platformId,/v2/datalink/platform?join=icon.fileId,fileId,/v1/attachment/attachments'
  }
  return {
    type: key.GET_FEETYPE_IMPORT_RULE_BY_ID,
    payload: feeTypeImport.GET(`/menuList/${id}`, join)
  }
}

const joinReferenceData = () =>
  'components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform?join=icon.fileId,fileId,/v1/attachment/attachments'
const joinAssignmentRule = () => 'components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping'

function polifyZhiyuanIOS(newParams) {
  return feeTypeImport
    .POST(`/available/feeTypes`, newParams, {
      join: `expenseSpecificationId,expenseSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}`,
      join$1: `requisitionSpecificationId,requisitionSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}`,
      join$2: `receiptSpecificationId,receiptSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}`,
      join$3: `expenseSpecificationId,expenseSpecificationV1,/form/v1/specificationVersions?join=${joinAssignmentRule()}`,
      join$4: `requisitionSpecificationId,requisitionSpecificationV1,/form/v1/specificationVersions?join=${joinAssignmentRule()}`,
      join$5: `receiptSpecificationId,receiptSpecificationV1,/form/v1/specificationVersions?join=${joinAssignmentRule()}`
    })
    .then(data => {
      const items = data.items
      items?.forEach(el => {
        el.expenseSpecification?.components?.forEach((c, idx) => {
          if (c?.assignmentRule && isString(c.assignmentRule)) {
            c.assignmentRule = el.expenseSpecificationV1?.components?.[idx]?.assignmentRule
          }
        })
        el.requisitionSpecification?.components?.forEach((c, idx) => {
          if (c?.assignmentRule && isString(c.assignmentRule)) {
            c.assignmentRule = el.requisitionSpecificationV1?.components?.[idx]?.assignmentRule
          }
        })
        el.receiptSpecification?.components?.forEach((c, idx) => {
          if (c?.assignmentRule && isString(c.assignmentRule)) {
            c.assignmentRule = el.receiptSpecificationV1?.components?.[idx]?.assignmentRule
          }
        })
        delete el.expenseSpecificationV1
        delete el.requisitionSpecificationV1
        delete el.receiptSpecificationV1
      })
      return data
    })
}
export function getRuleDataLinkFeetype(params) {
  const { type } = params
  const newParams = { ...params, type: type === 'reimbursement' ? 'expense' : type } //报账单的时候取报销单类型字段
  return {
    type: key.GET_VISIBLE_DATALINK_FEETYPE,
    payload:
      window.isIOS && window.isSeeyon
        ? polifyZhiyuanIOS(newParams)
        : feeTypeImport.POST(`/available/feeTypes`, newParams, {
            join: `expenseSpecificationId,expenseSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}&join=${joinAssignmentRule()}`,
            join$1: `requisitionSpecificationId,requisitionSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}&join=${joinAssignmentRule()}`,
            join$2: `receiptSpecificationId,receiptSpecification,/form/v1/specificationVersions?join=${joinReferenceData()}&join=${joinAssignmentRule()}`
          })
  }
}

export function getFlowSubmitCheckState(flowId) {
  return {
    type: key.GET_FLOW_SUBMIT_CHECK_STATE,
    payload: current.GET(`/state/$flowId`, { flowId }, null, { skipCache: true })
  }
}

export function getCurrentBackLog(flowId) {
  return {
    type: key.GET_CURRENT_BACKLOG,
    payload: backlogs.GET(`/current/$flowId`, { flowId })
  }
}

export function getIsEbotShift(params) {
  const { flowId, form } = params
  return {
    type: key.GET_ISEBOT_SHIFT,
    payload: isEbot.POST(`/shift/$flowId`, form, { flowId })
  }
}

export function saveCopiedValue(value) {
  // 复制单据不能带费用明细序号，序号应由后端生成
  if (value?.details?.length) {
    value.details = value.details?.map(detail => {
      delete detail.feeTypeForm.detailNo
      return detail
    })
  }
  return {
    type: key.SAVE_COPIED_VALUE,
    payload: value
  }
}

export function getLedgerPlanedById(dataLinkId) {
  return {
    type: key.GET_LEDGER_PLANED_BYID,
    payload: dataLinkV2.GET(`/$dataLinkId/other/info`, dataLinkId)
  }
}

function parseParams(params) {
  const { ledgerConfigId, statisticsSource, start, count, sort = true } = params
  let query = new QuerySelect()
    .filterBy('active==true')
    .filterBy(`ledgerConfigId=="${ledgerConfigId}"`)
    .orderBy('updateTime', sort ? 'ASC' : 'DESC')
    .limit(start, count)
  if (statisticsSource === 'MASTER') {
    return {
      query: query
        .select(`flowId(form(submitterId(id,name,enName,code,email,cellphone,note),...),...),...`)
        .filterBy('(detailId=="null"||detailId=="")'),
      join: {}
    }
  } else if (statisticsSource === 'DATA_LINK') {
    return {
      query: query.filterBy('refDataLinkId!="null"'),
      join: { join: `feeTypeId,feeTypeId,/v1/form/feeTypes` }
    }
  } else {
    return {
      query: query.filterBy('detailId!="null"'),
      join: { join: `feeTypeId,feeTypeId,/v1/form/feeTypes` }
    }
  }
}

export function getLedgerRelationList(params) {
  const { dataLinkId, ledgerConfigId, childrenId } = params
  const { query, join } = parseParams(params)
  return {
    type: key.GET_LEDGER_RELATION_LIST,
    payload: dataLinkV2.POST('/byLedgerConfigId/$ledgerConfigId/byDataLinkId/$dataLinkId', query.value(), {
      ledgerConfigId,
      dataLinkId,
      childrenId,
      ...join
    })
  }
}
export function getEntityByPlatformName(name) {
  const join = `platformId,platformId,/v2/datalink/platform`
  return {
    type: key.GET_ENTITY_BY_PLATFORM_NAME,
    payload: dataLink.POST(
      '/entity/getEntityByPlatformName/$name',
      {
        filterBy: `(type== ("TAXI")|| type== ("HOTEL") ||type== ("TRAIN") ||type== ("FLIGHT") ||type== ("ORDER"))`
      },
      { name, join }
    )
  }
}

export async function getLedgerRelationListAmount(params) {
  const { dataLinkId, ledgerConfigId, childrenId } = params
  const { query } = parseParams(params)
  return dataLinkV2.POST('/byLedgerConfigId/$ledgerConfigId/byDataLinkId/$dataLinkId/total', query.value(), {
    ledgerConfigId,
    dataLinkId,
    childrenId
  })
}

export async function getLedgerRelationEntityList(params) {
  const { ledgerConfigId, dataLinkId, start, count } = params
  const dataLinkQuery = new QuerySelect()
    .limit(start, count)
    .select(`...`)
    .value()
  const relationQuery = new QuerySelect()
    .filterBy('active==true')
    .filterBy('refDataLinkId!="null"')
    .value()
  try {
    const result = await dataLink.POST(
      '/byLedgerConfigId/$ledgerConfigId/byDataLinkId/$dataLinkId/bySourceDataLink',
      { relationQuery, dataLinkQuery, type: 'LIST' },
      {
        ledgerConfigId,
        dataLinkId
      }
    )
    return result.items
  } catch (e) {
    return e
  }
}

export function setPayeeComponentVisibility(payeeComponentData) {
  return {
    type: key.SET_PAYEE_COMPONENT_VISIBILITY,
    payload: payeeComponentData
  }
}

export function getDatalinkTemplateById(params) {
  return {
    type: key.GET_DATALLINK_TEMPLATE_BYID,
    payload: dataLink.GET(`/detail/$entityId/$type`, {
      ...params,
      join: 'data.dataLink.entityId,entity,/v2/datalink/entity'
    })
  }
}

export function getDatalinkTemplateByIds(params) {
  return {
    type: key.GET_DATALLINK_TEMPLATE_BYIDS,
    payload: dataLink.POST(`/detail/$type`, params, { join: 'data.dataLink.entityId,entity,/v2/datalink/entity' })
  }
}

function getStaffWithPageSize(data = {}) {
  let items = []
  let pages = 1
  let pageSize = 2999
  let params = {
    start: 0,
    count: pageSize,
    ...data
  }
  let getStaffs = () => {
    return dependence.POST('', params).then(resp => {
      items = items.concat(resp.items)
      if (resp.count !== items.length) {
        params.start = pages * pageSize
        pages++
        return getStaffs()
      }
      return { items }
    })
  }
  return getStaffs()
}

export function getDependenceList(params) {
  // if (!params.dependenceId) {
  //   return {
  //     type: key.GET_RECORDLINK,
  //     payload: Promise.resolve({ matchDefaultValue: false, leafItems: undefined, items: [] })
  //   }
  // }
  return {
    type: key.GET_RECORDLINK,
    payload: params.entity === 'organization.Staff' ? getStaffWithPageSize(params) : dependence.POST('', params)
  }
}

export function getExpressBudgetChildList(params) {
  return {
    type: key.GET_EXPRESS_BUDGET_CHILD_LIST,
    payload: params
  }
}

//获取借款单据审批中logs
export function getloanpackageDetailLogs(flowId, relateFlowId) {
  let params = {
    flowId,
    join: 'repaymentRecords.ownerId,ownerId,/v1/organization/staffs'
  }
  if (relateFlowId && getBoolVariation('hailiang_loan_permission')) {
    params.relateFlowId = relateFlowId
  }
  return {
    type: key.GET_LOAN_LOGS,
    payload: loanpackageAction.GET('/approve/byFlowId/$flowId', params)
  }
}

// 有借款能力的单据增加借款详情tab
export function getLoanPackageByFlowId(param, done) {
  let flowId = param.id
  let params = {
    flowId,
    join: 'repaymentRecords.casherId,staffs,/v1/organization/staffs',
    join$1: 'repaymentRecords.ownerId,ownerId,/v1/organization/staffs',
    join$2: 'repaymentRecords.shiftStaffId,shiftStaffId,/v1/organization/staffs',
    join$3: `flowSpecificationId,flowSpecificationId,/form/v1/specificationVersions`,
    join$4: `repaymentRecords.accountId,accountCompany,/pay/v1/accounts`,
    join$5: 'repaymentRecords.staffId,staffId,/v1/organization/staffs'
  }
  return {
    type: key.GET_LOAN_PACKAGE_BY_FLOWID,
    payload: loanpackageAction.GET('/byFlowId/$flowId', params),
    done
  }
}

export function getCalculateField(specificationId, submitterId, billSpecificationId, legalEntityId, others = {}) {
  // presetTemplateId有值，则是快速报销的自动计算，使用presetTemplateId，非快速报销使用billSpecificationId
  const { presetTemplateId } = others || {}
  return {
    type: key.GET_CALCULATE_FIELD,
    payload: calculateField.GET('/calculateFields/staffId/$submitterId/specId/$specificationId', {
      specificationId,
      submitterId,
      billSpecificationId: presetTemplateId || billSpecificationId,
      legalEntityId
    })
  }
}

// 是否补充收信息中的证件内容
export function checkSupplementIdentity(params) {
  return supplementIdentity.POST('', params).catch(err => {
    if (err.msg) {
      util.alert(err.msg)
    }
  })
}

// 是否补充收信息中的证件内容
export function checkYeeGoOrder(params) {
  return yeeGoOrder.POST('', params)
}

export function getAssignmentRuleById(entityIds) {
  return {
    type: key.GET_ASSIGNMENT_RULE_BY_ID,
    payload: fieldMapping.GET(`/getListByEntityIds/$[entityIds]`, { entityIds: entityIds })
  }
}

export function checkDimensionIsLeaf(ids) {
  return new Promise(async resolve => {
    try {
      const result = await dimensionItems.GET('/getLastNode/[id]', { id: ids })
      resolve(result.items)
    } catch (e) {
      resolve([])
    }
  })
}

/**
 * @params {bill/details/trip:errorName[]}
 */
export function setValidateError(param) {
  return {
    type: key.VALIDATE_ERROR,
    payload: param
  }
}

export function getSpecificationsById(ids) {
  return {
    type: key.GET_SPECIFICATION_BY_IDS,
    payload: specifications.GET('/[ids]', { ids })
  }
}

const DetailListJoin = {
  join: `form.details.feeTypeId,feeTypeId,/v1/form/feeTypes`,
  join$1: `form.details.feeTypeForm.attachments.fileId,fileId,/v1/attachment/attachments`
}

export function getExpenseLinkDetails({ id }) {
  const { selectEntity } = api.require('@common/join/bill.join')
  const query = new QuerySelect().select(`${selectEntity()}`).value()
  let params = {
    id,
    ...DetailListJoin
  }
  const request = apply
    .POST('/$id/details', query, params)
    .then(fetchAttachment)
    .then(fetchInvoice)
  return {
    type: key.GET_EXPENSE_LINK_DETAILS,
    payload: request
  }
}

const LinkListJoin = {
  join: `details.form.details.feeTypeId,feeTypeId,/v1/form/feeTypes`,
  join$1: `details.form.details.feeTypeForm.attachments.fileId,fileId,/v1/attachment/attachments`
}

export function getExpenseLinkList(ids) {
  const { selectEntity } = api.require('@common/join/bill.join')
  const query = new QuerySelect().select(`${selectEntity()}`).value()
  return requisitionInfo.POST('/details/byIds', { query, ids }, LinkListJoin)
}

export function saveExpenseLinkRecordDetails(expenseLinkDetails) {
  return {
    type: key.SAVE_EXPENSE_LINK_RECORD_DETAILS,
    expenseLinkDetails
  }
}

export function saveFeeTypeVisibleList(param) {
  return {
    type: key.SAVE_FEETYPE_VISIBLE_LIST,
    feeTypeVisibleObjForModify: param
  }
}

//获取单据可用的管理规则分组
export function getActiveCreditRulesWithGroup(params) {
  return {
    type: key.GET_ACTIVE_CREDIT_RULES_GROUP,
    payload: credit.GET('/rules/byFlowId/withGroup/$flowId', params)
  }
}

//获取单据上的批注
export function getBillNotes(params) {
  return {
    type: key.GET_BILL_NOTES,
    payload: credit.GET('/notes/byFlowId/$flowId', params)
  }
}

//在单据上添加批注
export function addBillNote(params) {
  return {
    type: key.POST_ADD_NOTE,
    payload: credit.POST('/notes', params),
    flowId: params.dataId
  }
}

// 是否进入批注模式
export function changeAddNoteMode(addNoteMode) {
  return {
    type: key.CHANGE_ADD_NOTE_MODE,
    addNoteMode
  }
}

//在单据历史版本中查询批注快照
export function searchNoteFormHistory(params) {
  return {
    type: key.SEARCH_NOTE_FORM_HISTORY,
    payload: credit.GET('/notes/byFlowVersionedId/$id', params)
  }
}

//更新用户浏览状态，用来判断是否正在浏览单据历史
export function changeStatusOfShowBillNotesInHistory(showBillNotesInHistory) {
  return {
    type: key.CHANGE_STATUS_OF_SHOWBILLNOTESINHISTORY,
    showBillNotesInHistory
  }
}

//获取个人信用积分
export function getMyCreditPoint(submitterId) {
  return {
    type: key.GET_MY_CREDIT_POINT,
    submitterId,
    payload: credit.GET('/stafflog/point/$submitterId', { submitterId })
  }
}

export function batchSaveWithNotes(data, isQuickExpense = false, qs) {
  if (isQuickExpense) {
    return QuickExpense.PUT('', data)
  }
  const p = data?.map(i => {
    const d = i.data
    if (d.stage === 'corpPayment') {
      d.stage = 'expense'
    }
    return i
  })
  return withNotes.PUT('', p, qs)
}

export function getFlowByIds(ids) {
  const join = {
    join: `form.specificationId,specificationId,/form/v1/specificationVersions`,
    join$1: `form.submitterId,submitterId,/v1/organization/staffs`
  }
  return flows.GET('/[ids]', { ids, ...join })
}

export function getMetaByIds(ids) {
  return meta.POST('/meta', ids)
}

export function getTravelManagementConfig(type) {
  return {
    type: key.GET_TREVELMANAGEMENT_CONFIG,
    payload: travelManagement.GET('/getTravelManagementConfig', type)
  }
}
export function checkReturnCity(params) {
  return {
    type: key.CHECK_RETURN_CITY,
    payload: travelManagement.POST('/city/checkReturnCity', { ...params })
  }
}
export function pullTravelOrder(params) {
  return {
    type: key.PULL_TRAVEL_ORDER,
    payload: travelManagement.PUT('/order/pullTravelOrderByStaff', { ...params })
  }
}
export function getTravelManagement(params = {}) {
  return {
    type: key.GET_CUSTOMTRAVELTYPE,
    payload: travelManagement.GET('/customTravelType', params)
  }
}

export function getLoanListByBill(params) {
  return loanpackageAction.POST('/byWriteOffLoans', { ...params })
}

export function saveWrittenOffSummary(writtenOffSummaryForMutiCurrency) {
  return {
    type: key.SAVE_WRITTENOFF_SUMMARY,
    writtenOffSummaryForMutiCurrency
  }
}

export function clearWrittenOffSummary() {
  return { type: key.CLEAR_WRITTENOFF_SUMMARY }
}

export function saveCrossWrittenOffSummary(writtenOffSummaryForCrossCurrency) {
  return {
    type: key.SAVE_CROSS_WRITTENOFF_SUMMARY,
    writtenOffSummaryForCrossCurrency
  }
}

export function clearCrossWrittenOffSummary() {
  return { type: key.CLEAR_CROSS_WRITTENOFF_SUMMARY }
}

export function checkBillBudgetOccupy(params) {
  return budgetList.POST('/$flowId/checkFreeze', params)
}

export function getTravelBackInfo(requisitionId, submitterId) {
  return {
    type: key.GET_TRAVEL_BACK_INFO,
    payload: dataLink.POST('/searchOrderIdsByRequistion', undefined, { requisitionId, submitterId })
  }
}

export function getTravelBackInfoV3(params) {
  return {
    type: key.GET_TRAVEL_BACK_INFO_V3,
    payload: travelManagement.POST('/selectTravels', params).then(data => {
      const availableStatus = ['已订购', '确认订单', '离店', '出票', '改签', '已完成']
      return data.items
        .filter(i => {
          let status = i.orderStatus
          if (i.orders?.length) {
            let dataLink = i.orders?.[0]?.dataLink
            const entityId = dataLink?.entityId?.parentId
            const key = `E_${entityId}_订单状态`
            status = dataLink?.form?.[key]
          }
          return availableStatus.includes(status)
        })
        .map(i => i.id)
    })
  }
}

export function getFieldsGroupData(params) {
  return v2flows.GET('/dimension', params)
}

export function updateDimentionCurrency(dimensionCurrencyInfo) {
  return {
    type: key.UPDATE_DIMENTION_CURRECY,
    dimensionCurrencyInfo
  }
}

export function payeeConfigCheck() {
  return {
    type: key.GET_PAYEE_CONFIG_CHECK,
    payload: configCheck.POST('')
  }
}
//根据收款账户id获取收款账户信息
export function getAccountById(id) {
  return payee.GET(`/[ids]`, { ids: [id] })
}
export function getDependenceDatalinkList(params) {
  return dependenceAction.POST('/searchForDataLink', params)
}
// 发票风险记录接口
export function recordInvoiceRiskWarning(id, params) {
  riskwarning.GET('/info/$flowId', { flowId: id })
  return singleInvoiceRiskWarning.POST('/[flowId]', { flowId: id, ...params })
}

export function getFlowRiskInfo(params) {
  return budgetList.POST('/budgetFlowReport', params, null, { hiddenLoading: true })
}

export function saveTripInfo(info) {
  return {
    type: key.SAVE_TRIPS_INFO,
    payload: info
  }
}

/**
 * @description 获取所有行程订单
 * @param {*} params
 * @returns
 */
// export function getAllOrders({ code, submitterId }) {
//   return {
//     type: key.GET_ALL_ORDERS,
//     payload: orders.POST(`/searchByRequistion`, {}, { code, submitterId })
//   }
// }
/**
 * @description 变更申请单
 * @param {*} params
 * @returns
 */
export function flowDoAction(params) {
  return flows.POST('/do/action/batch', params)
}

export function getAutoAssociateDetail(param, data) {
  return autoAssociate.POST('/$specificationId/$requisitionId', data, param)
}

//自动生成费用明细
export function getAutoGenerationFeeDetail(data) {
  return {
    type: key.AUTO_GENERATION_FEE_DETAIL,
    payload: autoGenerationFeeDetail.POST('', { ...data })
  }
}

/**
 * 自动生成费用明细规则
 */
export function getAutoGenerationFeeDetailRules(data) {
  return {
    type: key.AUTO_GENERATION_FEE_DETAIL_RULES,
    payload: autoGenerationFeeDetail.GET('/get/$specId', data)
  }
}

export function searchExtensionCenterConfig(params) {
  return {
    type: key.SEARCH_EXTENSION_CENTER_CONFIG,
    payload: extensionCenterConfigRes.POST(`/search`, params)
  }
}

//我的单据search接口
export function searchMyBillList(param = {}) {
  const start = get(param, 'start', 0)
  const stateFilter = get(param, 'stateFilter', '')
  let query = new QuerySelect()
    .filterBy(param.formType)
    .filterBy(param.filters)
    .filterBy(stateFilter)
    .limit(start, pageSize)
    .desc('updateTime')
  let pathP = { type: 'home5' }
  if (!window.isNewHome) {
    pathP = {}
    query = query.select(
      `id,
      state,
      formType,
      logs,
      ownerId(name),
      form(
        submitDate,
        title,
        submitterId(id,name,enName,avatar),
        expenseMoney,
        loanMoney,
        payMoney,
        requisitionMoney
      )`
    )
  }
  return flows.POST('/my', query.value(), pathP)
}

//获取历史版本详情
export function getBillHistoryVersionDetail(id, privilegeId) {
  const { joinParam, selectEntity } = api.require('@common/join/bill.join')
  let query = new QuerySelect().select(`${selectEntity()}`).value()
  let params = {
    id,
    ...joinParam(),
    privilegeId
  }
  return billVersions
    .POST('/version/$id', query, params)
    .then(fetchAttachment)
    .then(fetchInvoice)
}

//获取历史版本数据
export function getBillHistoryVersionList(flowId, privilegeId) {
  const query = new QuerySelect()
    .desc('updateTime')
    .select(
      `
         id,
         createTime,
         modifyUserId
         `
    )
    .value()
  const params = {
    id: flowId,
    privilegeId,
    join: 'modifyUserId,modifyUserId,/v1/organization/staffs?select=id,name'
  }

  return billVersions.POST('/flowId/$id', query, params)
}

export function getMicroOrderList() {
  return travelManagement.GET('/orderMicro/orderType')
}

// 获取小组件配置
export function getWidgetConfig() {
  return widgetConfig.GET('/', {})
}
/**
 * @description 费用变更
 * @param {*} flowid
 * @returns
 */
export function getFeeTypeChange(flowid) {
  return {
    type: key.GET_FEE_TYPE_CHANGE,
    payload: flows.GET('/checkDetail/$flowid', { flowid })
  }
}

/**
 *  校验“单据当前审批人与前序节点重复时无权审批”是否重复
 * @param id
 * @returns {Promise<any>}
 */
export function getApproverRepeatConfig(id) {
  return unAuthorizedApprove.GET('/$id', { id })
}

export function getDefaultPayee(params) {
  return {
    type: key.GET_DEFAULT_PAYEE,
    payload: payee.GET('/default', { ...params })
  }
}

export function getDelegateConfig(id) {
  return {
    type: key.GET_DELEGATE_CONFIG,
    payload: delegate.GET(`/getDelegateCustomConfig/delegateId/$id`, { id })
  }
}

export function setSubmitterData(submitterData) {
  return {
    type: key.SET_SUBMITTER_DATA,
    payload: submitterData
  }
}

/**
 * @description 获取当前企业是否不进行自动计算企业白名单
 * @returns
 */
export function getCalculateCorpIdWhiteList() {
  return {
    type: key.GET_CAlCULATE_CORP_ID_WHITELIST,
    payload: flows.GET('/check/optimizeCorpId')
  }
}

/**
 * @description 查询当前单据提交后所处的阶段
 * @param {*} id
 * @returns
 */
export function getFlowStage(id) {
  return flows.GET('/flowStage/$id', { id }, null, { skipCache: true })
}

export function getFlowLinkList(data = {}, done) {
  const { formData, calculateFlag, field, query } = data
  let param = {
    formData,
    calculateFlag,
    query
  }
  return {
    type: key.GET_FLOW_LINK_LIST,
    payload: flowLink.POST(field?.editable ? '/choose' : '/calculate', { ...param } || {}, {}),
    done
  }
}
//作废流程
export function nullifyFlow(id) {
  util.showLoading()
  const data = { name: 'freeflow.nullify', id, params: { nullifyType: 'nullifyRule' } }
  return {
    type: key.NULLIFY_FLOW,
    payload: flows.POST('/$id', data).then(data => {
      util.hideLoading()
      return data
    }),
    done(_, action) {
      if (!action.error) {
      }
    }
  }
}
//检查模板是否可显示作废按钮
export function checkNulllifyRule(params) {
  return {
    type: key.GET_NULLIFY_RULE_BYSPECID,
    payload: nullifyRule.GET('/$specId/effect/$state', params)
  }
}
//检查单据提交人是否可作废
export function checkNulllifyFlowId(flowId) {
  return {
    type: key.GET_NULLIFY_RULE_BY_FLOWID,
    payload: nullifyRule.GET('/$flowId/check', flowId)
  }
}

export function getRepaymentList() {
  let params = { state: 'APPROVE' }

  return {
    type: key.APPEND_REPAYMENT_LIST,
    payload: repay.GET('/mine/byState', params)
  }
}
export function getCalculateResult(form) {
  return {
    type: key.CALCULATE_RECORD_LINK,
    payload: calculateRecordLink.POST('', { form }, {})
  }
}

// 获取业务对象单据模板依赖配置字段
export function getPermissionFieldComponents(id) {
  return specifications.GET('/fieldsDependentReferences', { id })
}

// 根据单据id获取明细来源
export function getDetailFLowRelation(id) {
  return detailFLowRelation.GET('/$id', { id })
}

// 保存单据明细来源
export function setDetailFLowRelation(data) {
  return detailFLowRelation.POST('', null, data)
}

export function getInvoiceCorporation(ids) {
  return {
    type: key.GET_INVOICE_CORPORATION,
    payload: ids ? corporationList.GET('/[ids]', { ids }) : corporationList.GET('')
  }
}

//校验单据行程数据状态,是否闭环,是否订单确认
export function getFlowTravelResult(params = {}) {
  return orderConfirm.GET('/checkFlowTravel/$flowId', params)
}

export function saveHistoryCurrencyRates(historyCurrencyInfo) {
  return {
    type: key.SAVE_HISTORY_CURRECY_RATES,
    historyCurrencyInfo
  }
}

/**
 * 获取百旺预览地址
 * @param url:string 链接
 */
export const getBaiwangPreviewUrl = url => {
  return baiwangPreviewUrl.GET('/$MOBILE', { url })
}

/**
 * 获取还款时可用的付款账户
 */
export const getRepaymentAccountList = params => {
  return repaymentAccountList.GET('', params)
}

// 查询审批时弹窗风险提示/金额口径详情
export function getApproveDetail(params) {
  return backlogs.POST('/approveDisplay/approveDetail', params)
}
export function checkBatchApprove(params) {
  return backlogsApprove.POST('/checkBatchApprove', params)
}

/**
 * 根据单据code获取单据相关信息
 * @param params { code: string}
 * @returns {Promise<{backLogId, buttons, flow}>}
 */
export async function getFlowInfoByCode(params) {
  return v2flows.POST('/code/$code', { code: encodeURIComponent(params.code) })
}

/**
 * 根据单据模版id获取该模版需要隐藏的字段
 * @param specificationId
 * @returns {Promise<any>}
 */
export function getSpecificationHiddenFieldsById(specificationId) {
  return specifications.GET('/fieldsHideList', { id: specificationId })
}

/**
 * 根据多个单据模版id批量获取单据模版隐藏的字段
 * @param ids
 * @returns {Promise<any>}
 */
export async function batchGetSpecificationHiddenFieldsByIds(ids) {
  return specifications.POST('/batchFieldsHideList', { ids })
}

// 我的单据列表缓存
export function setMyBillsEntry(value) {
  return {
    type: key.GET_MY_BILLS_ENTRY,
    myBillsOpenEntry: value
  }
}

/**
 * 获取流程按钮
 * @param params {
 * "flowId":"xx", //单据id
 * "privilegeId":"", //小权限id
 * "deviceType":"DESKTOP", // 设备类型： 桌面端：DESKTOP； 移动端：MOBILE
 * "scene":"OWNER" //场景
 * 场景：制单人 (我的单据)：OWNER；审批人 (待办)：APPROVER；管理员 (单据管理)：ADMIN；
 * }
 * @returns {Promise<any>}
 */
export function getFlowButtons(params) {
  return flowButtonAction.POST('', params)
}

export function updateBillAppendInfo(params) {
  return {
    type: key.UPDATE_BILL_ADDEND_INFO,
    billAdditionalInfo: params
  }
}

export function checkTravelTime(param) {
  return travelManagement.POST('/checkPurchasedTravel', param)
}

export function updateBillCurrencyRates(data) {
  return {
    type: key.SET_CURRENCY_RATES,
    payload: data
  }
}

export function updateBillLastOperation(data = {}) {
  return {
    type: key.UPDATE_BILL_LAST_OPERATION,
    payload: {
      timestamp: Date.now(),
      ...data,
    }
  }
}


// 创建飞书群聊
export const createFeishuChat = data => {
  return feishuChat.POST('/chat', data)
}

// 获取飞书群聊地址
export const getFeishuChatUrl = flowId => {
  return feishuChat.GET('/chat', { flowId })
}

// 获取飞书群聊是否绑定
export const getFeishuChatStatus = flowId => {
  return feishuChat.GET('/status', { flowId })
}

// 获取部门可见性数据
export const getDepartmentVisibleData = params => {
  const { businessType, ...body } = params
  return v2flows.POST(`/departmentFieldSearch/${businessType}`, body)
}
export function orderAIPlanTravel(params = {}) {
  return aiLink.POST('/travelAgentChat', params)
}

export function getTextFormAudio(params = {}) {
  return aiLink.POST('/speechToText', params)
}
export const getMallTripConfig = (params = {}) => {
  return mallLink.GET('/order/getAllowTravelType', params)
}

export function getRecieptPrintUrl(id) {
  return payV3Action.GET(`/receipt/preview/$${id}`)
}
export const getAIAgent = params => {
  return aiAgent.GET('/detail', params)
}
