import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { cloneDeep, get, xor } from 'lodash'
import { isObject, isNull, isString } from '@ekuaibao/helpers'
import { showLoading, toast, hideLoading } from '../../../lib/util'
import { getPureDetailsValue, parseShowValue2SaveValue } from './formatUtil'
import { parseInvoiceForm } from '../../../lib/formatDetail'
import {
  standardValueMoney,
  standardMoneyByForeign,
  standardMoneyByForeignWithRule,
  convertConsumeStandardMoney
} from '../../../components/utils/fnInitalValue'
import { checkStaffDataRange } from '../../../components/utils/fnCheckStaffDataRange'
import { reportBillPagePaintDurationV2 } from '../../../lib/flowPerformanceStatistics'
import { getBoolVariation } from '../../../lib/featbit'

const blackCheckCorpList = ['AJU3H9IgqT6rxM']

function deleteUselessKey(formData: any = {}) {
  const form: any = {}
  Object.keys(formData).forEach((key: string) => {
    if (formData[key] !== undefined && !isNull(formData[key])) {
      form[key] = formData[key]
    }
  })
  return form
}
/**
 * @method async getAutoCalResultOnField
 * @param {baseDataPropertiesMap} Object
 * @param {bus} messageCenter
 * @param {specification} Object specification
 * @param {formData} Object fromValue
 * @param {sourceType} String  master_||detail_||trip_
 * @param {submitterId} String  submitterId
 * @return undefined
 */
export async function getAutoCalResultOnField(
  baseDataPropertiesMap: any,
  bus: any,
  specification: any,
  formData: any,
  sourceType: any,
  billContent?: any,
  updateLocalStorage?: any,
  template?: any,
  checkDefaultValue?: boolean,
  updateAutoCalResultAttribute?: boolean
) {
  let billData: any = parseShowValue2SaveValue(deleteUselessKey(formData), { isInvoicePure: true })
  switch (sourceType) {
    case 'master_':
      billData.source = 'master_'
      break
    case 'detail_':
      billContent.feeTypeForm = deleteUselessKey(billContent.feeTypeForm)
      if (billContent.feeTypeForm?.invoiceForm) {
        billContent.feeTypeForm.invoiceForm = parseInvoiceForm(billContent.feeTypeForm.invoiceForm, 'invoiceForm')
      }
      formData.details = [billContent]
      billData = parseShowValue2SaveValue(formData, { isInvoicePure: true })
      billData.specificationId = specification.id
      billData.source = 'detail_'
      break
    case 'trip_':
      billContent.tripTypeForm = deleteUselessKey(billContent.tripForm)
      formData.trips = [billContent]
      billData = parseShowValue2SaveValue(formData, { isInvoicePure: true })
      billData.specificationId = specification.id
      billData.source = 'trip_'
      break
    default:
      throw new TypeError('sourceType is invalid')
  }
  addArgumentsForCost(billData, specification)
  const params: any = getParams(formData, billData, sourceType, billContent, checkDefaultValue)
  if (window.__FLOW_START_LOAD_TIMESTAMP) {
    reportBillPagePaintDurationV2()
  }
  api?.logger?.info('自动计算发起请求', params)
  const results = await api
    .invokeService('@bill:get:calculationresult', params)
    .then((action: any) => {
      const { items } = action.payload
      if (!items) {
        return toast.error(action.payload.msg)
      }
      const hasError = items.filter((v: { errorMsg: any }) => v.errorMsg)
      if (hasError.length > 0) {
        throw { msg: hasError[0].errorMsg }
      }
      bus.emit('switch:message:change', { messageData: action.payload.items })
      return action.payload.items
    })
    .catch((error: any) => {
      return toast.error(error.msg || error.errorMessage)
    })

  if (!results) {
    return
  }
  api?.logger?.info('自动计算返回值', results)
  const { resultValue, attrValue } = await formatAutoCalculateResult(
    bus,
    results,
    baseDataPropertiesMap,
    template,
    checkDefaultValue,
    updateAutoCalResultAttribute,
    undefined,
    params,
  )
  if (Object.keys(attrValue).length) {
    bus.emit('update:calculate:template', attrValue)
    bus.emit('update:calculate:detail:template', attrValue)
  }
  if (Object.keys(resultValue).length) {
    // TODO: 自动计算埋点
    api?.logger?.info('自动计算赋值', {
      specificationId: specification?.id,
      specificationName: specification?.name,
      flowId: billData?.flowId,
      code: billData?.code,
      sceneName: sourceType === 'detail_' ? '在费用上自动计算赋值' : '在单据上自动计算赋值',
      feeTypeId: sourceType === 'detail_' ? billContent?.feeTypeId : undefined,
      feeTypeName: undefined,
      resultForm: resultValue
    })
    bus.setFieldsValue({ ...resultValue }).then((_: any) => {
      updateLocalStorage && updateLocalStorage()
    })
  }

  Object.keys(resultValue).forEach((key: string) => {
    // 报账金额 的自动计算
    if (key === 'reimbursementMoney') {
      bus.emit('reimbursementMoney:changed', resultValue.reimbursementMoney)
    }
  })
}

function getParams(formData: any, billData: any, sourceType: any, billContent: any, checkDefaultValue: boolean) {
  const params: any = { submitterId: formData?.submitterId?.id, isCalculateWrite: checkDefaultValue || false }
  if (sourceType === 'master_') {
    const { trips, ...others } = billData
    if (others.details) {
      delete others.details
    }
    params.billData = billData
    params.formData = others
  }
  if (sourceType === 'detail_') {
    params.formData = billContent
    params.billData = billData
  }
  if (sourceType === 'trip_') {
    params.billData = billData
    params.formData = billContent
  }
  return params
}
// 自动计算的结果中是否必填的结果去重并且以结果中的必填为主
export function calculateResultUniq(calculateFields: any[] = []) {
  if (!calculateFields.length) return calculateFields
  const resultValue = []
  const beforeValue = {}
  calculateFields.forEach(element => {
    const { onField, result, resultType, attribute, dataFrom } = element
    if (resultType === 'SPECIFICATION_ATTRIBUTE' && attribute === 'optional') {
      const key = `${dataFrom}_${onField}_${attribute}`
      const before = beforeValue[key]
      if (!before) {
        beforeValue[key] = element
      } else if (result === 'true' && before.result !== result) {
        beforeValue[key] = element
      }
    } else {
      resultValue.push(element)
    }
  })
  return resultValue.concat(Object.values(beforeValue))
}
async function formatAutoCalculateResult(
  bus: any,
  results: any,
  baseDataPropertiesMap: any,
  components: any,
  checkDefaultValue: boolean,
  updateAutoCalResultAttribute?: boolean,
  checkSubmit?: boolean,
  params?: any
) {
  const formValue = (await bus?.getValue()) || {} //  当前表单值
  const resultValue: any = {}
  const attrValue: any = {}
  let isCheckDetailCalAttr = false
  let checkDetails = [] as any
  calculateResultUniq(results).forEach((element: any) => {
    const { onField, result, error, dataFrom, loc, resultType, attribute, currencyType, numCode, amount, multiNumCode } = element
    let r = result === '' || result === 'null' ? undefined : result // 为了匹配老的自动计算接口奇奇怪怪的返回值
    const type = get(baseDataPropertiesMap[onField], 'dataType.type')
    const entity =
      get(baseDataPropertiesMap[onField], 'dataType.entity') ||
      get(baseDataPropertiesMap[onField], 'dataType.elemType.entity')
    const component = components && components.find((v: any) => v.name === onField)
    if (resultType === 'VALUE') {
      if (updateAutoCalResultAttribute) return
      if (dataFrom === 'details') {
        const { details = [] } = formValue
        const currentDetail = details[loc]
        const { specificationId, feeTypeForm } = currentDetail
        const feeTypeComponent = specificationId.components.find((v: any) => {
          return onField === v.field
        })
        if (!feeTypeComponent) {
          console.log('ediatble===>', onField)
          return
        }
        const defaultValue = get(feeTypeComponent, 'defaultValue.type')
        const originalValue = typeof feeTypeForm[onField] === 'object' ? feeTypeForm[onField]?.id : feeTypeForm[onField]
        feeTypeForm[onField] =
          feeTypeComponent.editable && defaultValue === 'formula'
            ? feeTypeForm[onField]
            : formatValue(type, entity, r, feeTypeComponent, feeTypeForm[onField], { currencyType, numCode, amount, multiNumCode })
        const changeOnFieldValue =
          typeof feeTypeForm[onField] === 'object' ? feeTypeForm[onField]?.id : feeTypeForm[onField]
        if (
          feeTypeComponent.isLinkageAssignment &&
          !feeTypeComponent.editable &&
          originalValue !== changeOnFieldValue
        ) {
          feeTypeForm[onField] = originalValue
          const label = feeTypeComponent?.label || feeTypeComponent?.labelCopy
          currentDetail.errorMsg = `${label}的值发生变更,请重新保存该条明细`
          currentDetail.needUpdate = true
        }
        if (onField === 'amount' && !(feeTypeComponent.editable && defaultValue === 'formula')) {
          checkDetails = details
        }
        resultValue.details = details
      } else if (
        component &&
        !checkValueChange(formValue[onField], r, type, components, onField, checkDefaultValue, entity, currencyType, {
          component,
          multiNumCode
        }) &&
        !error
      ) {
        const formatValueData = formatValue(type, entity, r, component, formValue[onField], {
          currencyType,
          numCode,
          amount,
          multiNumCode
        })
        // 人员单选/多选字段的角色依赖性与自动计算的结果取交集
        // if (getBoolVariation('mfrd-3800-staff-default-value-visible') && component.valueRangeFilter) {
        //   const staffRange = await api.dataLoader('@common.staffRangeByRule').reload({ ...params.billData, ruleId: component.valueRangeFilter })
        //   const staffRangeSet = new Set(staffRange?.data?.map(staff => staff.id))
        //   if (Array.isArray(formatValueData)) {
        //     resultValue[onField] = formatValueData.filter(item => staffRangeSet.has(item.id))
        //   } else if (staffRangeSet.has(formatValueData?.id)) {
        //     resultValue[onField] = formatValueData
        //   }
        // } else {
        //   resultValue[onField] = formatValueData
        // }
        resultValue[onField] = formatValueData
      }
    } else if (resultType === 'SPECIFICATION_ATTRIBUTE') {
      if (dataFrom === 'details') {
        const { details = [] } = formValue
        const currentDetail = details[loc]
        const detailComponents = get(currentDetail, 'specificationId.components', [])
        const feeTypeForm = currentDetail.feeTypeForm
        const cmpIndex = detailComponents.findIndex(el => el.field === onField)
        let cmp = detailComponents[cmpIndex]
        if (!cmp) {
          console.log('SPECIFICATION_ATTRIBUTE====>', onField)
          return
        }
        const type =
          get(baseDataPropertiesMap[onField], 'dataType.elemType.type') ||
          get(baseDataPropertiesMap[onField], 'dataType.type')
        if (attribute === 'optional') {
          if (!checkAttrChange(cmp[attribute], r, cmp, baseDataPropertiesMap[onField])) {
            const attrObj = formatAttrValue(attribute, r, detailComponents, baseDataPropertiesMap[onField])
            cmp = { ...cmp, ...attrObj }
            detailComponents[cmpIndex] = cmp // cmp 未曾用到Components里面，fix 解决草稿态中的费类系统计算未更新
          }
          let isOptional = false
          if (type === 'attachment') {
            isOptional =
              cmp[attribute] === false &&
              (!feeTypeForm[onField] || !(Array.isArray(feeTypeForm[onField]) && feeTypeForm[onField].length))
          } else {
            isOptional = cmp[attribute] === false && !feeTypeForm[onField]
          }
          if (isOptional) {
            isCheckDetailCalAttr = true
            currentDetail.errorMsg = {
              completed: i18n.get('该明细填写不完整'),
              message: i18n.get('该明细填写不完整'),
              isCheckCalAttr: true
            }
          }
        } else if (attribute === 'open') {
          // 分摊必填计算逻辑
          cmp.open = r === 'true'
        }
        attrValue.detailInfo = { details, isCheckDetailCalAttr }
      } else {
        if (attribute === 'optional') {
          if (component && !checkAttrChange(component[attribute], r, component, baseDataPropertiesMap[onField])) {
            const res = formatAttrValue(attribute, r, component, baseDataPropertiesMap[onField])
            attrValue[onField] = { ...attrValue[onField], ...res }
          }
        } else if (attribute === 'hide') {
          const res = formatSetValue('attributeHide', r, component, baseDataPropertiesMap[onField])
          attrValue[onField] = { ...attrValue[onField], ...res }
        } else if (attribute === 'open') {
          // 分摊必填计算逻辑
          attrValue[onField] = {
            ...attrValue[onField],
            open: r === 'true'
          }
        }
      }
    }
  })
  if (!checkSubmit && checkDetails?.length) {
    bus.invoke('detail:value:change', checkDetails)
    bus.setFieldsValue({ details: checkDetails })
  }
  return { resultValue, attrValue }
}

/**
 * 获取自动计算结果（整张单据包括费用明细）
 * @param billData
 */
export async function getAllAutoCalResultForBillDiff(billData: any) {
  const formData = cloneDeep(billData)
  formData.source = 'master_'
  if (formData.details) {
    formData.details = getPureDetailsValue(formData.details, true)
  }
  const billDataCloned = parseShowValue2SaveValue(deleteUselessKey(formData), { isInvoicePure: true })

  const params = getParams(formData, billDataCloned, 'master_', undefined, undefined)

  params.formData.details = params?.billData.details ?? []
  if (typeof params.formData.specificationId !== 'string' && !!params.formData.specificationId?.id) {
    params.formData.specificationId = params.formData.specificationId.id
  }
  console.log(params)
  const action = await api.invokeService('@bill:get:calculationresult', params)
  const { items } = action.payload
  if (!items) {
    return toast.error(action.payloadmsg)
  }
  return action.payload
}

function formatSetValue(attribute: any, result: any, component: any, field: any) {
  const type = get(field, 'dataType.elemType.type') || get(field, 'dataType.type')
  const res = {}
  let typeArr = [
    'text',
    'ref',
    'boolean',
    'number',
    'money',
    'date',
    'dateRange',
    'complex',
    'attachment',
    'corporateExpenseCard',
    'travel'
  ]
  if (typeArr.indexOf(type) > -1 && result !== undefined) {
    return { ...res, [attribute]: result === 'true' ? true : false }
  }
  return res
}

export function formatAttrValue(attribute: any, result: any, component: any, field: any) {
  const type = get(field, 'dataType.elemType.type') || get(field, 'dataType.type')
  const res = {}
  const typeArr = ['attachment', 'text', 'ref', 'money', 'complex', 'date', 'dateRange', 'invoice', 'travel'] // complex 行程规划必填支持公式
  if (typeArr.indexOf(type) > -1 && result && attribute) {
    return { ...res, [attribute]: result === 'true' ? false : true }
  }
  return res
}

export function formatValue(
  type: string,
  entity: string,
  value: string | undefined,
  component: any,
  formValue?: any,
  resultCurrencyInfo?: any
) {
  if (type === 'money') {
    if (isObject(formValue)) {
      const defaultRate = 1
      const { foreignScale, scale, rate = defaultRate, foreignNumCode } = formValue
      if (isString(value)) {
        if (resultCurrencyInfo?.multiNumCode) {
          return convertConsumeStandardMoney(value, resultCurrencyInfo)
        } else if (resultCurrencyInfo?.currencyType === 'FOREIGN') {
          const changeRate = formValue?.rate
            ? formValue?.rate
            : resultCurrencyInfo?.numCode?.length
            ? undefined
            : defaultRate
          if (get(component, 'defaultValue.customRuleId')) {
            return standardMoneyByForeignWithRule(
              value,
              resultCurrencyInfo?.numCode,
              changeRate,
              resultCurrencyInfo?.amount
            )
          } else {
            return standardMoneyByForeign(value, resultCurrencyInfo?.numCode, changeRate)
          }
        } else if (component?.defaultValue?.assignValueTo === 'ORIGINAL' && formValue.hasOwnProperty('foreign')) {
          // 自动计算结果优先赋值给原币，目标字段没有原币将会赋值给本位币
          // if (formValue.hasOwnProperty('foreign')) { // 目标字段有原币时赋值给目标的原币
          return standardMoneyByForeignWithRule(value, foreignNumCode, rate, undefined)
          // }else { // 否则不使用自动计算的结果
          //   return {...formValue}
          // }
        } else {
          if (formValue.hasOwnProperty('foreign')) {
            const currentScale = foreignScale !== undefined ? foreignScale : scale !== undefined ? scale : 2
            formValue.foreign = new Big(value).div(rate || defaultRate).toFixed(currentScale)
          }
          formValue.standard = value ?? ''
        }
      }
      return { ...formValue }
    }
    if (isString(value)) {
      const dimensionCurrency = api.getState()['@bill'].dimensionCurrencyInfo
      if (resultCurrencyInfo?.multiNumCode) {
        return convertConsumeStandardMoney(value, resultCurrencyInfo)
      } else if (resultCurrencyInfo?.currencyType === 'FOREIGN') {
        if (get(component, 'defaultValue.customRuleId')) {
          return standardMoneyByForeignWithRule(
            value,
            resultCurrencyInfo?.numCode,
            undefined,
            resultCurrencyInfo?.amount
          )
        } else {
          return standardMoneyByForeign(value, resultCurrencyInfo?.numCode)
        }
      }
      const val = standardValueMoney(value, dimensionCurrency?.currency)
      let reg = new RegExp(`^(-?)(([1-9]\\d*)|0)(\\.\\d{0,${val?.standardScale}})?$`)
      if (!reg.test(val?.standard)) {
        val.standard = new Big(val?.standard).toFixed(val?.standardScale)
      }
      return val
    }
  } else if (type === 'date' && value) {
    return parseFloat(value)
  } else if (type === 'dateRange' && isString(value)) {
    return JSON.parse(value)
  } else if ((type === 'switcher' || type === 'boolean') && value) {
    return value === 'true'
  } else if (entity === 'basedata.city') {
    if (!value || value === '[]') {
      return undefined
    }
    const vv = value.replace(/中国\/\s*/g, '')
    return !component.multiple && component.editable ? JSON.stringify([JSON.parse(vv)[0]]) : vv
  } else if (
    isString(value) &&
    isString(entity) &&
    (entity === 'organization.Staff' || entity === 'organization.Department' || entity.startsWith('basedata.Dimension'))
  ) {
    if (!!value && entity === 'organization.Staff') {
      const staff = JSON.parse(value)
      return checkStaffDataRange(component, staff)
    }
    return value ? JSON.parse(value) : undefined
  } else if (entity && entity.includes('datalink.DataLinkEntity') && isString(value)) {
    const dataLinkObj = JSON.parse(value)
    return dataLinkObj && dataLinkObj.id
  }
  return value
}

function checkValueChange(
  prevValue: any,
  nextValue: any,
  type: any,
  template: any,
  key: any,
  checkDefaultValue: boolean,
  entity?: string,
  currencyType?: string,
  config?: any
) {
  const field = template.find((v: any) => {
    return key === v.name
  })
  const defaultValue = get(field, 'defaultValue.type')
  if (field && field.editable === true && defaultValue === 'formula') {
    return !checkDefaultValue || Boolean(prevValue)
  }
  if (type === 'text' || type === 'textarea') {
    return prevValue === nextValue
  } else if (type === 'dateRange') {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return next.start === prev.start && next.end === prev.end
  } else if (type === 'boolean') {
    return String(prevValue) === String(nextValue)
  } else if (type === 'number' || type === 'money' || type === 'date') {
    let prev = isObject(prevValue) && type === 'money' ? parseFloat(prevValue.standard) : parseFloat(prevValue)
    let next = isObject(nextValue) && type === 'money' ? parseFloat(nextValue.standard) : parseFloat(nextValue)
    if (
      type === 'money' &&
      (currencyType === 'FOREIGN' ||
        !!config?.multiNumCode ||
        config?.component?.defaultValue?.assignValueTo === 'ORIGINAL')
    ) {
      prev =
        isObject(prevValue) && type === 'money'
          ? parseFloat(prevValue.foreign || prevValue.standard)
          : parseFloat(prevValue)
      next = isObject(nextValue) ? parseFloat(nextValue.foreign) : parseFloat(nextValue)
    }
    return (isNaN(prev) && isNaN(next)) || prev === next
  } else if (entity && entity === 'basedata.city') {
    const next = nextValue ? JSON.parse(nextValue) : []
    const prev = prevValue ? JSON.parse(prevValue) : []
    const nextIds = next.map((v: any) => v.key)
    const prevIds = prev.map((v: any) => v.key)
    return !xor(nextIds, prevIds).length
  } else if (type === 'list' && entity === 'organization.Staff') {
    const next = nextValue ? JSON.parse(nextValue) : []
    const prev = prevValue ? prevValue : []
    const nextIds = next.map((v: any) => v.id)
    const prevIds = prev
    return !xor(nextIds, prevIds).length
  } else if ((type === 'ref' && entity === 'organization.Staff') || entity === 'organization.Department') {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else if (type === 'ref' && isString(entity) && entity.startsWith('basedata.Dimension')) {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else if (entity && entity.includes('datalink.DataLinkEntity')) {
    const next = nextValue ? JSON.parse(nextValue) : {}
    const prev = isObject(prevValue) ? prevValue : {}
    return prev.id === next.id
  } else {
    return prevValue === nextValue
  }
}

export function checkAttrChange(prevValue: any, nextValue: any, component: any, field: any) {
  const type = get(field, 'dataType.elemType.type') || get(field, 'dataType.type')
  let typeArr = ['attachment', 'text', 'ref', 'money', 'complex', 'travel'] // complex 行程规划必填支持公式
  if (typeArr.indexOf(type) > -1 && prevValue !== undefined) {
    return String(!prevValue) === String(nextValue)
  }
  return prevValue === nextValue
}

export function addArgumentsForCost(billData: any, specification: any) {
  if (!specification?.configs) return
  const canPay = specification.configs.find((v: any) => v.ability === 'pay')
  const canWrittenOff = specification.configs.find((v: any) => v.ability === 'writtenOff')
  const canExpense = specification.configs.find((v: any) => v.ability === 'expense')
  const canRequisition = specification.configs.find(
    (v: any) => v.ability === 'requisition' && v.applyContentRule.applyContentRule === 'auto'
  )
  const canReceipt = specification.configs.find((v: any) => v.ability === 'receipt')
  //后台需要匹配口径
  if (canPay) {
    billData.payMoney = billData.payMoney || standardValueMoney(0)
  }
  if (canWrittenOff) {
    billData.writtenOffMoney = billData.writtenOffMoney || standardValueMoney(0)
  }
  if (canExpense) {
    billData.expenseMoney = billData.expenseMoney || standardValueMoney(0)
  }
  if (canRequisition) {
    billData.requisitionMoney = billData.requisitionMoney || standardValueMoney(0)
  }
  if (canReceipt) {
    billData.receiptMoney = billData.receiptMoney || standardValueMoney(0)
  }
}

export function getAutoCalParams4Import(billData: any, feeTypeForm: any, billSpecification: any) {
  billData.details = [feeTypeForm]
  const billValue: any = parseShowValue2SaveValue(billData, { isInvoicePure: true })
  billValue.specificationId = billSpecification.id
  billValue.source = 'detail_'
  addArgumentsForCost(billValue, billSpecification)
  return { submitterId: billValue.submitterId, formData: feeTypeForm, billData: billValue, isCalculateWrite: true }
}

export async function checkSubmitValue(formData: any, bus: any, specificationId: string) {
  if (!formData.details || !formData.submitterId) return
  const value = parseShowValue2SaveValue(formData, { isInvoicePure: true })
  value.specificationId = specificationId
  const params = {
    billData: { ...value, source: 'master_' },
    formData: value,
    submitterId: value.submitterId,
    isCalculateWrite: false
  }
  showLoading()
  const results = await api
    .invokeService('@bill:get:calculationresult', params)
    .then((action: any) => {
      const { items } = action.payload
      if (!items) {
        return toast.error(action.payloadmsg)
      }
      const hasError = items.filter((v: { errorMsg: any }) => v.errorMsg)
      if (hasError.length > 0) {
        throw { msg: hasError[0].errorMsg }
      }
      return action.payload.items
    })
    .catch((error: any) => {
      return toast.error(error.msg || error.errorMessage)
    })
  hideLoading()
  const checkSubmit = blackCheckCorpList.includes(Fetch.ekbCorpId)
  if (results) {
    const baseDataPropertiesMap = api.getState()['@common'].baseDataProperties.baseDataPropertiesMap
    const { resultValue, attrValue } = await formatAutoCalculateResult(
      bus,
      results,
      baseDataPropertiesMap,
      undefined,
      false,
      false,
      checkSubmit
    )
    return { resultValue, attrValue }
  }
  return undefined
}
