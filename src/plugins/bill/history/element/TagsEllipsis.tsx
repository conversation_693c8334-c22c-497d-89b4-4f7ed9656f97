/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-08-14 11:31:49
 * @Description  : tag 隐藏组件
 * 描述功能/使用范围/注意事项
 */
import React, { useRef, useState, useEffect } from 'react'
import { Popover } from '@hose/eui-mobile'

const TagsEllipsis = ({ items, itemSpacing = 4, containerWidth = 300 }) => {
  const containerRef = useRef(null)
  const [visibleItemsCount, setVisibleItemsCount] = useState(items.length > 1 ? items.length : 0)
  const isOnlyOne = items.length === 1 
  const allData = (items || []).join('、')
  
  const Tips = ({children})=> <Popover placement="topLeft" content={allData} mode="dark" trigger="click">
    { children}
  </Popover>

  useEffect(() => {
    if (containerRef.current) {
      let totalWidthUsed = 0
      let count = 0

      const children = containerRef.current.children
      for (let i = 0; i < children.length; i++) {
        const childWidth = children[i].getBoundingClientRect().width
        const spaceAfterItem = i < children.length - 1 ? itemSpacing : 0
        if (totalWidthUsed + childWidth + spaceAfterItem <= containerWidth) {
          totalWidthUsed += childWidth + spaceAfterItem
          count++
        } else {
          break
        }
      }

      setVisibleItemsCount(count)
    }
  }, [])

  return (
    <div ref={containerRef} className="dis-f tags-ellipsis"  style={{ maxWidth: `${containerWidth}px`, whiteSpace: 'nowrap', overflow: 'hidden' }}>
      {
        (isOnlyOne || !visibleItemsCount) && <Tips>
              <span  className={`item text-ellipsis`} >
                {(items && items[0]) || '<空>'}
              </span>
        </Tips> 
      }
      {!isOnlyOne && items.slice(0, visibleItemsCount).map((item:string, index:number) => (
        <span key={index} className={`item`} >
          {item || '<空>'}
        </span>
      ))}
      {items.length > visibleItemsCount && !!visibleItemsCount && <Tips><span className="item more">...</span></Tips>}
    </div>
  )
}

export default TagsEllipsis