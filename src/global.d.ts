declare const __DEV__: boolean
declare const i18n: any

declare var API_URL: string
declare var APPLICATION_VERSION: string
declare var UPLOAD_INVOICE_FILE_URL: string
declare var IS_STANDALONE: string

declare const IS_HSFK: boolean
declare const IS_SZJL: boolean
declare const IS_CMBC: boolean
declare const IS_NOEKB: boolean
declare const HUAWEI_LOGIN: string
declare const IS_SMG: boolean
declare const PLATFORMINFO_NAME: any
declare const IS_ICBC: boolean
declare const IS_BOCIM: boolean
declare const IS_ZJZY: boolean

interface Window {
  API_URL: string
  UPLOAD_INVOICE_FILE_URL: string
  APPLICATION_VERSION: string
  NOEKBSET: any
  masdk: any
  endpoint: any
  // 还款权限
  $_privilegeId: string
  __UPDATESCOPEVARIABLE__: Function
  PLATFORMINFO: any
  PREVIEW_DOMAIN: string
  FROM_SUISHOUJI: any
  IS_FESCO: boolean
  isZhongDian: boolean
  PromiseDefer: () => any
  alertMessage: (msg: any) => any
  __showMessage_error: (msg: any) => any
  getCorpStyle: () => any // 企业品牌化设置
  __WITHNOTE_ERROR: string // 随手记错误信息
  __HOME_PAGE_LOADED_TS?: number // 首页资源加载完开始渲染时间戳
  TranslateConfig?: {
    initTranslate: () => void
    setToLanguage: (lang: string) => void
    setDefaultLanguage: (lang: string) => void
    translate: {
      execute: () => void
    }
  },
  __FLOW_START_CLICK_DIMENSION_TIMESTAMP: number
  __FLOW_DETAILS_RECORD: number
  CODEPUSH_VERSION: string
  clearLocalStorage: () => void
  __BIRD_CONFIG?: any
  __BIRD?: any
}

interface Window {
  HSM: any
  i18n: any
  close: any
  __PLANTFORM__: string
  SUB__PLANTFORM__: string
  CURRENCY_SYMBOL: string
  IS_STANDALONE: string
  isMessage: boolean
  DEFAULTTITLE: string
  IS_LDAP: boolean
  isNewHome: boolean
  home5GuideIsShowLeftBtn: boolean
  showNavBarLeftBtn: boolean
  isIOS: boolean
  Intercom: Function
  PLATFORM_FEATURE: any
  payeeInfo: any
  IS_HSFK: boolean
  IS_SZJL: boolean
  IS_ZJZY: boolean
  IS_CMBC: boolean
  IS_NOEKB: boolean
  HUAWEI_LOGIN: string
  IS_SMG: boolean
  IS_ICBC: boolean
  IS_BOCIM: boolean
  IS_KINGOA: Boolean
  dtCorpId: string
  inGroupApp: boolean
  GROUP_URL: string
  APP_URL: string
  corpConfig: any
  isSharedApp: boolean
  __BEFORE_ENTRY_HISTORY_LENGTH: number // 进入应用的路由栈的长度
  ReactNativeWebView: any
  __HAB_BaseURL?: string // 组件加载基础路径
  __HAB_CorpId?: string // 企业id
  __HAB_AcessToken?: string // token
  __HAB_StaffId?: string // 当前登录人id
  __HAB_DepartmentId?: string //当前登录人部门id
  __HAB_DataType?: string // 当前单据类型
  __HAB_EkbCode?: string // ekbCode
  __HAB_FlowId?: string // 单据ID
  __HAB_FlowCode?: string // 单据Code
  __HAB_Components__?: Map<string, any> // 组件缓存
  // 环境中配置的变量，nginx服务下发
  ENV_CONFIG: IEnvConfig,
  // 获取环境变量的统一方法
  getEnvConfigValue: (configKey: keyof IEnvConfig, defaultValue: string) => string
}

interface IEnvConfig {
  // 微前端宿主id
  mfeHostId: string
  // 微前端服务地址
  mfeURL: string
  // 观测云上报地址
  ddTraceURL: string
  // idp授权服务域名
  idpDomain: string
  // featBit服务域名
  featBitDomain: string
  // featBitClientKey
  featBitClientKey: string
  // unity域名
  unityDomain: string
}

declare module '*.png' {
  const value: string
  export default value
}

declare module '*.jpeg' {
  const value: string
  export default value
}

declare module '*.svg' {
  const value: string
  export default value
}

declare module '*.svgx' {
  const value: string
  export default value
}

declare module '*.worker' {
  const value: Worker
  export default value
}

declare module '*.module.less' {
  const styles: any
  export default styles
}

declare module '*.less'
declare module 'rc-form/*'

declare interface StringAnyProps {
  [propName: string]: any
}

declare interface StringStringProps {
  [propName: string]: string
}

declare module '@antv/f2'

declare module '@hose/numeral'

declare module 'ekbc-query-builder'
