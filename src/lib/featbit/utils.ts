import { FeatBitKeys } from "./keys"
import { getBoolVariation, getVariation } from "./init"

/**
 * 【国际化】对接翻译插件，优化多种语言在不同环境下的展示名称
*/
export const enableTranslatePlugin = () => {
  return getBoolVariation(FeatBitKeys.integrateTranslatePlugin, false)
}

/**
 * 明细保存时增加费用金额<=发票总金额校验
 */
export const getIfCheckInvoicesMoney = () => {
  return getBoolVariation('mfrd-3469-invoices-total-less-than-amount-validate', false)
}

/**
 * 单据详情性能优化二期开关，针对接口优化
 * @returns
 */
export const enableBillDetailOptimization = () => {
  return getBoolVariation('mfrd-3728-bill-detail-optimization', false)
}

/**
 * 报销金额使用接口数据进行展示
 */
export const useApiExpenseMoney = () => {
  return getBoolVariation('cyxq-70702-use-api-expense-money', false)
}

/**
 * 部门组织架构可见性
 */
export const useDepartmentVisible = () => {
  return getBoolVariation('mfrd-3133-department-visible', false)
}

/**
 * 单据换新开关
 */
export const switchDocmentRenewEnable = () => {
  return getBoolVariation('mfrd-3152-renew-document-applet', false)
}

/**
 * 是否启用员工API切换
 * @link https://jira.hosecloud.com/browse/MFRD-4278
 */
export const enableStaffApiSwitch = () => {
  return getBoolVariation('mfrd-4278-staff-api-switch', false)
}

export const enableOtherInvoiceByDimension = () => {
  return getBoolVariation('fird-4844-invoice-by-dimension', false)
}
/**
 * 【自定义档案选择优化】https://jira.hosecloud.com/browse/MFRD-3465
 */
export const enableRecordSelectOptimization = () => {
  return getBoolVariation('mfrd-3465-record-select-optimization', false)
}

/** 【单据焕新】【二期】【查看态】「我的单据」去除已完成操作
 * @link https://jira.hosecloud.com/browse/MFRD-3156
 */
export const enableHidingFinishedBills = () => {
  return getBoolVariation('mfrd-3156-get-rid-of-finished-bills', false)
}

/**
 * MFRD-3463【移动端编辑态】新建单据/切换单据模板优化
 * @link https://jira.hosecloud.com/browse/MFRD-3463
*/
export const enableNewBillMode = () => {
  return getBoolVariation('mfrd-3463', false)
}

export const enableFlowHiddenFields = () => {
  const value = getVariation('mfrd-3670-flow-hidden-field', 'UNOPEN')
  const hasDetail = ['READORWRITE', 'READORWRITE_DETAIL'].includes(value)

  return {
    value,
    handleDetail: !!hasDetail
  }
}